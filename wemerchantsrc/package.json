{"name": "sudian-nongmao", "version": "1.0.0", "description": "sudian-nongmao", "author": "ya<PERSON><PERSON><PERSON> <<EMAIL>>", "private": true, "authors": "Sn F2E Team", "copyright": "https://sdnm.freshfirst.cn\n<AUTHOR> F2E Team\n@version {{VERSION}}\n(c) 2017", "scripts": {"dev": "node build/dev-server.js", "start": "node build/dev-server.js", "build": "node build/build.js", "build:dll": "webpack --config build/webpack.dll.conf.js", "unit": "cross-env BABEL_ENV=test karma start test/unit/karma.conf.js --single-run", "e2e": "node test/e2e/runner.js", "test": "npm run unit && npm run e2e", "lint": "eslint --ext .js,.vue src test/unit/specs test/e2e/specs"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^1.0.0", "@xkeshi/vue-barcode": "^1.0.0", "axios": "^0.19.0", "babel-polyfill": "^6.23.0", "element-ui": "^2.15.14", "highcharts": "^6.0.4", "iscroll": "^5.2.0", "lrz": "^4.9.40", "qs": "^6.11.2", "select2": "^4.1.0-rc.0", "v-select2-component": "^0.4.7", "vconsole": "^3.15.1", "vue": "^2.5.17", "vue-flatpickr-component": "^8.1.7", "vue-highcharts": "0.0.10", "vue-resource": "^1.3.4", "vue-router": "^2.7.0", "vue-select": "^3.20.4", "vue-wechat-title": "^2.0.4", "vuex": "^2.3.1", "weixin-js-sdk": "^1.4.0-test"}, "devDependencies": {"assets-webpack-plugin": "^3.5.1", "autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^7.1.1", "babel-plugin-istanbul": "^4.1.1", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chai": "^3.5.0", "chalk": "^2.0.1", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "cross-env": "^5.0.1", "cross-spawn": "^5.0.1", "css-loader": "^0.28.0", "cssnano": "^3.10.0", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^3.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.19.1", "inject-loader": "^3.0.0", "karma": "^1.4.1", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-phantomjs-shim": "^1.4.0", "karma-sinon-chai": "^1.3.1", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.31", "karma-webpack": "^2.0.2", "less": "^2.7.2", "less-loader": "^4.0.4", "mocha": "^3.2.0", "nightwatch": "^0.9.12", "opn": "^5.1.0", "optimize-css-assets-webpack-plugin": "^2.0.0", "ora": "^1.2.0", "phantomjs-prebuilt": "^2.1.16", "rimraf": "^2.6.0", "selenium-server": "^3.0.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "url-loader": "^0.5.8", "vconsole-webpack-plugin": "^1.8.0", "vue-loader": "^13.0.4", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.4.2", "webpack": "^2.6.1", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}