// see http://vuejs-templates.github.io/webpack for documentation.
var path = require('path');
const pkg = require('../package.json');
const local = require('./local');
const banner = pkg.copyright.replace('{{VERSION}}', pkg.version);

const vConsolePlugin = require('vconsole-webpack-plugin');
module.exports = {
	build: {
		env: require('./prod.env'),
		index: path.resolve(__dirname, '../../resources/views/wemerchant.blade.php'),
		assetsRoot: path.resolve(__dirname, '../../public'),
		assetsSubDirectory: 'wemerchant',
		assetsPublicPath: '/',
		productionSourceMap: !true,
		banner,
		// Gzip off by default as many popular static hosts such as
		// Surge or Netlify already gzip all static assets for you.
		// Before setting to `true`, make sure to:
		// npm install --save-dev compression-webpack-plugin
		productionGzip: false,
		productionGzipExtensions: ['js', 'css'],
		// Run the build command with an extra argument to
		// View the bundle analyzer report after build finishes:
		// `npm run build --report`
		// Set to `true` or `false` to always turn it on or off
		bundleAnalyzerReport: process.env.npm_config_report
	},
	configureWebpack: config => {
		//生产环境去掉vconsole调试器
		let envType = process.env.NODE_ENV != 'production'
		let pluginsDev = [
			new vConsolePlugin({
				filter: [],
				enable: envType
			})
		]

		config.plugins = [...config.plugins, ...pluginsDev]
	},
	dev: {
		env: require('./dev.env'),
		port: local.devPort,
		// autoOpenBrowser: true,
		assetsSubDirectory: 'static',
		assetsPublicPath: '/',
		proxyTable: {
			'/wemerchantdll': {
				target: `${local.url}:${local.phpPort}/`,
				changeOrigin: true,
			},
			'/lib': {
				target: `${local.url}:${local.phpPort}/`,
				changeOrigin: true,
			},
			'/images': {
				target: `${local.url}:${local.phpPort}/`,
				changeOrigin: true,
			},
			'/api': {
				target: `${local.url}:${local.phpPort}/`,
				changeOrigin: true,
			},
			'/h5': {
				target: `${local.url}:${local.phpPort}/`,
				changeOrigin: true,
			},
			'/screen': {
				target: `${local.url}:${local.phpPort}/`,
				changeOrigin: true,
			},
			'/storage': {
				target: `${local.url}:${local.phpPort}/`,
				changeOrigin: true,
			},


		},
		// CSS Sourcemaps off by default because relative paths are "buggy"
		// with this option, according to the CSS-Loader README
		// (https://github.com/webpack/css-loader#sourcemaps)
		// In our experience, they generally work as expected,
		// just be aware of this issue when enabling this option.
		cssSourceMap: false
	}
};
