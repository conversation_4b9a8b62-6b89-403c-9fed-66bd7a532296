# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/runtime@^7.17.2":
  "integrity" "sha512-0CX6F+BI2s9dkUqr08KFrAIZgNFj75rdBU/DjCyYLIaV/quFjkk6T+EJ2LkZHyZTbEV4L5p97mNkUsHl2wLFAw=="
  "resolved" "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.23.9.tgz"
  "version" "7.23.9"
  dependencies:
    "regenerator-runtime" "^0.14.0"

"@chenfengyuan/vue-qrcode@^1.0.0":
  "integrity" "sha512-Ph+W4ltPIyn9YjjITuLewhw8MeEXxJ1L5z6ZM+trmVWcKLfoIDi3yLHDPFKFjesWqsdH8irIX67PCBZ80TY8kw=="
  "resolved" "https://registry.npmjs.org/@chenfengyuan/vue-qrcode/-/vue-qrcode-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "qrcode" "^1.3.0"

"@sindresorhus/is@^0.7.0":
  "integrity" "sha512-ONhaKPIufzzrlNbqtWFFd+jlnemX6lJAgq9ZeiZtS7I1PIf/la7CW4m83rTXRnVnsMbW2k56pGYu7AUFJD9Pow=="
  "resolved" "https://registry.npmjs.org/@sindresorhus/is/-/is-0.7.0.tgz"
  "version" "0.7.0"

"@types/node@^8.0.7":
  "integrity" "sha512-RRSjdwz63kS4u7edIwJUn8NqKLLQ6LyqF/X4+4jp38MBT3Vwetewi2N4dgJEshLbDwNgOJXNYoOwzVZUSSLhkQ=="
  "resolved" "https://registry.npmjs.org/@types/node/-/node-8.10.40.tgz"
  "version" "8.10.40"

"@xkeshi/vue-barcode@^1.0.0":
  "integrity" "sha512-Zgwg7n314dHs5K7OBtdXzQXLr4UC3kZmoFuggxk0tNNKEekgUKAsvomukRtrRTK3FfohsYKkSgxBa3uRsiEdig=="
  "resolved" "https://registry.npmjs.org/@xkeshi/vue-barcode/-/vue-barcode-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "jsbarcode" "^3.9.0"

"abbrev@1", "abbrev@1.0.x":
  "integrity" "sha1-kbR5JYinc4wl813W9jdSovh3YTU="
  "resolved" "https://registry.npmjs.org/abbrev/-/abbrev-1.0.9.tgz"
  "version" "1.0.9"

"accepts@~1.3.5":
  "integrity" "sha1-63d99gEXI6OxTopywIBcjoZ0a9I="
  "resolved" "https://registry.npmjs.org/accepts/-/accepts-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "mime-types" "~2.1.18"
    "negotiator" "0.6.1"

"accepts@1.3.3":
  "integrity" "sha1-w8p0NJOGSMPg2cHjKN1otiLChMo="
  "resolved" "https://registry.npmjs.org/accepts/-/accepts-1.3.3.tgz"
  "version" "1.3.3"
  dependencies:
    "mime-types" "~2.1.11"
    "negotiator" "0.6.1"

"acorn-dynamic-import@^2.0.0":
  "integrity" "sha1-x1K9IQvvZ5UBtsbLf8hPj0cVjMQ="
  "resolved" "https://registry.npmjs.org/acorn-dynamic-import/-/acorn-dynamic-import-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "acorn" "^4.0.3"

"acorn-jsx@^3.0.0":
  "integrity" "sha1-r9+UiPsezvyDSPb7IvRk4ypYs2s="
  "resolved" "http://registry.npmjs.org/acorn-jsx/-/acorn-jsx-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "acorn" "^3.0.4"

"acorn@^3.0.4":
  "integrity" "sha1-ReN/s56No/JbruP/U2niu18iAXo="
  "resolved" "http://registry.npmjs.org/acorn/-/acorn-3.3.0.tgz"
  "version" "3.3.0"

"acorn@^4.0.3":
  "integrity" "sha1-EFSVrlNh1pe9GVyCUZLhrX8lN4c="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-4.0.13.tgz"
  "version" "4.0.13"

"acorn@^5.0.0", "acorn@^5.3.0", "acorn@^5.5.0":
  "integrity" "sha512-T/zvzYRfbVojPWahDsE5evJdHb3oJoQfFbsrKM7w5Zcs++Tr257tia3BmMP8XYVjp1S9RZXQMh7gao96BlqZOw=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-5.7.3.tgz"
  "version" "5.7.3"

"after@0.8.2":
  "integrity" "sha1-/ts5T58OAqqXaOcCvaI7UF+ufh8="
  "resolved" "https://registry.npmjs.org/after/-/after-0.8.2.tgz"
  "version" "0.8.2"

"agent-base@2":
  "integrity" "sha1-1t4Q1a9hMtW9aSQn1G/FOFOQlMc="
  "resolved" "https://registry.npmjs.org/agent-base/-/agent-base-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "extend" "~3.0.0"
    "semver" "~5.0.1"

"ajv-keywords@^1.0.0", "ajv-keywords@^1.1.1":
  "integrity" "sha1-MU3QpLM2j609/NxU7eYXG4htrzw="
  "resolved" "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.5.1.tgz"
  "version" "1.5.1"

"ajv@^4.7.0", "ajv@^4.9.1", "ajv@>=4.10.0":
  "integrity" "sha1-gv+wKynmYq5TvcIK8VlHcGc5xTY="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-4.11.8.tgz"
  "version" "4.11.8"
  dependencies:
    "co" "^4.6.0"
    "json-stable-stringify" "^1.0.1"

"ajv@^5.0.0":
  "integrity" "sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-5.5.2.tgz"
  "version" "5.5.2"
  dependencies:
    "co" "^4.6.0"
    "fast-deep-equal" "^1.0.0"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.3.0"

"ajv@^6.12.3":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"align-text@^0.1.1", "align-text@^0.1.3":
  "integrity" "sha1-DNkKVhCT810KmSVsIrcGlDP60Rc="
  "resolved" "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"
    "longest" "^1.0.1"
    "repeat-string" "^1.5.2"

"alphanum-sort@^1.0.1", "alphanum-sort@^1.0.2":
  "integrity" "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM="
  "resolved" "https://registry.npmjs.org/alphanum-sort/-/alphanum-sort-1.0.2.tgz"
  "version" "1.0.2"

"amdefine@>=0.0.4":
  "integrity" "sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU="
  "resolved" "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz"
  "version" "1.0.1"

"ansi-escapes@^1.1.0":
  "integrity" "sha1-06ioOzGapneTZisT52HHkRQiMG4="
  "resolved" "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-1.4.0.tgz"
  "version" "1.4.0"

"ansi-html@0.0.7":
  "integrity" "sha1-gTWEAhliqenm/QOflA0S9WynhZ4="
  "resolved" "https://registry.npmjs.org/ansi-html/-/ansi-html-0.0.7.tgz"
  "version" "0.0.7"

"ansi-regex@^2.0.0":
  "integrity" "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@^3.0.0":
  "integrity" "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz"
  "version" "3.0.0"

"ansi-styles@^2.2.1":
  "integrity" "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz"
  "version" "2.2.1"

"ansi-styles@^3.2.1":
  "integrity" "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"anymatch@^1.3.0":
  "integrity" "sha512-0XNayC8lTHQ2OI8aljNCN3sSx6hsr/1+rlcDAotXJR7C1oZZHCNsfpbKwMjRA3Uqb5tF1Rae2oloTr4xpq+WjA=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "micromatch" "^2.1.5"
    "normalize-path" "^2.0.0"

"anymatch@^2.0.0":
  "integrity" "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromatch" "^3.1.4"
    "normalize-path" "^2.1.1"

"aproba@^1.0.3":
  "integrity" "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw=="
  "resolved" "https://registry.npmjs.org/aproba/-/aproba-1.2.0.tgz"
  "version" "1.2.0"

"aproba@^1.1.1":
  "integrity" "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw=="
  "resolved" "https://registry.npmjs.org/aproba/-/aproba-1.2.0.tgz"
  "version" "1.2.0"

"are-we-there-yet@~1.1.2":
  "integrity" "sha512-5hYdAkZlcG8tOLujVDTgCT+uPX0VnpAH28gWsLfzpXYm7wP6mp5Q/gYyR7YQ0cKVJcXJnl3j2kpBan13PtQf6w=="
  "resolved" "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "delegates" "^1.0.0"
    "readable-stream" "^2.0.6"

"argparse@^1.0.7":
  "integrity" "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"arr-diff@^2.0.0":
  "integrity" "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8="
  "resolved" "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "arr-flatten" "^1.0.1"

"arr-diff@^4.0.0":
  "integrity" "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="
  "resolved" "https://registry.npmjs.org/arr-diff/-/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.0.1", "arr-flatten@^1.1.0":
  "integrity" "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg=="
  "resolved" "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="
  "resolved" "https://registry.npmjs.org/arr-union/-/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-find-index@^1.0.1":
  "integrity" "sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E="
  "resolved" "https://registry.npmjs.org/array-find-index/-/array-find-index-1.0.2.tgz"
  "version" "1.0.2"

"array-flatten@1.1.1":
  "integrity" "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="
  "resolved" "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-slice@^0.2.3":
  "integrity" "sha1-3Tz7gO15c6dRF82sabC5nshhhvU="
  "resolved" "https://registry.npmjs.org/array-slice/-/array-slice-0.2.3.tgz"
  "version" "0.2.3"

"array-union@^1.0.1":
  "integrity" "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk="
  "resolved" "https://registry.npmjs.org/array-union/-/array-union-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "array-uniq" "^1.0.1"

"array-uniq@^1.0.1":
  "integrity" "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY="
  "resolved" "https://registry.npmjs.org/array-uniq/-/array-uniq-1.0.3.tgz"
  "version" "1.0.3"

"array-unique@^0.2.1":
  "integrity" "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM="
  "resolved" "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz"
  "version" "0.2.1"

"array-unique@^0.3.2":
  "integrity" "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="
  "resolved" "https://registry.npmjs.org/array-unique/-/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"arraybuffer.slice@0.0.6":
  "integrity" "sha1-8zshWfBTKj8xB6JywMz70a0peco="
  "resolved" "https://registry.npmjs.org/arraybuffer.slice/-/arraybuffer.slice-0.0.6.tgz"
  "version" "0.0.6"

"arrify@^1.0.1":
  "integrity" "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0="
  "resolved" "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz"
  "version" "1.0.1"

"asap@~2.0.3":
  "integrity" "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY="
  "resolved" "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz"
  "version" "2.0.6"

"asn1.js@^4.0.0":
  "integrity" "sha512-p32cOF5q0Zqs9uBiONKYLm6BClCoBCM5O9JfeUSlnQLBTxYdTK+pW+nXflm8UkKd2UYlEbYz5qEi0JuZR9ckSw=="
  "resolved" "https://registry.npmjs.org/asn1.js/-/asn1.js-4.10.1.tgz"
  "version" "4.10.1"
  dependencies:
    "bn.js" "^4.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"asn1@~0.2.3":
  "integrity" "sha512-jxwzQpLQjSmWXgwaCZE9Nz+glAG01yF1QnWgbhGwHI5A6FRIEY6IVqtHhIepHqI7/kyEyQEagBC5mBEFlIYvdg=="
  "resolved" "https://registry.npmjs.org/asn1/-/asn1-0.2.4.tgz"
  "version" "0.2.4"
  dependencies:
    "safer-buffer" "~2.1.0"

"assert-plus@^0.2.0":
  "integrity" "sha1-104bh+ev/A24qttwIfP+SBAasjQ="
  "resolved" "https://registry.npmjs.org/assert-plus/-/assert-plus-0.2.0.tgz"
  "version" "0.2.0"

"assert-plus@^1.0.0", "assert-plus@1.0.0":
  "integrity" "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="
  "resolved" "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz"
  "version" "1.0.0"

"assert@^1.1.1":
  "integrity" "sha1-mZEtWRg2tab1s0XA8H7vwI/GXZE="
  "resolved" "https://registry.npmjs.org/assert/-/assert-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "util" "0.10.3"

"assertion-error@^1.0.1":
  "integrity" "sha512-jgsaNduz+ndvGyFt3uSuWqvy4lCnIJiovtouQN5JZHOKCS2QuhEdbcQHFhVksz2N2U9hXJo8odG7ETyWlEeuDw=="
  "resolved" "https://registry.npmjs.org/assertion-error/-/assertion-error-1.1.0.tgz"
  "version" "1.1.0"

"assertion-error@1.0.0":
  "integrity" "sha1-x/hUOP3UZrx8oWq5DIFRN5el0js="
  "resolved" "https://registry.npmjs.org/assertion-error/-/assertion-error-1.0.0.tgz"
  "version" "1.0.0"

"assets-webpack-plugin@^3.5.1":
  "integrity" "sha512-yxo4MlSb++B88qQFE27Wf56ykGaDHZeKcSbrstSFOOwOxv33gWXtM49+yfYPSErlXPAMT5lVy3YPIhWlIFjYQw=="
  "resolved" "https://registry.npmjs.org/assets-webpack-plugin/-/assets-webpack-plugin-3.9.7.tgz"
  "version" "3.9.7"
  dependencies:
    "camelcase" "^5.0.0"
    "escape-string-regexp" "^1.0.3"
    "lodash.assign" "^4.2.0"
    "lodash.merge" "^4.6.1"
    "mkdirp" "^0.5.1"

"assign-symbols@^1.0.0":
  "integrity" "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="
  "resolved" "https://registry.npmjs.org/assign-symbols/-/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"ast-types@0.x.x":
  "integrity" "sha512-8c83xDLJM/dLDyXNLiR6afRRm4dPKN6KAnKqytRK3DBJul9lA+atxdQkNDkSVPdTqea5HiRq3lnnOIZ0MBpvdg=="
  "resolved" "https://registry.npmjs.org/ast-types/-/ast-types-0.12.2.tgz"
  "version" "0.12.2"

"async-each@^1.0.0", "async-each@^1.0.1":
  "integrity" "sha1-GdOGodntxufByF04iu28xW0zYC0="
  "resolved" "https://registry.npmjs.org/async-each/-/async-each-1.0.1.tgz"
  "version" "1.0.1"

"async-limiter@~1.0.0":
  "integrity" "sha512-jp/uFnooOiO+L211eZOoSyzpOITMXx1rBITauYykG3BRYPu8h0UcxsPNB04RR5vo4Tyz3+ay17tR6JVf9qzYWg=="
  "resolved" "https://registry.npmjs.org/async-limiter/-/async-limiter-1.0.0.tgz"
  "version" "1.0.0"

"async-validator@~1.8.1":
  "integrity" "sha512-tXBM+1m056MAX0E8TL2iCjg8WvSyXu0Zc8LNtYqrVeyoL3+esHRZ4SieE9fKQyyU09uONjnMEjrNBMqT0mbvmA=="
  "resolved" "https://registry.npmmirror.com/async-validator/-/async-validator-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "babel-runtime" "6.x"

"async@^2.0.0", "async@^2.1.2":
  "integrity" "sha512-H1qVYh1MYhEEFLsP97cVKqCGo7KfCyTt6uEWqsTBr9SO84oK9Uwbyd/yCW+6rKJLHksBNUVWZDAjfS+Ccx0Bbg=="
  "resolved" "https://registry.npmjs.org/async/-/async-2.6.2.tgz"
  "version" "2.6.2"
  dependencies:
    "lodash" "^4.17.11"

"async@1.x":
  "integrity" "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo="
  "resolved" "http://registry.npmjs.org/async/-/async-1.5.2.tgz"
  "version" "1.5.2"

"asynckit@^0.4.0":
  "integrity" "sha1-x57Zf380y48robyXkLzDZkdLS3k="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@^2.1.1":
  "integrity" "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg=="
  "resolved" "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@^6.3.1":
  "integrity" "sha1-Hb0cg1ZY41zj+ZhAmdsAWFx4IBQ="
  "resolved" "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.7.7.tgz"
  "version" "6.7.7"
  dependencies:
    "browserslist" "^1.7.6"
    "caniuse-db" "^1.0.30000634"
    "normalize-range" "^0.1.2"
    "num2fraction" "^1.2.2"
    "postcss" "^5.2.16"
    "postcss-value-parser" "^3.2.3"

"autoprefixer@^7.1.2":
  "integrity" "sha512-Iq8TRIB+/9eQ8rbGhcP7ct5cYb/3qjNYAR2SnzLCEcwF6rvVOax8+9+fccgXk4bEhQGjOZd5TLhsksmAdsbGqQ=="
  "resolved" "http://registry.npmjs.org/autoprefixer/-/autoprefixer-7.2.6.tgz"
  "version" "7.2.6"
  dependencies:
    "browserslist" "^2.11.3"
    "caniuse-lite" "^1.0.30000805"
    "normalize-range" "^0.1.2"
    "num2fraction" "^1.2.2"
    "postcss" "^6.0.17"
    "postcss-value-parser" "^3.2.3"

"aws-sign2@~0.6.0":
  "integrity" "sha1-FDQt0428yU0OW4fXY81jYSwOeU8="
  "resolved" "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.6.0.tgz"
  "version" "0.6.0"

"aws-sign2@~0.7.0":
  "integrity" "sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA=="
  "resolved" "https://registry.npmmirror.com/aws-sign2/-/aws-sign2-0.7.0.tgz"
  "version" "0.7.0"

"aws4@^1.2.1", "aws4@^1.8.0":
  "integrity" "sha512-ReZxvNHIOv88FlT7rxcXIIC0fPt4KZqZbOlivyWtXLt8ESx84zd3kMC6iK5jVeS2qt+g7ftS7ye4fi06X5rtRQ=="
  "resolved" "https://registry.npmjs.org/aws4/-/aws4-1.8.0.tgz"
  "version" "1.8.0"

"axios@^0.19.0":
  "integrity" "sha1-jgm/89kSLhM/e4EByPvdAO09Krg="
  "resolved" "https://registry.npm.taobao.org/axios/download/axios-0.19.0.tgz"
  "version" "0.19.0"
  dependencies:
    "follow-redirects" "1.5.10"
    "is-buffer" "^2.0.2"

"babel-code-frame@^6.16.0", "babel-code-frame@^6.22.0", "babel-code-frame@^6.26.0":
  "integrity" "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s="
  "resolved" "https://registry.npmjs.org/babel-code-frame/-/babel-code-frame-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "chalk" "^1.1.3"
    "esutils" "^2.0.2"
    "js-tokens" "^3.0.2"

"babel-core@^6.22.1", "babel-core@^6.26.0", "babel-core@~6", "babel-core@6":
  "integrity" "sha512-6jyFLuDmeidKmUEb3NM+/yawG0M2bDZ9Z1qbZP59cyHLz8kYGKYwpJP0UwUKKUiTRNvxfLesJnTedqczP7cTDA=="
  "resolved" "https://registry.npmjs.org/babel-core/-/babel-core-6.26.3.tgz"
  "version" "6.26.3"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "babel-generator" "^6.26.0"
    "babel-helpers" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-register" "^6.26.0"
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "convert-source-map" "^1.5.1"
    "debug" "^2.6.9"
    "json5" "^0.5.1"
    "lodash" "^4.17.4"
    "minimatch" "^3.0.4"
    "path-is-absolute" "^1.0.1"
    "private" "^0.1.8"
    "slash" "^1.0.0"
    "source-map" "^0.5.7"

"babel-eslint@^7.1.1":
  "integrity" "sha1-sv4tgBJkcPXBlELcdXJTqJdxCCc="
  "resolved" "https://registry.npmjs.org/babel-eslint/-/babel-eslint-7.2.3.tgz"
  "version" "7.2.3"
  dependencies:
    "babel-code-frame" "^6.22.0"
    "babel-traverse" "^6.23.1"
    "babel-types" "^6.23.0"
    "babylon" "^6.17.0"

"babel-generator@^6.18.0", "babel-generator@^6.26.0":
  "integrity" "sha512-HyfwY6ApZj7BYTcJURpM5tznulaBvyio7/0d4zFOeMPUmfxkCjHocCuoLa2SAGzBI8AREcH3eP3758F672DppA=="
  "resolved" "https://registry.npmjs.org/babel-generator/-/babel-generator-6.26.1.tgz"
  "version" "6.26.1"
  dependencies:
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "detect-indent" "^4.0.0"
    "jsesc" "^1.3.0"
    "lodash" "^4.17.4"
    "source-map" "^0.5.7"
    "trim-right" "^1.0.1"

"babel-helper-bindify-decorators@^6.24.1":
  "integrity" "sha1-FMGeXxQte0fxmlJDHlKxzLxAozA="
  "resolved" "https://registry.npmjs.org/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-builder-binary-assignment-operator-visitor@^6.24.1":
  "integrity" "sha1-zORReto1b0IgvK6KAsKzRvmlZmQ="
  "resolved" "https://registry.npmjs.org/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-explode-assignable-expression" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-call-delegate@^6.24.1":
  "integrity" "sha1-7Oaqzdx25Bw0YfiL/Fdb0Nqi340="
  "resolved" "https://registry.npmjs.org/babel-helper-call-delegate/-/babel-helper-call-delegate-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-hoist-variables" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-define-map@^6.24.1":
  "integrity" "sha1-pfVtq0GiX5fstJjH66ypgZ+Vvl8="
  "resolved" "https://registry.npmjs.org/babel-helper-define-map/-/babel-helper-define-map-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-helper-explode-assignable-expression@^6.24.1":
  "integrity" "sha1-8luCz33BBDPFX3BZLVdGQArCLKo="
  "resolved" "https://registry.npmjs.org/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-explode-class@^6.24.1":
  "integrity" "sha1-fcKjkQ3uAHBW4eMdZAztPVTqqes="
  "resolved" "https://registry.npmjs.org/babel-helper-explode-class/-/babel-helper-explode-class-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-bindify-decorators" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-function-name@^6.24.1":
  "integrity" "sha1-00dbjAPtmCQqJbSDUasYOZ01gKk="
  "resolved" "https://registry.npmjs.org/babel-helper-function-name/-/babel-helper-function-name-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-get-function-arity" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-get-function-arity@^6.24.1":
  "integrity" "sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0="
  "resolved" "https://registry.npmjs.org/babel-helper-get-function-arity/-/babel-helper-get-function-arity-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-hoist-variables@^6.24.1":
  "integrity" "sha1-HssnaJydJVE+rbyZFKc/VAi+enY="
  "resolved" "https://registry.npmjs.org/babel-helper-hoist-variables/-/babel-helper-hoist-variables-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-optimise-call-expression@^6.24.1":
  "integrity" "sha1-96E0J7qfc/j0+pk8VKl4gtEkQlc="
  "resolved" "https://registry.npmjs.org/babel-helper-optimise-call-expression/-/babel-helper-optimise-call-expression-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-regex@^6.24.1":
  "integrity" "sha1-MlxZ+QL4LyS3T6zu0DY5VPZJXnI="
  "resolved" "https://registry.npmjs.org/babel-helper-regex/-/babel-helper-regex-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-helper-remap-async-to-generator@^6.24.1":
  "integrity" "sha1-XsWBgnrXI/7N04HxySg5BnbkVRs="
  "resolved" "https://registry.npmjs.org/babel-helper-remap-async-to-generator/-/babel-helper-remap-async-to-generator-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-replace-supers@^6.24.1":
  "integrity" "sha1-v22/5Dk40XNpohPKiov3S2qQqxo="
  "resolved" "https://registry.npmjs.org/babel-helper-replace-supers/-/babel-helper-replace-supers-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-optimise-call-expression" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-vue-jsx-merge-props@^2.0.0":
  "integrity" "sha512-gsLiKK7Qrb7zYJNgiXKpXblxbV5ffSwR0f5whkPAaBAR4fhi6bwRZxX9wBlIc5M/v8CCkXUbXZL4N/nSE97cqg=="
  "resolved" "https://registry.npmmirror.com/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-2.0.3.tgz"
  "version" "2.0.3"

"babel-helpers@^6.24.1":
  "integrity" "sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI="
  "resolved" "https://registry.npmjs.org/babel-helpers/-/babel-helpers-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-loader@^7.1.1":
  "integrity" "sha512-iCHfbieL5d1LfOQeeVJEUyD9rTwBcP/fcEbRCfempxTDuqrKpu0AZjLAQHEQa3Yqyj9ORKe2iHfoj4rHLf7xpw=="
  "resolved" "https://registry.npmjs.org/babel-loader/-/babel-loader-7.1.5.tgz"
  "version" "7.1.5"
  dependencies:
    "find-cache-dir" "^1.0.0"
    "loader-utils" "^1.0.2"
    "mkdirp" "^0.5.1"

"babel-messages@^6.23.0":
  "integrity" "sha1-8830cDhYA1sqKVHG7F7fbGLyYw4="
  "resolved" "https://registry.npmjs.org/babel-messages/-/babel-messages-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-check-es2015-constants@^6.22.0":
  "integrity" "sha1-NRV7EBQm/S/9PaP3XH0ekYNbv4o="
  "resolved" "https://registry.npmjs.org/babel-plugin-check-es2015-constants/-/babel-plugin-check-es2015-constants-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-istanbul@^4.1.1":
  "integrity" "sha512-PWP9FQ1AhZhS01T/4qLSKoHGY/xvkZdVBGlKM/HuxxS3+sC66HhTNR7+MpbO/so/cz/wY94MeSWJuP1hXIPfwQ=="
  "resolved" "http://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-4.1.6.tgz"
  "version" "4.1.6"
  dependencies:
    "babel-plugin-syntax-object-rest-spread" "^6.13.0"
    "find-up" "^2.1.0"
    "istanbul-lib-instrument" "^1.10.1"
    "test-exclude" "^4.2.1"

"babel-plugin-syntax-async-functions@^6.8.0":
  "integrity" "sha1-ytnK0RkbWtY0vzCuCHI5HgZHvpU="
  "resolved" "http://registry.npmjs.org/babel-plugin-syntax-async-functions/-/babel-plugin-syntax-async-functions-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-async-generators@^6.5.0":
  "integrity" "sha1-a8lj67FuzLrmuStZbrfzXDQqi5o="
  "resolved" "http://registry.npmjs.org/babel-plugin-syntax-async-generators/-/babel-plugin-syntax-async-generators-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-class-properties@^6.8.0":
  "integrity" "sha1-1+sjt5oxf4VDlixQW4J8fWysJ94="
  "resolved" "http://registry.npmjs.org/babel-plugin-syntax-class-properties/-/babel-plugin-syntax-class-properties-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-decorators@^6.13.0":
  "integrity" "sha1-MSVjtNvePMgGzuPkFszurd0RrAs="
  "resolved" "http://registry.npmjs.org/babel-plugin-syntax-decorators/-/babel-plugin-syntax-decorators-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-dynamic-import@^6.18.0":
  "integrity" "sha1-jWomIpyDdFqZgqRBBRVyyqF5sdo="
  "resolved" "http://registry.npmjs.org/babel-plugin-syntax-dynamic-import/-/babel-plugin-syntax-dynamic-import-6.18.0.tgz"
  "version" "6.18.0"

"babel-plugin-syntax-exponentiation-operator@^6.8.0":
  "integrity" "sha1-nufoM3KQ2pUoggGmpX9BcDF4MN4="
  "resolved" "http://registry.npmjs.org/babel-plugin-syntax-exponentiation-operator/-/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-object-rest-spread@^6.13.0", "babel-plugin-syntax-object-rest-spread@^6.8.0":
  "integrity" "sha1-/WU28rzhODb/o6VFjEkDpZe7O/U="
  "resolved" "http://registry.npmjs.org/babel-plugin-syntax-object-rest-spread/-/babel-plugin-syntax-object-rest-spread-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-trailing-function-commas@^6.22.0":
  "integrity" "sha1-ugNgk3+NBuQBgKQ/4NVhb/9TLPM="
  "resolved" "https://registry.npmjs.org/babel-plugin-syntax-trailing-function-commas/-/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz"
  "version" "6.22.0"

"babel-plugin-transform-async-generator-functions@^6.24.1":
  "integrity" "sha1-8FiQAUX9PpkHpt3yjaWfIVJYpds="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-remap-async-to-generator" "^6.24.1"
    "babel-plugin-syntax-async-generators" "^6.5.0"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-async-to-generator@^6.22.0", "babel-plugin-transform-async-to-generator@^6.24.1":
  "integrity" "sha1-ZTbjeK/2yx1VF6wOQOs+n8jQh2E="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-async-to-generator/-/babel-plugin-transform-async-to-generator-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-remap-async-to-generator" "^6.24.1"
    "babel-plugin-syntax-async-functions" "^6.8.0"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-class-properties@^6.24.1":
  "integrity" "sha1-anl2PqYdM9NvN7YRqp3vgagbRqw="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-plugin-syntax-class-properties" "^6.8.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-decorators@^6.24.1":
  "integrity" "sha1-eIAT2PjGtSIr33s0Q5Df13Vp4k0="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-explode-class" "^6.24.1"
    "babel-plugin-syntax-decorators" "^6.13.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-arrow-functions@^6.22.0":
  "integrity" "sha1-RSaSy3EdX3ncf4XkQM5BufJE0iE="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-arrow-functions/-/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-block-scoped-functions@^6.22.0":
  "integrity" "sha1-u8UbSflk1wy42OC5ToICRs46YUE="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-block-scoped-functions/-/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-block-scoping@^6.23.0":
  "integrity" "sha1-1w9SmcEwjQXBL0Y4E7CgnnOxiV8="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-block-scoping/-/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-plugin-transform-es2015-classes@^6.23.0":
  "integrity" "sha1-WkxYpQyclGHlZLSyo7+ryXolhNs="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-classes/-/babel-plugin-transform-es2015-classes-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-define-map" "^6.24.1"
    "babel-helper-function-name" "^6.24.1"
    "babel-helper-optimise-call-expression" "^6.24.1"
    "babel-helper-replace-supers" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-computed-properties@^6.22.0":
  "integrity" "sha1-b+Ko0WiV1WNPTNmZttNICjCBWbM="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-computed-properties/-/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-destructuring@^6.23.0":
  "integrity" "sha1-mXux8auWf2gtKwh2/jWNYOdlxW0="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-destructuring/-/babel-plugin-transform-es2015-destructuring-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-duplicate-keys@^6.22.0":
  "integrity" "sha1-c+s9MQypaePvnskcU3QabxV2Qj4="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-duplicate-keys/-/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-for-of@^6.23.0":
  "integrity" "sha1-9HyVsrYT3x0+zC/bdXNiPHUkhpE="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-for-of/-/babel-plugin-transform-es2015-for-of-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-function-name@^6.22.0":
  "integrity" "sha1-g0yJhTvDaxrw86TF26qU/Y6sqos="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-function-name/-/babel-plugin-transform-es2015-function-name-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-literals@^6.22.0":
  "integrity" "sha1-T1SgLWzWbPkVKAAZox0xklN3yi4="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-literals/-/babel-plugin-transform-es2015-literals-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-modules-amd@^6.22.0", "babel-plugin-transform-es2015-modules-amd@^6.24.1":
  "integrity" "sha1-Oz5UAXI5hC1tGcMBHEvS8AoA0VQ="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-transform-es2015-modules-commonjs" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-modules-commonjs@^6.23.0", "babel-plugin-transform-es2015-modules-commonjs@^6.24.1":
  "integrity" "sha512-CV9ROOHEdrjcwhIaJNBGMBCodN+1cfkwtM1SbUHmvyy35KGT7fohbpOxkE2uLz1o6odKK2Ck/tz47z+VqQfi9Q=="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz"
  "version" "6.26.2"
  dependencies:
    "babel-plugin-transform-strict-mode" "^6.24.1"
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-types" "^6.26.0"

"babel-plugin-transform-es2015-modules-systemjs@^6.23.0":
  "integrity" "sha1-/4mhQrkRmpBhlfXxBuzzBdlAfSM="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-hoist-variables" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-modules-umd@^6.23.0":
  "integrity" "sha1-rJl+YoXNGO1hdq22B9YCNErThGg="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-transform-es2015-modules-amd" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-object-super@^6.22.0":
  "integrity" "sha1-JM72muIcuDp/hgPa0CH1cusnj40="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-object-super/-/babel-plugin-transform-es2015-object-super-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-replace-supers" "^6.24.1"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-parameters@^6.23.0":
  "integrity" "sha1-V6w1GrScrxSpfNE7CfZv3wpiXys="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-parameters/-/babel-plugin-transform-es2015-parameters-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-call-delegate" "^6.24.1"
    "babel-helper-get-function-arity" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-shorthand-properties@^6.22.0":
  "integrity" "sha1-JPh11nIch2YbvZmkYi5R8U3jiqA="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-shorthand-properties/-/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-spread@^6.22.0":
  "integrity" "sha1-1taKmfia7cRTbIGlQujdnxdG+NE="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-spread/-/babel-plugin-transform-es2015-spread-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-sticky-regex@^6.22.0":
  "integrity" "sha1-AMHNsaynERLN8M9hJsLta0V8zbw="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-sticky-regex/-/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-regex" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-template-literals@^6.22.0":
  "integrity" "sha1-qEs0UPfp+PH2g51taH2oS7EjbY0="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-template-literals/-/babel-plugin-transform-es2015-template-literals-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-typeof-symbol@^6.23.0":
  "integrity" "sha1-3sCfHN3/lLUqxz1QXITfWdzOs3I="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-typeof-symbol/-/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-unicode-regex@^6.22.0":
  "integrity" "sha1-04sS9C6nMj9yk4fxinxa4frrNek="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-es2015-unicode-regex/-/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-regex" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "regexpu-core" "^2.0.0"

"babel-plugin-transform-exponentiation-operator@^6.22.0", "babel-plugin-transform-exponentiation-operator@^6.24.1":
  "integrity" "sha1-KrDJx/MJj6SJB3cruBP+QejeOg4="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-builder-binary-assignment-operator-visitor" "^6.24.1"
    "babel-plugin-syntax-exponentiation-operator" "^6.8.0"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-object-rest-spread@^6.22.0":
  "integrity" "sha1-DzZpLVD+9rfi1LOsFHgTepY7ewY="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-object-rest-spread/-/babel-plugin-transform-object-rest-spread-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-plugin-syntax-object-rest-spread" "^6.8.0"
    "babel-runtime" "^6.26.0"

"babel-plugin-transform-regenerator@^6.22.0":
  "integrity" "sha1-4HA2lvveJ/Cj78rPi03KL3s6jy8="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-regenerator/-/babel-plugin-transform-regenerator-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "regenerator-transform" "^0.10.0"

"babel-plugin-transform-runtime@^6.22.0":
  "integrity" "sha1-iEkNRGUC6puOfvsP4J7E2ZR5se4="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-runtime/-/babel-plugin-transform-runtime-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-strict-mode@^6.24.1":
  "integrity" "sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-strict-mode/-/babel-plugin-transform-strict-mode-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-polyfill@^6.23.0":
  "integrity" "sha1-N5k3q8Z9eJWXCtxiHyhM2WbPIVM="
  "resolved" "https://registry.npmjs.org/babel-polyfill/-/babel-polyfill-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "core-js" "^2.5.0"
    "regenerator-runtime" "^0.10.5"

"babel-preset-env@^1.3.2":
  "integrity" "sha512-9OR2afuKDneX2/q2EurSftUYM0xGu4O2D9adAhVfADDhrYDaxXV0rBbevVYoY9n6nyX1PmQW/0jtpJvUNr9CHg=="
  "resolved" "https://registry.npmjs.org/babel-preset-env/-/babel-preset-env-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "babel-plugin-check-es2015-constants" "^6.22.0"
    "babel-plugin-syntax-trailing-function-commas" "^6.22.0"
    "babel-plugin-transform-async-to-generator" "^6.22.0"
    "babel-plugin-transform-es2015-arrow-functions" "^6.22.0"
    "babel-plugin-transform-es2015-block-scoped-functions" "^6.22.0"
    "babel-plugin-transform-es2015-block-scoping" "^6.23.0"
    "babel-plugin-transform-es2015-classes" "^6.23.0"
    "babel-plugin-transform-es2015-computed-properties" "^6.22.0"
    "babel-plugin-transform-es2015-destructuring" "^6.23.0"
    "babel-plugin-transform-es2015-duplicate-keys" "^6.22.0"
    "babel-plugin-transform-es2015-for-of" "^6.23.0"
    "babel-plugin-transform-es2015-function-name" "^6.22.0"
    "babel-plugin-transform-es2015-literals" "^6.22.0"
    "babel-plugin-transform-es2015-modules-amd" "^6.22.0"
    "babel-plugin-transform-es2015-modules-commonjs" "^6.23.0"
    "babel-plugin-transform-es2015-modules-systemjs" "^6.23.0"
    "babel-plugin-transform-es2015-modules-umd" "^6.23.0"
    "babel-plugin-transform-es2015-object-super" "^6.22.0"
    "babel-plugin-transform-es2015-parameters" "^6.23.0"
    "babel-plugin-transform-es2015-shorthand-properties" "^6.22.0"
    "babel-plugin-transform-es2015-spread" "^6.22.0"
    "babel-plugin-transform-es2015-sticky-regex" "^6.22.0"
    "babel-plugin-transform-es2015-template-literals" "^6.22.0"
    "babel-plugin-transform-es2015-typeof-symbol" "^6.23.0"
    "babel-plugin-transform-es2015-unicode-regex" "^6.22.0"
    "babel-plugin-transform-exponentiation-operator" "^6.22.0"
    "babel-plugin-transform-regenerator" "^6.22.0"
    "browserslist" "^3.2.6"
    "invariant" "^2.2.2"
    "semver" "^5.3.0"

"babel-preset-stage-2@^6.22.0":
  "integrity" "sha1-2eKWD7PXEYfw5k7sYrwHdnIZvcE="
  "resolved" "https://registry.npmjs.org/babel-preset-stage-2/-/babel-preset-stage-2-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-syntax-dynamic-import" "^6.18.0"
    "babel-plugin-transform-class-properties" "^6.24.1"
    "babel-plugin-transform-decorators" "^6.24.1"
    "babel-preset-stage-3" "^6.24.1"

"babel-preset-stage-3@^6.24.1":
  "integrity" "sha1-g2raCp56f6N8sTj7kyb4eTSkg5U="
  "resolved" "https://registry.npmjs.org/babel-preset-stage-3/-/babel-preset-stage-3-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-syntax-trailing-function-commas" "^6.22.0"
    "babel-plugin-transform-async-generator-functions" "^6.24.1"
    "babel-plugin-transform-async-to-generator" "^6.24.1"
    "babel-plugin-transform-exponentiation-operator" "^6.24.1"
    "babel-plugin-transform-object-rest-spread" "^6.22.0"

"babel-register@^6.22.0", "babel-register@^6.26.0":
  "integrity" "sha1-btAhFz4vy0htestFxgCahW9kcHE="
  "resolved" "https://registry.npmjs.org/babel-register/-/babel-register-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-core" "^6.26.0"
    "babel-runtime" "^6.26.0"
    "core-js" "^2.5.0"
    "home-or-tmp" "^2.0.0"
    "lodash" "^4.17.4"
    "mkdirp" "^0.5.1"
    "source-map-support" "^0.4.15"

"babel-runtime@^6.0.0", "babel-runtime@^6.18.0", "babel-runtime@^6.22.0", "babel-runtime@^6.26.0", "babel-runtime@6.x":
  "integrity" "sha1-llxwWGaOgrVde/4E/yM3vItWR/4="
  "resolved" "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "^2.4.0"
    "regenerator-runtime" "^0.11.0"

"babel-template@^6.16.0", "babel-template@^6.24.1", "babel-template@^6.26.0":
  "integrity" "sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI="
  "resolved" "https://registry.npmjs.org/babel-template/-/babel-template-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "lodash" "^4.17.4"

"babel-traverse@^6.18.0", "babel-traverse@^6.23.1", "babel-traverse@^6.24.1", "babel-traverse@^6.26.0":
  "integrity" "sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4="
  "resolved" "https://registry.npmjs.org/babel-traverse/-/babel-traverse-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "debug" "^2.6.8"
    "globals" "^9.18.0"
    "invariant" "^2.2.2"
    "lodash" "^4.17.4"

"babel-types@^6.18.0", "babel-types@^6.19.0", "babel-types@^6.23.0", "babel-types@^6.24.1", "babel-types@^6.26.0":
  "integrity" "sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc="
  "resolved" "https://registry.npmjs.org/babel-types/-/babel-types-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "esutils" "^2.0.2"
    "lodash" "^4.17.4"
    "to-fast-properties" "^1.0.3"

"babylon@^6.17.0", "babylon@^6.18.0":
  "integrity" "sha512-q/UEjfGJ2Cm3oKV71DJz9d25TPnq5rhBVL2Q4fA5wcC3jcrdn7+SssEybFIxwAvvP+YCsCYNKughoF33GxgycQ=="
  "resolved" "https://registry.npmjs.org/babylon/-/babylon-6.18.0.tgz"
  "version" "6.18.0"

"backo2@1.0.2":
  "integrity" "sha1-MasayLEpNjRj41s+u2n038+6eUc="
  "resolved" "https://registry.npmjs.org/backo2/-/backo2-1.0.2.tgz"
  "version" "1.0.2"

"balanced-match@^0.4.2":
  "integrity" "sha1-yz8+PHMtwPAe5wtAPzAuYddwmDg="
  "resolved" "https://registry.npmjs.org/balanced-match/-/balanced-match-0.4.2.tgz"
  "version" "0.4.2"

"balanced-match@^1.0.0":
  "integrity" "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="
  "resolved" "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz"
  "version" "1.0.0"

"base@^0.11.1":
  "integrity" "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg=="
  "resolved" "https://registry.npmjs.org/base/-/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-arraybuffer@0.1.5":
  "integrity" "sha1-c5JncZI7Whl0etZmqlzUv5xunOg="
  "resolved" "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-0.1.5.tgz"
  "version" "0.1.5"

"base64-js@^1.0.2":
  "integrity" "sha512-ccav/yGvoa80BQDljCxsmmQ3Xvx60/UpBIij5QN21W3wBi/hhIC9OoO+KLpu9IJTS9j4DRVJ3aDDF9cMSoa2lw=="
  "resolved" "https://registry.npmjs.org/base64-js/-/base64-js-1.3.0.tgz"
  "version" "1.3.0"

"base64id@1.0.0":
  "integrity" "sha1-R2iMuZu2gE8OBtPnY7HDLlfY5rY="
  "resolved" "https://registry.npmjs.org/base64id/-/base64id-1.0.0.tgz"
  "version" "1.0.0"

"bcrypt-pbkdf@^1.0.0":
  "integrity" "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4="
  "resolved" "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tweetnacl" "^0.14.3"

"better-assert@~1.0.0":
  "integrity" "sha1-QIZrnhueC1W0gYlDEeaPr/rrxSI="
  "resolved" "https://registry.npmjs.org/better-assert/-/better-assert-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "callsite" "1.0.0"

"bfj-node4@^5.2.0":
  "integrity" "sha512-SOmOsowQWfXc7ybFARsK3C4MCOWzERaOMV/Fl3Tgjs+5dJWyzo3oa127jL44eMbQiAN17J7SvAs2TRxEScTUmg=="
  "resolved" "https://registry.npmjs.org/bfj-node4/-/bfj-node4-5.3.1.tgz"
  "version" "5.3.1"
  dependencies:
    "bluebird" "^3.5.1"
    "check-types" "^7.3.0"
    "tryer" "^1.0.0"

"big.js@^3.1.3":
  "integrity" "sha512-+hN/Zh2D08Mx65pZ/4g5bsmNiZUuChDiQfTUQ7qJr4/kuopCr88xZsAXv6mBoZEsUI4OuGHlX59qE94K2mMW8Q=="
  "resolved" "https://registry.npmjs.org/big.js/-/big.js-3.2.0.tgz"
  "version" "3.2.0"

"big.js@^5.2.2":
  "integrity" "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="
  "resolved" "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^1.0.0":
  "integrity" "sha512-EgmjVLMn22z7eGGv3kcnHwSnJXmFHjISTY9E/S5lIcTD3Oxw05QTcBLNkJFzcb3cNueUdF/IN4U+d78V0zO8Hw=="
  "resolved" "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.13.0.tgz"
  "version" "1.13.0"

"blob@0.0.4":
  "integrity" "sha1-vPEwUspURj8w+fx+lbmkdjCpSSE="
  "resolved" "https://registry.npmjs.org/blob/-/blob-0.0.4.tgz"
  "version" "0.0.4"

"bluebird@^3.1.1", "bluebird@^3.3.0", "bluebird@^3.4.7", "bluebird@^3.5.1":
  "integrity" "sha512-/qKPUQlaW1OyR51WeCPBvRnAlnZFUJkCSG5HzGnuIqhgyJtF+T94lFnn33eiazjRm2LAHVy2guNnaq48X9SJuw=="
  "resolved" "https://registry.npmjs.org/bluebird/-/bluebird-3.5.3.tgz"
  "version" "3.5.3"

"bn.js@^4.0.0", "bn.js@^4.1.0", "bn.js@^4.1.1", "bn.js@^4.4.0":
  "integrity" "sha512-ItfYfPLkWHUjckQCk8xC+LwxgK8NYcXywGigJgSwOP8Y2iyWT4f2vsZnoOXTTbo+o5yXmIUJ4gn5538SO5S3gA=="
  "resolved" "https://registry.npmjs.org/bn.js/-/bn.js-4.11.8.tgz"
  "version" "4.11.8"

"body-parser@^1.16.1", "body-parser@1.18.3":
  "integrity" "sha1-WykhmP/dVTs6DyDe0FkrlWlVyLQ="
  "resolved" "https://registry.npmjs.org/body-parser/-/body-parser-1.18.3.tgz"
  "version" "1.18.3"
  dependencies:
    "bytes" "3.0.0"
    "content-type" "~1.0.4"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "http-errors" "~1.6.3"
    "iconv-lite" "0.4.23"
    "on-finished" "~2.3.0"
    "qs" "6.5.2"
    "raw-body" "2.3.3"
    "type-is" "~1.6.16"

"boolbase@~1.0.0":
  "integrity" "sha1-aN/1++YMUes3cl6p4+0xDcwed24="
  "resolved" "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"boom@2.x.x":
  "integrity" "sha1-OciRjO/1eZ+D+UkqhI9iWt0Mdm8="
  "resolved" "https://registry.npmjs.org/boom/-/boom-2.10.1.tgz"
  "version" "2.10.1"
  dependencies:
    "hoek" "2.x.x"

"brace-expansion@^1.0.0", "brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^0.1.2":
  "integrity" "sha1-wIVxEIUpHYt1/ddOqw+FlygHEeY="
  "resolved" "https://registry.npmjs.org/braces/-/braces-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "expand-range" "^0.1.0"

"braces@^1.8.2":
  "integrity" "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc="
  "resolved" "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "expand-range" "^1.8.1"
    "preserve" "^0.2.0"
    "repeat-element" "^1.1.2"

"braces@^2.3.1":
  "integrity" "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk="
  "resolved" "https://registry.npm.taobao.org/braces/download/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@^2.3.2":
  "integrity" "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"brorand@^1.0.1":
  "integrity" "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8="
  "resolved" "https://registry.npmjs.org/brorand/-/brorand-1.1.0.tgz"
  "version" "1.1.0"

"browser-stdout@1.3.0":
  "integrity" "sha1-81HTKWnTL6XXpVZxVCY9korjvR8="
  "resolved" "https://registry.npmjs.org/browser-stdout/-/browser-stdout-1.3.0.tgz"
  "version" "1.3.0"

"browserify-aes@^1.0.0", "browserify-aes@^1.0.4":
  "integrity" "sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA=="
  "resolved" "http://registry.npmjs.org/browserify-aes/-/browserify-aes-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "buffer-xor" "^1.0.3"
    "cipher-base" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.3"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"browserify-cipher@^1.0.0":
  "integrity" "sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w=="
  "resolved" "https://registry.npmjs.org/browserify-cipher/-/browserify-cipher-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "browserify-aes" "^1.0.4"
    "browserify-des" "^1.0.0"
    "evp_bytestokey" "^1.0.0"

"browserify-des@^1.0.0":
  "integrity" "sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A=="
  "resolved" "https://registry.npmjs.org/browserify-des/-/browserify-des-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "cipher-base" "^1.0.1"
    "des.js" "^1.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"browserify-rsa@^4.0.0":
  "integrity" "sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ="
  "resolved" "http://registry.npmjs.org/browserify-rsa/-/browserify-rsa-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.1.0"
    "randombytes" "^2.0.1"

"browserify-sign@^4.0.0":
  "integrity" "sha1-qk62jl17ZYuqa/alfmMMvXqT0pg="
  "resolved" "https://registry.npmjs.org/browserify-sign/-/browserify-sign-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "bn.js" "^4.1.1"
    "browserify-rsa" "^4.0.0"
    "create-hash" "^1.1.0"
    "create-hmac" "^1.1.2"
    "elliptic" "^6.0.0"
    "inherits" "^2.0.1"
    "parse-asn1" "^5.0.0"

"browserify-zlib@^0.2.0":
  "integrity" "sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA=="
  "resolved" "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "pako" "~1.0.5"

"browserslist@^1.3.6":
  "integrity" "sha1-C9dnBCWL6CmyOYu1Dkti0aFmsLk="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-1.7.7.tgz"
  "version" "1.7.7"
  dependencies:
    "caniuse-db" "^1.0.30000639"
    "electron-to-chromium" "^1.2.7"

"browserslist@^1.5.2":
  "integrity" "sha1-C9dnBCWL6CmyOYu1Dkti0aFmsLk="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-1.7.7.tgz"
  "version" "1.7.7"
  dependencies:
    "caniuse-db" "^1.0.30000639"
    "electron-to-chromium" "^1.2.7"

"browserslist@^1.7.6":
  "integrity" "sha1-C9dnBCWL6CmyOYu1Dkti0aFmsLk="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-1.7.7.tgz"
  "version" "1.7.7"
  dependencies:
    "caniuse-db" "^1.0.30000639"
    "electron-to-chromium" "^1.2.7"

"browserslist@^2.11.3":
  "integrity" "sha512-yWu5cXT7Av6mVwzWc8lMsJMHWn4xyjSuGYi4IozbVTLUOEYPSagUB8kiMDUHA1fS3zjr8nkxkn9jdvug4BBRmA=="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-2.11.3.tgz"
  "version" "2.11.3"
  dependencies:
    "caniuse-lite" "^1.0.30000792"
    "electron-to-chromium" "^1.3.30"

"browserslist@^3.2.6":
  "integrity" "sha512-WHVocJYavUwVgVViC0ORikPHQquXwVh939TaelZ4WDqpWgTX/FsGhl/+P4qBUAGcRvtOgDgC+xftNWWp2RUTAQ=="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-3.2.8.tgz"
  "version" "3.2.8"
  dependencies:
    "caniuse-lite" "^1.0.30000844"
    "electron-to-chromium" "^1.3.47"

"buffer-alloc-unsafe@^1.1.0":
  "integrity" "sha512-TEM2iMIEQdJ2yjPJoSIsldnleVaAk1oW3DBVUykyOLsEsFmEc9kn+SFFPz+gl54KQNxlDnAwCXosOS9Okx2xAg=="
  "resolved" "https://registry.npmjs.org/buffer-alloc-unsafe/-/buffer-alloc-unsafe-1.1.0.tgz"
  "version" "1.1.0"

"buffer-alloc@^1.2.0":
  "integrity" "sha512-CFsHQgjtW1UChdXgbyJGtnm+O/uLQeZdtbDo8mfUgYXCHSM1wgrVxXm6bSyrUuErEb+4sYVGCzASBRot7zyrow=="
  "resolved" "https://registry.npmjs.org/buffer-alloc/-/buffer-alloc-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "buffer-alloc-unsafe" "^1.1.0"
    "buffer-fill" "^1.0.0"

"buffer-crc32@~0.2.3":
  "integrity" "sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ=="
  "resolved" "https://registry.npmmirror.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz"
  "version" "0.2.13"

"buffer-fill@^1.0.0":
  "integrity" "sha1-+PeLdniYiO858gXNY39o5wISKyw="
  "resolved" "https://registry.npmjs.org/buffer-fill/-/buffer-fill-1.0.0.tgz"
  "version" "1.0.0"

"buffer-from@^1.0.0":
  "integrity" "sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A=="
  "resolved" "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.1.tgz"
  "version" "1.1.1"

"buffer-xor@^1.0.3":
  "integrity" "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk="
  "resolved" "https://registry.npmjs.org/buffer-xor/-/buffer-xor-1.0.3.tgz"
  "version" "1.0.3"

"buffer@^4.3.0":
  "integrity" "sha1-bRu2AbB6TvztlwlBMgkwJ8lbwpg="
  "resolved" "http://registry.npmjs.org/buffer/-/buffer-4.9.1.tgz"
  "version" "4.9.1"
  dependencies:
    "base64-js" "^1.0.2"
    "ieee754" "^1.1.4"
    "isarray" "^1.0.0"

"builtin-status-codes@^3.0.0":
  "integrity" "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug="
  "resolved" "https://registry.npmjs.org/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.0.0":
  "integrity" "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="
  "resolved" "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz"
  "version" "3.0.0"

"cacache@^10.0.4":
  "integrity" "sha512-Dph0MzuH+rTQzGPNT9fAnrPmMmjKfST6trxJeK7NQuHRaVw24VzPRWTmg9MpcwOVQZO0E1FBICUlFeNaKPIfHA=="
  "resolved" "https://registry.npmjs.org/cacache/-/cacache-10.0.4.tgz"
  "version" "10.0.4"
  dependencies:
    "bluebird" "^3.5.1"
    "chownr" "^1.0.1"
    "glob" "^7.1.2"
    "graceful-fs" "^4.1.11"
    "lru-cache" "^4.1.1"
    "mississippi" "^2.0.0"
    "mkdirp" "^0.5.1"
    "move-concurrently" "^1.0.1"
    "promise-inflight" "^1.0.1"
    "rimraf" "^2.6.2"
    "ssri" "^5.2.4"
    "unique-filename" "^1.1.0"
    "y18n" "^4.0.0"

"cache-base@^1.0.1":
  "integrity" "sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ=="
  "resolved" "https://registry.npmjs.org/cache-base/-/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"cacheable-request@^2.1.1":
  "integrity" "sha1-DYCIAbY0KtM8kd+dC0TcCbkeXD0="
  "resolved" "https://registry.npmjs.org/cacheable-request/-/cacheable-request-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "clone-response" "1.0.2"
    "get-stream" "3.0.0"
    "http-cache-semantics" "3.8.1"
    "keyv" "3.0.0"
    "lowercase-keys" "1.0.0"
    "normalize-url" "2.0.1"
    "responselike" "1.0.2"

"call-bind@^1.0.0":
  "integrity" "sha512-C3nQxfFZxFRVoJoGKKI8y3MOEo129NQ+FgQ08iye+Mk4zNZZGdjfs06bVTr+DBSlA66Q2VEcMki/cUCP4SercQ=="
  "resolved" "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "function-bind" "^1.1.2"
    "get-intrinsic" "^1.2.1"
    "set-function-length" "^1.1.1"

"caller-path@^0.1.0":
  "integrity" "sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8="
  "resolved" "https://registry.npmjs.org/caller-path/-/caller-path-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "callsites" "^0.2.0"

"callsite@1.0.0":
  "integrity" "sha1-KAOY5dZkvXQDi28JBRU+borxvCA="
  "resolved" "https://registry.npmjs.org/callsite/-/callsite-1.0.0.tgz"
  "version" "1.0.0"

"callsites@^0.2.0":
  "integrity" "sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-0.2.0.tgz"
  "version" "0.2.0"

"camel-case@3.0.x":
  "integrity" "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M="
  "resolved" "https://registry.npmjs.org/camel-case/-/camel-case-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "no-case" "^2.2.0"
    "upper-case" "^1.1.1"

"camelcase-keys@^2.0.0":
  "integrity" "sha1-MIvur/3ygRkFHvodkyITyRuPkuc="
  "resolved" "http://registry.npmjs.org/camelcase-keys/-/camelcase-keys-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "camelcase" "^2.0.0"
    "map-obj" "^1.0.0"

"camelcase@^1.0.2":
  "integrity" "sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz"
  "version" "1.2.1"

"camelcase@^2.0.0":
  "integrity" "sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-2.1.1.tgz"
  "version" "2.1.1"

"camelcase@^3.0.0":
  "integrity" "sha1-MvxLn82vhF/N9+c7uXysImHwqwo="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-3.0.0.tgz"
  "version" "3.0.0"

"camelcase@^5.0.0":
  "integrity" "sha512-faqwZqnWxbxn+F1d399ygeamQNy3lPp/H9H6rNrqYh4FSVCtcY+3cub1MxA8o9mDd55mM8Aghuu/kuyYA6VTsA=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-5.0.0.tgz"
  "version" "5.0.0"

"can-promise@0.0.1":
  "integrity" "sha512-gzVrHyyrvgt0YpDm7pn04MQt8gjh0ZAhN4ZDyCRtGl6YnuuK6b4aiUTD7G52r9l4YNmxfTtEscb92vxtAlL6XQ=="
  "resolved" "https://registry.npmjs.org/can-promise/-/can-promise-0.0.1.tgz"
  "version" "0.0.1"
  dependencies:
    "window-or-global" "^1.0.1"

"caniuse-api@^1.5.2":
  "integrity" "sha1-tTTnxzTE+B7F++isoq0kNUuWLGw="
  "resolved" "https://registry.npmjs.org/caniuse-api/-/caniuse-api-1.6.1.tgz"
  "version" "1.6.1"
  dependencies:
    "browserslist" "^1.3.6"
    "caniuse-db" "^1.0.30000529"
    "lodash.memoize" "^4.1.2"
    "lodash.uniq" "^4.5.0"

"caniuse-db@^1.0.30000529", "caniuse-db@^1.0.30000634", "caniuse-db@^1.0.30000639":
  "integrity" "sha512-gOrcU8d+h5AdrO/Mhnj35vttNvAed2taqzrYDfhJE/qVnLxAaGb1doWlRF7iDex+EQPhkwAHc07RBwixnxpFDQ=="
  "resolved" "https://registry.npmjs.org/caniuse-db/-/caniuse-db-1.0.30000936.tgz"
  "version" "1.0.30000936"

"caniuse-lite@^1.0.30000792", "caniuse-lite@^1.0.30000805", "caniuse-lite@^1.0.30000844":
  "integrity" "sha512-orX4IdpbFhdNO7bTBhSbahp1EBpqzBc+qrvTRVUFfZgA4zta7TdM6PN5ZxkEUgDnz36m+PfWGcdX7AVfFWItJw=="
  "resolved" "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30000936.tgz"
  "version" "1.0.30000936"

"caseless@~0.12.0":
  "integrity" "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="
  "resolved" "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz"
  "version" "0.12.0"

"center-align@^0.1.1":
  "integrity" "sha1-qg0yYptu6XIgBBHL1EYckHvCt60="
  "resolved" "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "align-text" "^0.1.3"
    "lazy-cache" "^1.0.3"

"chai-nightwatch@~0.1.x":
  "integrity" "sha1-HKVt52jTwIaP5/wvTTLC/olOa+k="
  "resolved" "http://registry.npmjs.org/chai-nightwatch/-/chai-nightwatch-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "assertion-error" "1.0.0"
    "deep-eql" "0.1.3"

"chai@^3.5.0", "chai@>=1.9.2 <5", "chai@>=3.5.0 <5":
  "integrity" "sha1-TQJjewZ/6Vi9v906QOxW/vc3Mkc="
  "resolved" "http://registry.npmjs.org/chai/-/chai-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "assertion-error" "^1.0.1"
    "deep-eql" "^0.1.3"
    "type-detect" "^1.0.0"

"chalk@^1.0.0":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "http://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^1.1.1":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "http://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^1.1.3":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "http://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^2.0.1", "chalk@^2.1.0", "chalk@^2.3.0", "chalk@^2.4.1":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"check-types@^7.3.0":
  "integrity" "sha512-YbulWHdfP99UfZ73NcUDlNJhEIDgm9Doq9GhpyXbF+7Aegi3CVV7qqMCKTTqJxlvEvnQBp9IA+dxsGN6xK/nSg=="
  "resolved" "https://registry.npmjs.org/check-types/-/check-types-7.4.0.tgz"
  "version" "7.4.0"

"chokidar@^1.4.1":
  "integrity" "sha1-eY5ol3gVHIB2tLNg5e3SjNortGg="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "anymatch" "^1.3.0"
    "async-each" "^1.0.0"
    "glob-parent" "^2.0.0"
    "inherits" "^2.0.1"
    "is-binary-path" "^1.0.0"
    "is-glob" "^2.0.0"
    "path-is-absolute" "^1.0.0"
    "readdirp" "^2.0.0"
  optionalDependencies:
    "fsevents" "^1.0.0"

"chokidar@^2.0.2":
  "integrity" "sha512-gfw3p2oQV2wEt+8VuMlNsPjCxDxvvgnm/kz+uATu805mWVF8IJN7uz9DN7iBz+RMJISmiVbCOBFs9qBGMjtPfQ=="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "anymatch" "^2.0.0"
    "async-each" "^1.0.1"
    "braces" "^2.3.2"
    "glob-parent" "^3.1.0"
    "inherits" "^2.0.3"
    "is-binary-path" "^1.0.0"
    "is-glob" "^4.0.0"
    "normalize-path" "^3.0.0"
    "path-is-absolute" "^1.0.0"
    "readdirp" "^2.2.1"
    "upath" "^1.1.0"
  optionalDependencies:
    "fsevents" "^1.2.7"

"chownr@^1.0.1":
  "integrity" "sha512-j38EvO5+LHX84jlo6h4UzmOwi0UgW61WRyPtJz4qaadK5eY3BTS5TY/S1Stc3Uk2lIM6TPevAlULiEJwie860g=="
  "resolved" "https://registry.npmjs.org/chownr/-/chownr-1.1.1.tgz"
  "version" "1.1.1"

"chownr@^1.1.1":
  "integrity" "sha512-j38EvO5+LHX84jlo6h4UzmOwi0UgW61WRyPtJz4qaadK5eY3BTS5TY/S1Stc3Uk2lIM6TPevAlULiEJwie860g=="
  "resolved" "https://registry.npmjs.org/chownr/-/chownr-1.1.1.tgz"
  "version" "1.1.1"

"cipher-base@^1.0.0", "cipher-base@^1.0.1", "cipher-base@^1.0.3":
  "integrity" "sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q=="
  "resolved" "https://registry.npmjs.org/cipher-base/-/cipher-base-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"circular-json@^0.3.1":
  "integrity" "sha512-UZK3NBx2Mca+b5LsG7bY183pHWt5Y1xts4P3Pz7ENTwGVnJOUWbRb3ocjvX7hx9tq/yTAdclXm9sZ38gNuem4A=="
  "resolved" "https://registry.npmjs.org/circular-json/-/circular-json-0.3.3.tgz"
  "version" "0.3.3"

"clap@^1.0.9":
  "integrity" "sha512-4CoL/A3hf90V3VIEjeuhSvlGFEHKzOz+Wfc2IVZc+FaUgU0ZQafJTP49fvnULipOPcAfqhyI2duwQyns6xqjYA=="
  "resolved" "https://registry.npmjs.org/clap/-/clap-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "chalk" "^1.1.3"

"class-utils@^0.3.5":
  "integrity" "sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg=="
  "resolved" "https://registry.npmjs.org/class-utils/-/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"clean-css@4.2.x":
  "integrity" "sha512-4ZxI6dy4lrY6FHzfiy1aEOXgu4LIsW2MhwG0VBKdcoGoH/XLFgaHSdLTGr4O8Be6A8r3MOphEiI8Gc1n0ecf3g=="
  "resolved" "https://registry.npmjs.org/clean-css/-/clean-css-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "source-map" "~0.6.0"

"cli-cursor@^1.0.1":
  "integrity" "sha1-ZNo/fValRBLll5S9Ytw1KV6PKYc="
  "resolved" "https://registry.npmjs.org/cli-cursor/-/cli-cursor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "restore-cursor" "^1.0.1"

"cli-cursor@^2.1.0":
  "integrity" "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU="
  "resolved" "https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "restore-cursor" "^2.0.0"

"cli-spinners@^1.0.1":
  "integrity" "sha512-1QL4544moEsDVH9T/l6Cemov/37iv1RtoKf7NJ04A60+4MREXNfx/QvavbH6QoGdsD4N4Mwy49cmaINR/o2mdg=="
  "resolved" "https://registry.npmjs.org/cli-spinners/-/cli-spinners-1.3.1.tgz"
  "version" "1.3.1"

"cli-width@^2.0.0":
  "integrity" "sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk="
  "resolved" "https://registry.npmjs.org/cli-width/-/cli-width-2.2.0.tgz"
  "version" "2.2.0"

"cliui@^2.1.0":
  "integrity" "sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "center-align" "^0.1.1"
    "right-align" "^0.1.1"
    "wordwrap" "0.0.2"

"cliui@^3.2.0":
  "integrity" "sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "string-width" "^1.0.1"
    "strip-ansi" "^3.0.1"
    "wrap-ansi" "^2.0.0"

"cliui@^4.0.0":
  "integrity" "sha512-4FG+RSG9DL7uEwRUZXZn3SS34DiDPfzP0VOiEwtUWlE+AR2EIg+hSyvrIgUUfhdgR/UkAeW2QHgeP+hWrXs7jQ=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "string-width" "^2.1.1"
    "strip-ansi" "^4.0.0"
    "wrap-ansi" "^2.0.0"

"clone-response@1.0.2":
  "integrity" "sha1-0dyXOSAxTfZ/vrlCI7TuNQI56Ws="
  "resolved" "https://registry.npmjs.org/clone-response/-/clone-response-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "mimic-response" "^1.0.0"

"clone@^1.0.2":
  "integrity" "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="
  "resolved" "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz"
  "version" "1.0.4"

"clone@^2.1.1":
  "integrity" "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="
  "resolved" "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
  "version" "2.1.2"

"co@^4.6.0":
  "integrity" "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="
  "resolved" "https://registry.npmjs.org/co/-/co-4.6.0.tgz"
  "version" "4.6.0"

"co@~3.0.6":
  "integrity" "sha1-FEXyJsXrlWE45oyawwFn6n0ua9o="
  "resolved" "https://registry.npmjs.org/co/-/co-3.0.6.tgz"
  "version" "3.0.6"

"coa@~1.0.1":
  "integrity" "sha1-qe8VNmDWqGqL3sAomlxoTSF0Mv0="
  "resolved" "https://registry.npmjs.org/coa/-/coa-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "q" "^1.1.2"

"coalescy@1.0.0":
  "integrity" "sha1-SwZYRrg2NhrabEtKSr9LwcrDG/E="
  "resolved" "https://registry.npmjs.org/coalescy/-/coalescy-1.0.0.tgz"
  "version" "1.0.0"

"code-point-at@^1.0.0":
  "integrity" "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c="
  "resolved" "https://registry.npmjs.org/code-point-at/-/code-point-at-1.1.0.tgz"
  "version" "1.1.0"

"collection-visit@^1.0.0":
  "integrity" "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="
  "resolved" "https://registry.npmjs.org/collection-visit/-/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.3.0", "color-convert@^1.9.0":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-name@^1.0.0", "color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-string@^0.3.0":
  "integrity" "sha1-J9RvtnAlxcL6JZk7+/V55HhBuZE="
  "resolved" "https://registry.npmjs.org/color-string/-/color-string-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "color-name" "^1.0.0"

"color@^0.11.0":
  "integrity" "sha1-bXtcdPtl6EHNSHkq0e1eB7kE12Q="
  "resolved" "http://registry.npmjs.org/color/-/color-0.11.4.tgz"
  "version" "0.11.4"
  dependencies:
    "clone" "^1.0.2"
    "color-convert" "^1.3.0"
    "color-string" "^0.3.0"

"colormin@^1.0.5":
  "integrity" "sha1-6i90IKcrlogaOKrlnsEkpvcpgTM="
  "resolved" "https://registry.npmjs.org/colormin/-/colormin-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "color" "^0.11.0"
    "css-color-names" "0.0.4"
    "has" "^1.0.1"

"colors@^1.1.0", "colors@^1.1.2", "colors@~1.1.2":
  "integrity" "sha1-FopHAXVran9RoSzgyXv6KMCE7WM="
  "resolved" "https://registry.npmjs.org/colors/-/colors-1.1.2.tgz"
  "version" "1.1.2"

"combine-lists@^1.0.0":
  "integrity" "sha1-RYwH4J4NkA/Ci3Cj/sLazR0st/Y="
  "resolved" "https://registry.npmjs.org/combine-lists/-/combine-lists-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "lodash" "^4.5.0"

"combined-stream@^1.0.5", "combined-stream@^1.0.6", "combined-stream@~1.0.5", "combined-stream@~1.0.6":
  "integrity" "sha512-brWl9y6vOB1xYPZcpZde3N9zDByXTosAeMDo4p1wzo6UMOX4vumB+TP1RZ76sfE6Md68Q0NJSrE/gbezd4Ul+w=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^2.13.0", "commander@~2.17.1", "commander@2.17.x":
  "integrity" "sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-2.17.1.tgz"
  "version" "2.17.1"

"commander@2.9.0":
  "integrity" "sha1-nJkJQXbhIkDLItbFFGCYQA/g99Q="
  "resolved" "http://registry.npmjs.org/commander/-/commander-2.9.0.tgz"
  "version" "2.9.0"
  dependencies:
    "graceful-readlink" ">= 1.0.0"

"commondir@^1.0.1":
  "integrity" "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="
  "resolved" "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-bind@1.0.0":
  "integrity" "sha1-AMYIq33Nk4l8AAllGx06jh5zu9E="
  "resolved" "https://registry.npmjs.org/component-bind/-/component-bind-1.0.0.tgz"
  "version" "1.0.0"

"component-emitter@^1.2.1", "component-emitter@1.2.1":
  "integrity" "sha1-E3kY1teCg/ffemt8WmPhQOaUJeY="
  "resolved" "https://registry.npmjs.org/component-emitter/-/component-emitter-1.2.1.tgz"
  "version" "1.2.1"

"component-emitter@1.1.2":
  "integrity" "sha1-KWWU8nU9qmOZbSrwjRWpURbJrsM="
  "resolved" "https://registry.npmjs.org/component-emitter/-/component-emitter-1.1.2.tgz"
  "version" "1.1.2"

"component-inherit@0.0.3":
  "integrity" "sha1-ZF/ErfWLcrZJ1crmUTVhnbJv8UM="
  "resolved" "https://registry.npmjs.org/component-inherit/-/component-inherit-0.0.3.tgz"
  "version" "0.0.3"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concat-stream@^1.5.0", "concat-stream@^1.5.2", "concat-stream@^1.6.2":
  "integrity" "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw=="
  "resolved" "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "buffer-from" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^2.2.2"
    "typedarray" "^0.0.6"

"connect-history-api-fallback@^1.3.0":
  "integrity" "sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg=="
  "resolved" "https://registry.npmjs.org/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz"
  "version" "1.6.0"

"connect@^3.6.0":
  "integrity" "sha1-Ce/2xVr3I24TcTWnJXSFi2eG9SQ="
  "resolved" "https://registry.npmjs.org/connect/-/connect-3.6.6.tgz"
  "version" "3.6.6"
  dependencies:
    "debug" "2.6.9"
    "finalhandler" "1.1.0"
    "parseurl" "~1.3.2"
    "utils-merge" "1.0.1"

"console-browserify@^1.1.0":
  "integrity" "sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA="
  "resolved" "https://registry.npmjs.org/console-browserify/-/console-browserify-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "date-now" "^0.1.4"

"console-control-strings@^1.0.0", "console-control-strings@~1.1.0":
  "integrity" "sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4="
  "resolved" "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz"
  "version" "1.1.0"

"consolidate@^0.14.0":
  "integrity" "sha1-WiUEe8dvcwcmZ8jLUsmJiI9JTGM="
  "resolved" "https://registry.npmjs.org/consolidate/-/consolidate-0.14.5.tgz"
  "version" "0.14.5"
  dependencies:
    "bluebird" "^3.1.1"

"constants-browserify@^1.0.0":
  "integrity" "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U="
  "resolved" "https://registry.npmjs.org/constants-browserify/-/constants-browserify-1.0.0.tgz"
  "version" "1.0.0"

"content-disposition@0.5.2":
  "integrity" "sha1-DPaLud318r55YcOoUXjLhdunjLQ="
  "resolved" "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.2.tgz"
  "version" "0.5.2"

"content-type@~1.0.4":
  "integrity" "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA=="
  "resolved" "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz"
  "version" "1.0.4"

"convert-source-map@^1.5.1":
  "integrity" "sha512-eFu7XigvxdZ1ETfbgPBohgyQ/Z++C0eEhTor0qRwBw9unw+L0/6V8wkSuGgzdThkiS5lSpdptOQPD8Ak40a+7A=="
  "resolved" "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "safe-buffer" "~5.1.1"

"cookie-signature@1.0.6":
  "integrity" "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="
  "resolved" "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@0.3.1":
  "integrity" "sha1-5+Ch+e9DtMi6klxcWpboBtFoc7s="
  "resolved" "https://registry.npmjs.org/cookie/-/cookie-0.3.1.tgz"
  "version" "0.3.1"

"copy-concurrently@^1.0.0":
  "integrity" "sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A=="
  "resolved" "https://registry.npmjs.org/copy-concurrently/-/copy-concurrently-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "aproba" "^1.1.1"
    "fs-write-stream-atomic" "^1.0.8"
    "iferr" "^0.1.5"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.0"

"copy-descriptor@^0.1.0":
  "integrity" "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="
  "resolved" "https://registry.npmjs.org/copy-descriptor/-/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"copy-text-to-clipboard@^3.0.1":
  "integrity" "sha512-RnJFp1XR/LOBDckxTib5Qjr/PMfkatD0MUCQgdpqS8MdKiNUzBjAQBEN6oUy+jW7LI93BBG3DtMB2KOOKpGs2Q=="
  "resolved" "https://registry.npmmirror.com/copy-text-to-clipboard/-/copy-text-to-clipboard-3.2.0.tgz"
  "version" "3.2.0"

"copy-webpack-plugin@^4.0.1":
  "integrity" "sha512-Y+SQCF+0NoWQryez2zXn5J5knmr9z/9qSQt7fbL78u83rxmigOy8X5+BFn8CFSuX+nKT8gpYwJX68ekqtQt6ZA=="
  "resolved" "https://registry.npmjs.org/copy-webpack-plugin/-/copy-webpack-plugin-4.6.0.tgz"
  "version" "4.6.0"
  dependencies:
    "cacache" "^10.0.4"
    "find-cache-dir" "^1.0.0"
    "globby" "^7.1.1"
    "is-glob" "^4.0.0"
    "loader-utils" "^1.1.0"
    "minimatch" "^3.0.4"
    "p-limit" "^1.0.0"
    "serialize-javascript" "^1.4.0"

"core-js@^2.2.0", "core-js@^2.4.0", "core-js@^2.5.0":
  "integrity" "sha512-05qQ5hXShcqGkPZpXEFLIpxayZscVD2kuMBZewxiIPPEagukO4mqgPA9CWhUvFBJfy3ODdK2p9xyHh7FTU9/7A=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-2.6.4.tgz"
  "version" "2.6.4"

"core-js@^3.11.0":
  "integrity" "sha512-IgdsbxNyMskrTFxa9lWHyMwAJU5gXOPP+1yO+K59d50VLVAIDAbs7gIv705KzALModfK3ZrSZTPNpC0PQgIZuw=="
  "resolved" "https://registry.npmmirror.com/core-js/-/core-js-3.35.1.tgz"
  "version" "3.35.1"

"core-util-is@~1.0.0", "core-util-is@1.0.2":
  "integrity" "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="
  "resolved" "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz"
  "version" "1.0.2"

"cosmiconfig@^2.1.0", "cosmiconfig@^2.1.1":
  "integrity" "sha512-GiNXLwAFPYHy25XmTPpafYvn3CLAkJ8FLsscq78MQd1Kh0OU6Yzhn4eV2MVF4G9WEQZoWEGltatdR+ntGPMl5A=="
  "resolved" "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "is-directory" "^0.3.1"
    "js-yaml" "^3.4.3"
    "minimist" "^1.2.0"
    "object-assign" "^4.1.0"
    "os-homedir" "^1.0.1"
    "parse-json" "^2.2.0"
    "require-from-string" "^1.1.0"

"create-ecdh@^4.0.0":
  "integrity" "sha512-GbEHQPMOswGpKXM9kCWVrremUcBmjteUaQ01T9rkKCPDXfUHX0IoP9LpHYo2NPFampa4e+/pFDc3jQdxrxQLaw=="
  "resolved" "https://registry.npmjs.org/create-ecdh/-/create-ecdh-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "elliptic" "^6.0.0"

"create-hash@^1.1.0", "create-hash@^1.1.2":
  "integrity" "sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg=="
  "resolved" "http://registry.npmjs.org/create-hash/-/create-hash-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cipher-base" "^1.0.1"
    "inherits" "^2.0.1"
    "md5.js" "^1.3.4"
    "ripemd160" "^2.0.1"
    "sha.js" "^2.4.0"

"create-hmac@^1.1.0", "create-hmac@^1.1.2", "create-hmac@^1.1.4":
  "integrity" "sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg=="
  "resolved" "http://registry.npmjs.org/create-hmac/-/create-hmac-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "cipher-base" "^1.0.3"
    "create-hash" "^1.1.0"
    "inherits" "^2.0.1"
    "ripemd160" "^2.0.0"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"cross-env@^5.0.1":
  "integrity" "sha512-jtdNFfFW1hB7sMhr/H6rW1Z45LFqyI431m3qU6bFXcQ3Eh7LtBuG3h74o7ohHZ3crrRkkqHlo4jYHFPcjroANg=="
  "resolved" "https://registry.npmjs.org/cross-env/-/cross-env-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "cross-spawn" "^6.0.5"
    "is-windows" "^1.0.0"

"cross-spawn@^5.0.1":
  "integrity" "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lru-cache" "^4.0.1"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^6.0.0":
  "integrity" "sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^6.0.5":
  "integrity" "sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cryptiles@2.x.x":
  "integrity" "sha1-O9/s3GCBR8HGcgL6KR59ylnqo7g="
  "resolved" "https://registry.npmjs.org/cryptiles/-/cryptiles-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "boom" "2.x.x"

"crypto-browserify@^3.11.0":
  "integrity" "sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg=="
  "resolved" "https://registry.npmjs.org/crypto-browserify/-/crypto-browserify-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "browserify-cipher" "^1.0.0"
    "browserify-sign" "^4.0.0"
    "create-ecdh" "^4.0.0"
    "create-hash" "^1.1.0"
    "create-hmac" "^1.1.0"
    "diffie-hellman" "^5.0.0"
    "inherits" "^2.0.1"
    "pbkdf2" "^3.0.3"
    "public-encrypt" "^4.0.0"
    "randombytes" "^2.0.0"
    "randomfill" "^1.0.3"

"css-color-names@0.0.4":
  "integrity" "sha1-gIrcLnnPhHOAabZGyyDsJ762KeA="
  "resolved" "http://registry.npmjs.org/css-color-names/-/css-color-names-0.0.4.tgz"
  "version" "0.0.4"

"css-loader@*", "css-loader@^0.28.0":
  "integrity" "sha512-wovHgjAx8ZIMGSL8pTys7edA1ClmzxHeY6n/d97gg5odgsxEgKjULPR0viqyC+FWMCL9sfqoC/QCUBo62tLvPg=="
  "resolved" "http://registry.npmjs.org/css-loader/-/css-loader-0.28.11.tgz"
  "version" "0.28.11"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "css-selector-tokenizer" "^0.7.0"
    "cssnano" "^3.10.0"
    "icss-utils" "^2.1.0"
    "loader-utils" "^1.0.2"
    "lodash.camelcase" "^4.3.0"
    "object-assign" "^4.1.1"
    "postcss" "^5.0.6"
    "postcss-modules-extract-imports" "^1.2.0"
    "postcss-modules-local-by-default" "^1.2.0"
    "postcss-modules-scope" "^1.1.0"
    "postcss-modules-values" "^1.3.0"
    "postcss-value-parser" "^3.3.0"
    "source-list-map" "^2.0.0"

"css-select@^1.1.0":
  "integrity" "sha1-KzoRBTnFNV8c2NMUYj6HCxIeyFg="
  "resolved" "https://registry.npmjs.org/css-select/-/css-select-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "boolbase" "~1.0.0"
    "css-what" "2.1"
    "domutils" "1.5.1"
    "nth-check" "~1.0.1"

"css-selector-tokenizer@^0.7.0":
  "integrity" "sha512-xYL0AMZJ4gFzJQsHUKa5jiWWi2vH77WVNg7JYRyewwj6oPh4yb/y6Y9ZCw9dsj/9UauMhtuxR+ogQd//EdEVNA=="
  "resolved" "https://registry.npmjs.org/css-selector-tokenizer/-/css-selector-tokenizer-0.7.1.tgz"
  "version" "0.7.1"
  dependencies:
    "cssesc" "^0.1.0"
    "fastparse" "^1.1.1"
    "regexpu-core" "^1.0.0"

"css-what@2.1":
  "integrity" "sha512-wan8dMWQ0GUeF7DGEPVjhHemVW/vy6xUYmFzRY8RYqgA0JtXC9rJmbScBjqSu6dg9q0lwPQy6ZAmJVr3PPTvqQ=="
  "resolved" "https://registry.npmjs.org/css-what/-/css-what-2.1.2.tgz"
  "version" "2.1.2"

"cssesc@^0.1.0":
  "integrity" "sha1-yBSQPkViM3GgR3tAEJqq++6t27Q="
  "resolved" "https://registry.npmjs.org/cssesc/-/cssesc-0.1.0.tgz"
  "version" "0.1.0"

"cssnano@^3.10.0", "cssnano@>=3.4.0":
  "integrity" "sha1-Tzj2zqK5sX+gFJDyPx3GjqZcHDg="
  "resolved" "http://registry.npmjs.org/cssnano/-/cssnano-3.10.0.tgz"
  "version" "3.10.0"
  dependencies:
    "autoprefixer" "^6.3.1"
    "decamelize" "^1.1.2"
    "defined" "^1.0.0"
    "has" "^1.0.1"
    "object-assign" "^4.0.1"
    "postcss" "^5.0.14"
    "postcss-calc" "^5.2.0"
    "postcss-colormin" "^2.1.8"
    "postcss-convert-values" "^2.3.4"
    "postcss-discard-comments" "^2.0.4"
    "postcss-discard-duplicates" "^2.0.1"
    "postcss-discard-empty" "^2.0.1"
    "postcss-discard-overridden" "^0.1.1"
    "postcss-discard-unused" "^2.2.1"
    "postcss-filter-plugins" "^2.0.0"
    "postcss-merge-idents" "^2.1.5"
    "postcss-merge-longhand" "^2.0.1"
    "postcss-merge-rules" "^2.0.3"
    "postcss-minify-font-values" "^1.0.2"
    "postcss-minify-gradients" "^1.0.1"
    "postcss-minify-params" "^1.0.4"
    "postcss-minify-selectors" "^2.0.4"
    "postcss-normalize-charset" "^1.1.0"
    "postcss-normalize-url" "^3.0.7"
    "postcss-ordered-values" "^2.1.0"
    "postcss-reduce-idents" "^2.2.2"
    "postcss-reduce-initial" "^1.0.0"
    "postcss-reduce-transforms" "^1.0.3"
    "postcss-svgo" "^2.1.1"
    "postcss-unique-selectors" "^2.0.2"
    "postcss-value-parser" "^3.2.3"
    "postcss-zindex" "^2.0.1"

"csso@~2.3.1":
  "integrity" "sha1-3dUsWHAz9J6Utx/FVWnyUuj/X4U="
  "resolved" "https://registry.npmjs.org/csso/-/csso-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "clap" "^1.0.9"
    "source-map" "^0.5.3"

"currently-unhandled@^0.4.1":
  "integrity" "sha1-mI3zP+qxke95mmE2nddsF635V+o="
  "resolved" "https://registry.npmjs.org/currently-unhandled/-/currently-unhandled-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "array-find-index" "^1.0.1"

"custom-event@~1.0.0":
  "integrity" "sha1-XQKkaFCt8bSjF5RqOSj8y1v9BCU="
  "resolved" "https://registry.npmjs.org/custom-event/-/custom-event-1.0.1.tgz"
  "version" "1.0.1"

"cyclist@~0.2.2":
  "integrity" "sha1-GzN5LhHpFKL9bW7WRHRkRE5fpkA="
  "resolved" "https://registry.npmjs.org/cyclist/-/cyclist-0.2.2.tgz"
  "version" "0.2.2"

"d@1":
  "integrity" "sha1-dUu1v+VUUdpppYuU1F9MWwRi1Y8="
  "resolved" "https://registry.npmjs.org/d/-/d-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "es5-ext" "^0.10.9"

"dashdash@^1.12.0":
  "integrity" "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA="
  "resolved" "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "assert-plus" "^1.0.0"

"data-uri-to-buffer@2":
  "integrity" "sha512-YbKCNLPPP4inc0E5If4OaalBc7gpaM2MRv77Pv2VThVComLKfbGYtJcdDCViDyp1Wd4SebhHLz94vp91zbK6bw=="
  "resolved" "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@types/node" "^8.0.7"

"date-now@^0.1.4":
  "integrity" "sha1-6vQ5/U1ISK105cx9vvIAZyueNFs="
  "resolved" "https://registry.npmjs.org/date-now/-/date-now-0.1.4.tgz"
  "version" "0.1.4"

"dateformat@^1.0.6":
  "integrity" "sha1-nxJLZ1lMk3/3BpMuSmQsyo27/uk="
  "resolved" "https://registry.npmjs.org/dateformat/-/dateformat-1.0.12.tgz"
  "version" "1.0.12"
  dependencies:
    "get-stdin" "^4.0.1"
    "meow" "^3.3.0"

"de-indent@^1.0.2":
  "integrity" "sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0="
  "resolved" "https://registry.npmjs.org/de-indent/-/de-indent-1.0.2.tgz"
  "version" "1.0.2"

"debug@^2.1.1", "debug@^2.2.0", "debug@^2.3.3", "debug@^2.6.8", "debug@^2.6.9", "debug@2", "debug@2.6.9":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.1.2":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.2.6":
  "integrity" "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz"
  "version" "3.2.6"
  dependencies:
    "ms" "^2.1.1"

"debug@=3.1.0":
  "integrity" "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-3.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "ms" "2.0.0"

"debug@2.2.0":
  "integrity" "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo="
  "resolved" "http://registry.npmjs.org/debug/-/debug-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "ms" "0.7.1"

"debug@2.3.3":
  "integrity" "sha1-QMRT5n5uE8kB3ewxeviYbNqe/4w="
  "resolved" "http://registry.npmjs.org/debug/-/debug-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "ms" "0.7.2"

"debug@2.6.8":
  "integrity" "sha1-5zFTHKLt4n0YgiJCfaF4IdaP9Pw="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.8.tgz"
  "version" "2.6.8"
  dependencies:
    "ms" "2.0.0"

"debug@4":
  "integrity" "sha512-pYAIzeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "ms" "^2.1.1"

"decamelize@^1.0.0", "decamelize@^1.1.1", "decamelize@^1.1.2", "decamelize@^1.2.0":
  "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="
  "resolved" "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decode-uri-component@^0.2.0":
  "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="
  "resolved" "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"decompress-response@^3.3.0":
  "integrity" "sha1-gKTdMjdIOEv6JICDYirt7Jgq3/M="
  "resolved" "https://registry.npmjs.org/decompress-response/-/decompress-response-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "mimic-response" "^1.0.0"

"deep-eql@^0.1.3", "deep-eql@0.1.3":
  "integrity" "sha1-71WKyrjeJSBs1xOQbXTlaTDrafI="
  "resolved" "http://registry.npmjs.org/deep-eql/-/deep-eql-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "type-detect" "0.1.1"

"deep-extend@^0.6.0":
  "integrity" "sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA=="
  "resolved" "https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz"
  "version" "0.6.0"

"deep-is@~0.1.3":
  "integrity" "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ="
  "resolved" "https://registry.npmjs.org/deep-is/-/deep-is-0.1.3.tgz"
  "version" "0.1.3"

"deepmerge@^1.2.0":
  "integrity" "sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ=="
  "resolved" "https://registry.npmmirror.com/deepmerge/-/deepmerge-1.5.2.tgz"
  "version" "1.5.2"

"define-data-property@^1.1.1":
  "integrity" "sha512-E7uGkTzkk1d0ByLeSc6ZsFS79Axg+m1P/VsgYsxHgiuc3tFSj+MjMIwe90FC4lOAZzNBdY7kkO2P2wKdsQ1vgQ=="
  "resolved" "https://registry.npmmirror.com/define-data-property/-/define-data-property-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "get-intrinsic" "^1.2.1"
    "gopd" "^1.0.1"
    "has-property-descriptors" "^1.0.0"

"define-property@^0.2.5":
  "integrity" "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="
  "resolved" "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha1-dp66rz9KY6rTr56NMEybvnm/sOY="
  "resolved" "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ=="
  "resolved" "https://registry.npmjs.org/define-property/-/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"defined@^1.0.0":
  "integrity" "sha1-yY2bzvdWdBiOEQlpFRGZ45sfppM="
  "resolved" "https://registry.npmjs.org/defined/-/defined-1.0.0.tgz"
  "version" "1.0.0"

"degenerator@~1.0.2":
  "integrity" "sha1-/PSQo37OJmRk2cxDGrmMWBnO0JU="
  "resolved" "https://registry.npmjs.org/degenerator/-/degenerator-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "ast-types" "0.x.x"
    "escodegen" "1.x.x"
    "esprima" "3.x.x"

"delayed-stream@~1.0.0":
  "integrity" "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"delegates@^1.0.0":
  "integrity" "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o="
  "resolved" "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz"
  "version" "1.0.0"

"depd@~1.1.2":
  "integrity" "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="
  "resolved" "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz"
  "version" "1.1.2"

"des.js@^1.0.0":
  "integrity" "sha1-wHTS4qpqipoH29YfmhXCzYPsjsw="
  "resolved" "https://registry.npmjs.org/des.js/-/des.js-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"destroy@~1.0.4":
  "integrity" "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="
  "resolved" "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz"
  "version" "1.0.4"

"detect-indent@^4.0.0":
  "integrity" "sha1-920GQ1LN9Docts5hnE7jqUdd4gg="
  "resolved" "https://registry.npmjs.org/detect-indent/-/detect-indent-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "repeating" "^2.0.0"

"detect-libc@^1.0.2":
  "integrity" "sha1-+hN8S9aY7fVc1c0CrFWfkaTEups="
  "resolved" "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz"
  "version" "1.0.3"

"di@^0.0.1":
  "integrity" "sha1-gGZJMmzqp8qjMG112YXqJ0i6kTw="
  "resolved" "https://registry.npmjs.org/di/-/di-0.0.1.tgz"
  "version" "0.0.1"

"diff@^3.1.0", "diff@3.2.0":
  "integrity" "sha1-yc45Okt8vQsFinJck98pkCeGj/k="
  "resolved" "https://registry.npmjs.org/diff/-/diff-3.2.0.tgz"
  "version" "3.2.0"

"diff@1.4.0":
  "integrity" "sha1-fyjS657nsVqX79ic5j3P2qPMur8="
  "resolved" "https://registry.npmjs.org/diff/-/diff-1.4.0.tgz"
  "version" "1.4.0"

"diffie-hellman@^5.0.0":
  "integrity" "sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg=="
  "resolved" "http://registry.npmjs.org/diffie-hellman/-/diffie-hellman-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "miller-rabin" "^4.0.0"
    "randombytes" "^2.0.0"

"dijkstrajs@^1.0.1":
  "integrity" "sha1-082BIh4+pAdCz83lVtTpnpjdxxs="
  "resolved" "https://registry.npmjs.org/dijkstrajs/-/dijkstrajs-1.0.1.tgz"
  "version" "1.0.1"

"dir-glob@^2.0.0":
  "integrity" "sha512-f9LBi5QWzIW3I6e//uxZoLBlUt9kcp66qo0sSCxL6YZKc75R1c4MFCoe/LaZiBGmgujvQdxc5Bn3QhfyvK5Hsw=="
  "resolved" "https://registry.npmjs.org/dir-glob/-/dir-glob-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "path-type" "^3.0.0"

"doctrine@^2.0.0":
  "integrity" "sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw=="
  "resolved" "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esutils" "^2.0.2"

"dom-converter@~0.2":
  "integrity" "sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA=="
  "resolved" "https://registry.npmjs.org/dom-converter/-/dom-converter-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "utila" "~0.4"

"dom-serialize@^2.2.0":
  "integrity" "sha1-ViromZ9Evl6jB29UGdzVnrQ6yVs="
  "resolved" "https://registry.npmjs.org/dom-serialize/-/dom-serialize-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "custom-event" "~1.0.0"
    "ent" "~2.2.0"
    "extend" "^3.0.0"
    "void-elements" "^2.0.0"

"dom-serializer@0":
  "integrity" "sha1-BzxpdUbOB4DOI75KKOKT5AvDDII="
  "resolved" "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "domelementtype" "~1.1.1"
    "entities" "~1.1.1"

"domain-browser@^1.1.1":
  "integrity" "sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA=="
  "resolved" "https://registry.npmjs.org/domain-browser/-/domain-browser-1.2.0.tgz"
  "version" "1.2.0"

"domelementtype@^1.3.0", "domelementtype@1":
  "integrity" "sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w=="
  "resolved" "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domelementtype@~1.1.1":
  "integrity" "sha1-vSh3PiZCiBrsUVRJJCmcXNgiGFs="
  "resolved" "https://registry.npmjs.org/domelementtype/-/domelementtype-1.1.3.tgz"
  "version" "1.1.3"

"domhandler@^2.3.0":
  "integrity" "sha512-JiK04h0Ht5u/80fdLMCEmV4zkNh2BcoMFBmZ/91WtYZ8qVXSKjiw7fXMgFPnHcSZgOo3XdinHvmnDUeMf5R4wA=="
  "resolved" "https://registry.npmjs.org/domhandler/-/domhandler-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "domelementtype" "1"

"domhandler@2.1":
  "integrity" "sha1-0mRvXlf2w7qxHPbLBdPArPdBJZQ="
  "resolved" "https://registry.npmjs.org/domhandler/-/domhandler-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "domelementtype" "1"

"domutils@^1.5.1":
  "integrity" "sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg=="
  "resolved" "https://registry.npmjs.org/domutils/-/domutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"domutils@1.1":
  "integrity" "sha1-vdw94Jm5ou+sxRxiPyj0FuzFdIU="
  "resolved" "https://registry.npmjs.org/domutils/-/domutils-1.1.6.tgz"
  "version" "1.1.6"
  dependencies:
    "domelementtype" "1"

"domutils@1.5.1":
  "integrity" "sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8="
  "resolved" "https://registry.npmjs.org/domutils/-/domutils-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"duplexer@^0.1.1":
  "integrity" "sha1-rOb/gIwc5mtX0ev5eXessCM0z8E="
  "resolved" "http://registry.npmjs.org/duplexer/-/duplexer-0.1.1.tgz"
  "version" "0.1.1"

"duplexer3@^0.1.4":
  "integrity" "sha1-7gHdHKwO08vH/b6jfcCo8c4ALOI="
  "resolved" "https://registry.npmjs.org/duplexer3/-/duplexer3-0.1.4.tgz"
  "version" "0.1.4"

"duplexify@^3.4.2", "duplexify@^3.6.0":
  "integrity" "sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g=="
  "resolved" "https://registry.npmjs.org/duplexify/-/duplexify-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "end-of-stream" "^1.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"
    "stream-shift" "^1.0.0"

"ecc-jsbn@~0.1.1":
  "integrity" "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk="
  "resolved" "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.1.0"

"ee-first@1.1.1":
  "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
  "resolved" "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"ejs@^2.5.7", "ejs@2.5.7":
  "integrity" "sha1-zIcsFoiArjxxiXYv1f/ACJbJUYo="
  "resolved" "https://registry.npmjs.org/ejs/-/ejs-2.5.7.tgz"
  "version" "2.5.7"

"electron-to-chromium@^1.2.7", "electron-to-chromium@^1.3.30", "electron-to-chromium@^1.3.47":
  "integrity" "sha512-De+lPAxEcpxvqPTyZAXELNpRZXABRxf+uL/rSykstQhzj/B0l1150G/ExIIxKc16lI89Hgz81J0BHAcbTqK49g=="
  "resolved" "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.3.113.tgz"
  "version" "1.3.113"

"element-ui@^2.15.14":
  "integrity" "sha512-2v9fHL0ZGINotOlRIAJD5YuVB8V7WKxrE9Qy7dXhRipa035+kF7WuU/z+tEmLVPBcJ0zt8mOu1DKpWcVzBK8IA=="
  "resolved" "https://registry.npmmirror.com/element-ui/-/element-ui-2.15.14.tgz"
  "version" "2.15.14"
  dependencies:
    "async-validator" "~1.8.1"
    "babel-helper-vue-jsx-merge-props" "^2.0.0"
    "deepmerge" "^1.2.0"
    "normalize-wheel" "^1.0.1"
    "resize-observer-polyfill" "^1.5.0"
    "throttle-debounce" "^1.0.1"

"elliptic@^6.0.0":
  "integrity" "sha512-BsXLz5sqX8OHcsh7CqBMztyXARmGQ3LWPtGjJi6DiJHq5C/qvi9P3OqgswKSDftbu8+IoI/QDTAm2fFnQ9SZSQ=="
  "resolved" "https://registry.npmjs.org/elliptic/-/elliptic-6.4.1.tgz"
  "version" "6.4.1"
  dependencies:
    "bn.js" "^4.4.0"
    "brorand" "^1.0.1"
    "hash.js" "^1.0.0"
    "hmac-drbg" "^1.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.0"

"emojis-list@^2.0.0":
  "integrity" "sha1-TapNnbAPmBmIDHn6RXrlsJof04k="
  "resolved" "https://registry.npmjs.org/emojis-list/-/emojis-list-2.1.0.tgz"
  "version" "2.1.0"

"encodeurl@~1.0.1", "encodeurl@~1.0.2":
  "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
  "resolved" "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"end-of-stream@^1.0.0", "end-of-stream@^1.1.0":
  "integrity" "sha512-1MkrZNvWTKCaigbn+W15elq2BB/L22nqrSY5DKlo3X6+vclJm8Bb5djXJBmEX6fS3+zCh/F4VBK5Z2KxJt4s2Q=="
  "resolved" "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "once" "^1.4.0"

"engine.io-client@1.8.3":
  "integrity" "sha1-F5jtk0USRkU9TG9jXXogH+lA1as="
  "resolved" "https://registry.npmjs.org/engine.io-client/-/engine.io-client-1.8.3.tgz"
  "version" "1.8.3"
  dependencies:
    "component-emitter" "1.2.1"
    "component-inherit" "0.0.3"
    "debug" "2.3.3"
    "engine.io-parser" "1.3.2"
    "has-cors" "1.1.0"
    "indexof" "0.0.1"
    "parsejson" "0.0.3"
    "parseqs" "0.0.5"
    "parseuri" "0.0.5"
    "ws" "1.1.2"
    "xmlhttprequest-ssl" "1.5.3"
    "yeast" "0.1.2"

"engine.io-parser@1.3.2":
  "integrity" "sha1-k3sHnwAH0Ik+xW1GyyILjLQ1Igo="
  "resolved" "https://registry.npmjs.org/engine.io-parser/-/engine.io-parser-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "after" "0.8.2"
    "arraybuffer.slice" "0.0.6"
    "base64-arraybuffer" "0.1.5"
    "blob" "0.0.4"
    "has-binary" "0.1.7"
    "wtf-8" "1.0.0"

"engine.io@1.8.3":
  "integrity" "sha1-jef5eJXSDTm4X4ju7nd7K9QrE9Q="
  "resolved" "https://registry.npmjs.org/engine.io/-/engine.io-1.8.3.tgz"
  "version" "1.8.3"
  dependencies:
    "accepts" "1.3.3"
    "base64id" "1.0.0"
    "cookie" "0.3.1"
    "debug" "2.3.3"
    "engine.io-parser" "1.3.2"
    "ws" "1.1.2"

"enhanced-resolve@^3.3.0":
  "integrity" "sha1-BCHjOf1xQZs9oT0Smzl5BAIwR24="
  "resolved" "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "memory-fs" "^0.4.0"
    "object-assign" "^4.0.1"
    "tapable" "^0.2.7"

"ent@~2.2.0":
  "integrity" "sha1-6WQhkyWiHQX0RGai9obtbOX13R0="
  "resolved" "https://registry.npmjs.org/ent/-/ent-2.2.0.tgz"
  "version" "2.2.0"

"entities@^1.1.1", "entities@~1.1.1":
  "integrity" "sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w=="
  "resolved" "https://registry.npmjs.org/entities/-/entities-1.1.2.tgz"
  "version" "1.1.2"

"errno@^0.1.1", "errno@^0.1.3":
  "integrity" "sha512-MfrRBDWzIWifgq6tJj60gkAwtLNb6sQPlcFrSOflcP1aFmmruKQ2wRnze/8V6kgyz7H3FF8Npzv78mZ7XLLflg=="
  "resolved" "https://registry.npmjs.org/errno/-/errno-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "prr" "~1.0.1"

"error-ex@^1.2.0":
  "integrity" "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="
  "resolved" "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.0":
  "integrity" "sha512-E1fPutRDdIj/hohG0UpT5mayXNCxXP9d+snxFsPU9X0XgccOumKraa3juDMwTUyi7+Bu5+mCGagjg4IYeNbOdw=="
  "resolved" "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "stackframe" "^1.0.4"

"es5-ext@^0.10.14", "es5-ext@^0.10.35", "es5-ext@^0.10.9", "es5-ext@~0.10.14":
  "integrity" "sha512-/1TItLfj+TTfWoeRcDn/0FbGV6SNo4R+On2GGVucPU/j3BWnXE2Co8h8CTo4Tu34gFJtnmwS9xiScKs4EjZhdw=="
  "resolved" "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.47.tgz"
  "version" "0.10.47"
  dependencies:
    "es6-iterator" "~2.0.3"
    "es6-symbol" "~3.1.1"
    "next-tick" "1"

"es6-iterator@^2.0.1", "es6-iterator@~2.0.1", "es6-iterator@~2.0.3":
  "integrity" "sha1-p96IkUGgWpSwhUQDstCg+/qY87c="
  "resolved" "https://registry.npmjs.org/es6-iterator/-/es6-iterator-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "d" "1"
    "es5-ext" "^0.10.35"
    "es6-symbol" "^3.1.1"

"es6-map@^0.1.3":
  "integrity" "sha1-kTbgUD3MBqMBaQ8LsU/042TpSfA="
  "resolved" "https://registry.npmjs.org/es6-map/-/es6-map-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"
    "es6-iterator" "~2.0.1"
    "es6-set" "~0.1.5"
    "es6-symbol" "~3.1.1"
    "event-emitter" "~0.3.5"

"es6-promise@^4.0.3":
  "integrity" "sha512-4QUVu8ljexIQebW0h+5EhEqVKvh8p7Wu3zi3AqPmX3tFL2bf6MnZ2ytC/3xuUt1mo6kE2GSYNmjWyDo2SjkAsg=="
  "resolved" "https://registry.npmmirror.com/es6-promise/-/es6-promise-4.0.5.tgz"
  "version" "4.0.5"

"es6-set@~0.1.5":
  "integrity" "sha1-0rPsXU2ADO2BjbU40ol02wpzzLE="
  "resolved" "https://registry.npmjs.org/es6-set/-/es6-set-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"
    "es6-iterator" "~2.0.1"
    "es6-symbol" "3.1.1"
    "event-emitter" "~0.3.5"

"es6-symbol@^3.1.1", "es6-symbol@~3.1.1", "es6-symbol@3.1.1":
  "integrity" "sha1-vwDvT9q2uhtG7Le2KbTH7VcVzHc="
  "resolved" "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"

"es6-weak-map@^2.0.1":
  "integrity" "sha1-XjqzIlH/0VOKH45f+hNXdy+S2W8="
  "resolved" "https://registry.npmjs.org/es6-weak-map/-/es6-weak-map-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "d" "1"
    "es5-ext" "^0.10.14"
    "es6-iterator" "^2.0.1"
    "es6-symbol" "^3.1.1"

"escape-html@~1.0.3":
  "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
  "resolved" "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.2", "escape-string-regexp@^1.0.3", "escape-string-regexp@^1.0.5", "escape-string-regexp@1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escodegen@1.8.x", "escodegen@1.x.x":
  "integrity" "sha1-WltTr0aTEQvrsIZ6o0MN07cKEBg="
  "resolved" "https://registry.npmjs.org/escodegen/-/escodegen-1.8.1.tgz"
  "version" "1.8.1"
  dependencies:
    "esprima" "^2.7.1"
    "estraverse" "^1.9.1"
    "esutils" "^2.0.2"
    "optionator" "^0.8.1"
  optionalDependencies:
    "source-map" "~0.2.0"

"escope@^3.6.0":
  "integrity" "sha1-4Bl16BJ4GhY6ba392AOY3GTIicM="
  "resolved" "https://registry.npmjs.org/escope/-/escope-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "es6-map" "^0.1.3"
    "es6-weak-map" "^2.0.1"
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"eslint-config-standard@^6.2.1":
  "integrity" "sha1-06aKr8cZFjnn7kQec0hzkCY1QpI="
  "resolved" "http://registry.npmjs.org/eslint-config-standard/-/eslint-config-standard-6.2.1.tgz"
  "version" "6.2.1"

"eslint-friendly-formatter@^3.0.0":
  "integrity" "sha1-J4h0Q1psRuwdlPoLH/SU4w7wQpA="
  "resolved" "https://registry.npmjs.org/eslint-friendly-formatter/-/eslint-friendly-formatter-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "chalk" "^1.0.0"
    "coalescy" "1.0.0"
    "extend" "^3.0.0"
    "minimist" "^1.2.0"
    "text-table" "^0.2.0"

"eslint-loader@^1.7.1":
  "integrity" "sha512-40aN976qSNPyb9ejTqjEthZITpls1SVKtwguahmH1dzGCwQU/vySE+xX33VZmD8csU0ahVNCtFlsPgKqRBiqgg=="
  "resolved" "https://registry.npmjs.org/eslint-loader/-/eslint-loader-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "loader-fs-cache" "^1.0.0"
    "loader-utils" "^1.0.2"
    "object-assign" "^4.0.1"
    "object-hash" "^1.1.4"
    "rimraf" "^2.6.1"

"eslint-plugin-html@^3.0.0":
  "integrity" "sha512-sSuafathF6ImPrzF2vUKEJY6Llq06d/riMTMzlsruDRDhNsQMYp2viUKo+jx+JRr1QevskeUpQcuptp2gN1XVQ=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-html/-/eslint-plugin-html-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "htmlparser2" "^3.8.2"
    "semver" "^5.4.1"

"eslint-plugin-promise@^3.4.0", "eslint-plugin-promise@>=3.3.0":
  "integrity" "sha512-JiFL9UFR15NKpHyGii1ZcvmtIqa3UTwiDAGb8atSffe43qJ3+1czVGN6UtkklpcJ2DVnqvTMzEKRaJdBkAL2aQ=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-promise/-/eslint-plugin-promise-3.8.0.tgz"
  "version" "3.8.0"

"eslint-plugin-standard@^2.0.1", "eslint-plugin-standard@>=2.0.0":
  "integrity" "sha1-Z2W9Km2ezce98bFFrkuzDit7hvg="
  "resolved" "https://registry.npmjs.org/eslint-plugin-standard/-/eslint-plugin-standard-2.3.1.tgz"
  "version" "2.3.1"

"eslint@^3.19.0", "eslint@>=1.6.0 <5.0.0", "eslint@>=3.0.0", "eslint@>=3.8.1":
  "integrity" "sha1-yPxiAcf0DdCJQbh8CFdnOGpnmsw="
  "resolved" "https://registry.npmjs.org/eslint/-/eslint-3.19.0.tgz"
  "version" "3.19.0"
  dependencies:
    "babel-code-frame" "^6.16.0"
    "chalk" "^1.1.3"
    "concat-stream" "^1.5.2"
    "debug" "^2.1.1"
    "doctrine" "^2.0.0"
    "escope" "^3.6.0"
    "espree" "^3.4.0"
    "esquery" "^1.0.0"
    "estraverse" "^4.2.0"
    "esutils" "^2.0.2"
    "file-entry-cache" "^2.0.0"
    "glob" "^7.0.3"
    "globals" "^9.14.0"
    "ignore" "^3.2.0"
    "imurmurhash" "^0.1.4"
    "inquirer" "^0.12.0"
    "is-my-json-valid" "^2.10.0"
    "is-resolvable" "^1.0.0"
    "js-yaml" "^3.5.1"
    "json-stable-stringify" "^1.0.0"
    "levn" "^0.3.0"
    "lodash" "^4.0.0"
    "mkdirp" "^0.5.0"
    "natural-compare" "^1.4.0"
    "optionator" "^0.8.2"
    "path-is-inside" "^1.0.1"
    "pluralize" "^1.2.1"
    "progress" "^1.1.8"
    "require-uncached" "^1.0.2"
    "shelljs" "^0.7.5"
    "strip-bom" "^3.0.0"
    "strip-json-comments" "~2.0.1"
    "table" "^3.7.8"
    "text-table" "~0.2.0"
    "user-home" "^2.0.0"

"espree@^3.4.0":
  "integrity" "sha512-yAcIQxtmMiB/jL32dzEp2enBeidsB7xWPLNiw3IIkpVds1P+h7qF9YwJq1yUNzp2OKXgAprs4F61ih66UsoD1A=="
  "resolved" "https://registry.npmjs.org/espree/-/espree-3.5.4.tgz"
  "version" "3.5.4"
  dependencies:
    "acorn" "^5.5.0"
    "acorn-jsx" "^3.0.0"

"esprima@^2.6.0", "esprima@^2.7.1", "esprima@2.7.x":
  "integrity" "sha1-luO3DVd59q1JzQMmc9HDEnZ7pYE="
  "resolved" "https://registry.npmjs.org/esprima/-/esprima-2.7.3.tgz"
  "version" "2.7.3"

"esprima@^4.0.0":
  "integrity" "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="
  "resolved" "https://registry.npm.taobao.org/esprima/download/esprima-4.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fesprima%2Fdownload%2Fesprima-4.0.1.tgz"
  "version" "4.0.1"

"esprima@3.x.x":
  "integrity" "sha1-/cpRzuYTOJXjyI1TXOSdv/YqRjM="
  "resolved" "https://registry.npmjs.org/esprima/-/esprima-3.1.3.tgz"
  "version" "3.1.3"

"esquery@^1.0.0":
  "integrity" "sha512-SmiyZ5zIWH9VM+SRUReLS5Q8a7GxtRdxEBVZpm98rJM7Sb+A9DVCndXfkeFUd3byderg+EbDkfnevfCwynWaNA=="
  "resolved" "https://registry.npmjs.org/esquery/-/esquery-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "estraverse" "^4.0.0"

"esrecurse@^4.1.0":
  "integrity" "sha512-64RBB++fIOAXPw3P9cy89qfMlvZEXZkqqJkjqqXIvzP5ezRZjW+lPWjw35UX/3EhUPFYbg5ER4JYgDw4007/DQ=="
  "resolved" "https://registry.npmjs.org/esrecurse/-/esrecurse-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "estraverse" "^4.1.0"

"estraverse@^1.9.1":
  "integrity" "sha1-r2fy3JIlgkFZUJJgkaQAXSnJu0Q="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-1.9.3.tgz"
  "version" "1.9.3"

"estraverse@^4.0.0", "estraverse@^4.1.0", "estraverse@^4.1.1", "estraverse@^4.2.0":
  "integrity" "sha1-De4/7TH81GlhjOc0IJn8GvoL2xM="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-4.2.0.tgz"
  "version" "4.2.0"

"esutils@^2.0.2":
  "integrity" "sha1-Cr9PHKpbyx96nYrMbepPqqBLrJs="
  "resolved" "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz"
  "version" "2.0.2"

"etag@~1.8.1":
  "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
  "resolved" "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-emitter@~0.3.5":
  "integrity" "sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk="
  "resolved" "https://registry.npmjs.org/event-emitter/-/event-emitter-0.3.5.tgz"
  "version" "0.3.5"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"

"eventemitter3@^3.0.0":
  "integrity" "sha512-ivIvhpq/Y0uSjcHDcOIccjmYjGLcP09MFGE7ysAwkAvkXfpZlC985pH2/ui64DKazbTW/4kN3yqozUxlXzI6cA=="
  "resolved" "https://registry.npmjs.org/eventemitter3/-/eventemitter3-3.1.0.tgz"
  "version" "3.1.0"

"events@^3.0.0":
  "integrity" "sha512-Dc381HFWJzEOhQ+d8pkNon++bk9h6cdAoAj4iE6Q4y6xgTzySWXlKn05/TVNpjnfRqi/X0EpJEJohPjNI3zpVA=="
  "resolved" "https://registry.npmjs.org/events/-/events-3.0.0.tgz"
  "version" "3.0.0"

"eventsource-polyfill@^0.9.6":
  "integrity" "sha1-EODRh/ERsWfyj9q5GIQ859gY8Tw="
  "resolved" "https://registry.npmjs.org/eventsource-polyfill/-/eventsource-polyfill-0.9.6.tgz"
  "version" "0.9.6"

"evp_bytestokey@^1.0.0", "evp_bytestokey@^1.0.3":
  "integrity" "sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA=="
  "resolved" "https://registry.npmjs.org/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "md5.js" "^1.3.4"
    "safe-buffer" "^5.1.1"

"execa@^1.0.0":
  "integrity" "sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA=="
  "resolved" "https://registry.npmjs.org/execa/-/execa-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "cross-spawn" "^6.0.0"
    "get-stream" "^4.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"exit-hook@^1.0.0":
  "integrity" "sha1-8FyiM7SMBdVP/wd2XfhQfpXAL/g="
  "resolved" "https://registry.npmjs.org/exit-hook/-/exit-hook-1.1.1.tgz"
  "version" "1.1.1"

"expand-braces@^0.1.1":
  "integrity" "sha1-SIsdHSRRyz06axks/AMPRMWFX+o="
  "resolved" "https://registry.npmjs.org/expand-braces/-/expand-braces-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "array-slice" "^0.2.3"
    "array-unique" "^0.2.1"
    "braces" "^0.1.2"

"expand-brackets@^0.1.4":
  "integrity" "sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s="
  "resolved" "https://registry.npmjs.org/expand-brackets/-/expand-brackets-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "is-posix-bracket" "^0.1.0"

"expand-brackets@^2.1.4":
  "integrity" "sha1-t3c14xXOMPa27/D4OwQVGiJEliI="
  "resolved" "https://registry.npmjs.org/expand-brackets/-/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"expand-range@^0.1.0":
  "integrity" "sha1-TLjtoJk8pW+k9B/ELzy7TMrf8EQ="
  "resolved" "https://registry.npmjs.org/expand-range/-/expand-range-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "is-number" "^0.1.1"
    "repeat-string" "^0.2.2"

"expand-range@^1.8.1":
  "integrity" "sha1-opnv/TNf4nIeuujiV+x5ZE/IUzc="
  "resolved" "https://registry.npmjs.org/expand-range/-/expand-range-1.8.2.tgz"
  "version" "1.8.2"
  dependencies:
    "fill-range" "^2.1.0"

"express@^4.14.1", "express@^4.16.2":
  "integrity" "sha512-j12Uuyb4FMrd/qQAm6uCHAkPtO8FDTRJZBDd5D2KOL2eLaz1yUNdUB/NOIyq0iU4q4cFarsUCrnFDPBcnksuOg=="
  "resolved" "https://registry.npmjs.org/express/-/express-4.16.4.tgz"
  "version" "4.16.4"
  dependencies:
    "accepts" "~1.3.5"
    "array-flatten" "1.1.1"
    "body-parser" "1.18.3"
    "content-disposition" "0.5.2"
    "content-type" "~1.0.4"
    "cookie" "0.3.1"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "1.1.1"
    "fresh" "0.5.2"
    "merge-descriptors" "1.0.1"
    "methods" "~1.1.2"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.2"
    "path-to-regexp" "0.1.7"
    "proxy-addr" "~2.0.4"
    "qs" "6.5.2"
    "range-parser" "~1.2.0"
    "safe-buffer" "5.1.2"
    "send" "0.16.2"
    "serve-static" "1.13.2"
    "setprototypeof" "1.1.0"
    "statuses" "~1.4.0"
    "type-is" "~1.6.16"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"extend-shallow@^2.0.1":
  "integrity" "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="
  "resolved" "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0", "extend-shallow@^3.0.2":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "https://registry.npmjs.org/extend-shallow/-/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend@^3.0.0", "extend@~3.0.0", "extend@~3.0.2", "extend@3":
  "integrity" "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="
  "resolved" "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  "version" "3.0.2"

"extglob@^0.3.1":
  "integrity" "sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE="
  "resolved" "https://registry.npmjs.org/extglob/-/extglob-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "is-extglob" "^1.0.0"

"extglob@^2.0.4":
  "integrity" "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM="
  "resolved" "https://registry.npm.taobao.org/extglob/download/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"extract-text-webpack-plugin@^2.0.0":
  "integrity" "sha1-dW7076gVXDaBgz+8NNpTuUF0bWw="
  "resolved" "https://registry.npmjs.org/extract-text-webpack-plugin/-/extract-text-webpack-plugin-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "async" "^2.1.2"
    "loader-utils" "^1.0.2"
    "schema-utils" "^0.3.0"
    "webpack-sources" "^1.0.1"

"extract-zip@^1.6.5":
  "integrity" "sha512-xoh5G1W/PB0/27lXgMQyIhP5DSY/LhoCsOyZgb+6iMmRtCwVBo55uKaMoEYrDCKQhWvqEip5ZPKAc6eFNyf/MA=="
  "resolved" "https://registry.npmmirror.com/extract-zip/-/extract-zip-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "concat-stream" "^1.6.2"
    "debug" "^2.6.9"
    "mkdirp" "^0.5.4"
    "yauzl" "^2.10.0"

"extsprintf@^1.2.0", "extsprintf@1.3.0":
  "integrity" "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="
  "resolved" "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz"
  "version" "1.3.0"

"fast-deep-equal@^1.0.0":
  "integrity" "sha1-wFNHeBfIa1HaqFPIHgWbcz0CNhQ="
  "resolved" "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-1.1.0.tgz"
  "version" "1.1.0"

"fast-deep-equal@^3.1.1":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha1-1RQsDK7msRifh9OnYREGT4bIu/I="
  "resolved" "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz"
  "version" "2.0.0"

"fast-levenshtein@~2.0.4":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
  "resolved" "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastparse@^1.1.1":
  "integrity" "sha512-483XLLxTVIwWK3QTrMGRqUfUpoOs/0hbQrl2oz4J0pAcm3A3bu84wxTFqGqkJzewCLdME38xJLJAxBABfQT8sQ=="
  "resolved" "https://registry.npmjs.org/fastparse/-/fastparse-1.1.2.tgz"
  "version" "1.1.2"

"fd-slicer@~1.1.0":
  "integrity" "sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g=="
  "resolved" "https://registry.npmmirror.com/fd-slicer/-/fd-slicer-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "pend" "~1.2.0"

"figures@^1.3.5":
  "integrity" "sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4="
  "resolved" "https://registry.npmjs.org/figures/-/figures-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"
    "object-assign" "^4.1.0"

"file-entry-cache@^2.0.0":
  "integrity" "sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E="
  "resolved" "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "flat-cache" "^1.2.1"
    "object-assign" "^4.0.1"

"file-loader@*", "file-loader@^0.11.1":
  "integrity" "sha512-N+uhF3mswIFeziHQjGScJ/yHXYt3DiLBeC+9vWW+WjUBiClMSOlV1YrXQi+7KM2aA3Rn4Bybgv+uXFQbfkzpvg=="
  "resolved" "https://registry.npmjs.org/file-loader/-/file-loader-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "loader-utils" "^1.0.2"

"file-uri-to-path@1":
  "integrity" "sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw=="
  "resolved" "https://registry.npmjs.org/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz"
  "version" "1.0.0"

"filename-regex@^2.0.0":
  "integrity" "sha1-wcS5vuPglyXdsQa3XB4wH+LxiyY="
  "resolved" "https://registry.npmjs.org/filename-regex/-/filename-regex-2.0.1.tgz"
  "version" "2.0.1"

"filesize@^3.5.11":
  "integrity" "sha512-7KjR1vv6qnicaPMi1iiTcI85CyYwRO/PSFCu6SvqL8jN2Wjt/NIYQTFtFs7fSDCYOstUkEWIQGFUg5YZQfjlcg=="
  "resolved" "https://registry.npmjs.org/filesize/-/filesize-3.6.1.tgz"
  "version" "3.6.1"

"fill-range@^2.1.0":
  "integrity" "sha512-cnrcCbj01+j2gTG921VZPnHbjmdAf8oQV/iGeV2kZxGSyfYjjTyY79ErsK1WJWMpw6DaApEX72binqJE+/d+5Q=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "is-number" "^2.1.0"
    "isobject" "^2.0.0"
    "randomatic" "^3.0.0"
    "repeat-element" "^1.1.2"
    "repeat-string" "^1.5.2"

"fill-range@^4.0.0":
  "integrity" "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"finalhandler@1.1.0":
  "integrity" "sha1-zgtoVbRYU+eRsvzGgARtiCU91/U="
  "resolved" "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.1"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.2"
    "statuses" "~1.3.1"
    "unpipe" "~1.0.0"

"finalhandler@1.1.1":
  "integrity" "sha512-Y1GUDo39ez4aHAw7MysnUD5JzYX+WaIj8I57kO3aEPT1fFRL4sr7mjei97FgnwhAyyzRYmQZaTHb2+9uZ1dPtg=="
  "resolved" "http://registry.npmjs.org/finalhandler/-/finalhandler-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.2"
    "statuses" "~1.4.0"
    "unpipe" "~1.0.0"

"find-cache-dir@^0.1.1":
  "integrity" "sha1-yN765XyKUqinhPnjHFfHQumToLk="
  "resolved" "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "commondir" "^1.0.1"
    "mkdirp" "^0.5.1"
    "pkg-dir" "^1.0.0"

"find-cache-dir@^1.0.0":
  "integrity" "sha1-kojj6ePMN0hxfTnq3hfPcfww7m8="
  "resolved" "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^1.0.0"
    "pkg-dir" "^2.0.0"

"find-up@^1.0.0":
  "integrity" "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "path-exists" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"find-up@^2.1.0":
  "integrity" "sha1-RdG35QbHF93UgndaK3eSCjwMV6c="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "locate-path" "^2.0.0"

"find-up@^3.0.0":
  "integrity" "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "locate-path" "^3.0.0"

"flat-cache@^1.2.1":
  "integrity" "sha512-VwyB3Lkgacfik2vhqR4uv2rvebqmDvFu4jlN/C1RzWoJEo8I7z4Q404oiqYCkq41mni8EzQnm95emU9seckwtg=="
  "resolved" "https://registry.npmjs.org/flat-cache/-/flat-cache-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "circular-json" "^0.3.1"
    "graceful-fs" "^4.1.2"
    "rimraf" "~2.6.2"
    "write" "^0.2.1"

"flatpickr@^4.6.6":
  "integrity" "sha512-97PMG/aywoYpB4IvbvUJi0RQi8vearvU0oov1WW3k0WZPBMrTQVqekSX5CjSG/M4Q3i6A/0FKXC7RyAoAUUSPw=="
  "resolved" "https://registry.npmjs.org/flatpickr/-/flatpickr-4.6.13.tgz"
  "version" "4.6.13"

"flatten@^1.0.2":
  "integrity" "sha1-2uRqnXj74lKSJYzB54CkHZXAN4I="
  "resolved" "https://registry.npmjs.org/flatten/-/flatten-1.0.2.tgz"
  "version" "1.0.2"

"flush-write-stream@^1.0.0":
  "integrity" "sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w=="
  "resolved" "https://registry.npmjs.org/flush-write-stream/-/flush-write-stream-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "inherits" "^2.0.3"
    "readable-stream" "^2.3.6"

"follow-redirects@^1.0.0":
  "integrity" "sha512-m/pZQy4Gj287eNy94nivy5wchN3Kp+Q5WgUPNy5lJSZ3sgkVKSYV/ZChMAQVIgx1SqfZ2zBZtPA2YlXIWxxJOQ=="
  "resolved" "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "debug" "^3.2.6"

"follow-redirects@1.5.10":
  "integrity" "sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio="
  "resolved" "https://registry.npm.taobao.org/follow-redirects/download/follow-redirects-1.5.10.tgz"
  "version" "1.5.10"
  dependencies:
    "debug" "=3.1.0"

"for-in@^1.0.1", "for-in@^1.0.2":
  "integrity" "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="
  "resolved" "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz"
  "version" "1.0.2"

"for-own@^0.1.4":
  "integrity" "sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4="
  "resolved" "https://registry.npmjs.org/for-own/-/for-own-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "for-in" "^1.0.1"

"forever-agent@~0.6.1":
  "integrity" "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="
  "resolved" "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz"
  "version" "0.6.1"

"form-data@~2.1.1":
  "integrity" "sha1-M8GDrPGTJ27KqYFDpp6Uv+4XUNE="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.5"
    "mime-types" "^2.1.12"

"form-data@~2.3.2":
  "integrity" "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ=="
  "resolved" "https://registry.npmmirror.com/form-data/-/form-data-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.6"
    "mime-types" "^2.1.12"

"formatio@1.2.0":
  "integrity" "sha1-87IWfZBoxGmKjVH092CjmlTYGOs="
  "resolved" "https://registry.npmjs.org/formatio/-/formatio-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "samsam" "1.x"

"forwarded@~0.1.2":
  "integrity" "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ="
  "resolved" "https://registry.npmjs.org/forwarded/-/forwarded-0.1.2.tgz"
  "version" "0.1.2"

"fragment-cache@^0.2.1":
  "integrity" "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="
  "resolved" "https://registry.npmjs.org/fragment-cache/-/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fresh@0.5.2":
  "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
  "resolved" "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  "version" "0.5.2"

"friendly-errors-webpack-plugin@^1.1.3":
  "integrity" "sha512-K27M3VK30wVoOarP651zDmb93R9zF28usW4ocaK3mfQeIEI5BPht/EzZs5E8QLLwbLRJQMwscAjDxYPb1FuNiw=="
  "resolved" "https://registry.npmjs.org/friendly-errors-webpack-plugin/-/friendly-errors-webpack-plugin-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "chalk" "^1.1.3"
    "error-stack-parser" "^2.0.0"
    "string-width" "^2.0.0"

"from2@^2.1.0", "from2@^2.1.1":
  "integrity" "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8="
  "resolved" "https://registry.npmjs.org/from2/-/from2-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"

"fs-extra@^1.0.0":
  "integrity" "sha512-VerQV6vEKuhDWD2HGOybV6v5I73syoc/cXAbKlgTC7M/oFVEtklWlp9QH2Ijw3IaWDOQcMkldSPa7zXy79Z/UQ=="
  "resolved" "https://registry.npmmirror.com/fs-extra/-/fs-extra-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "jsonfile" "^2.1.0"
    "klaw" "^1.0.0"

"fs-minipass@^1.2.5":
  "integrity" "sha512-JhBl0skXjUPCFH7x6x61gQxrKyXsxB5gcgePLZCwfyCGGsTISMoIeObbrvVeP6Xmyaudw4TT43qV2Gz+iyd2oQ=="
  "resolved" "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.2.5.tgz"
  "version" "1.2.5"
  dependencies:
    "minipass" "^2.2.1"

"fs-write-stream-atomic@^1.0.8":
  "integrity" "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk="
  "resolved" "https://registry.npmjs.org/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "graceful-fs" "^4.1.2"
    "iferr" "^0.1.5"
    "imurmurhash" "^0.1.4"
    "readable-stream" "1 || 2"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"fsevents@^1.0.0", "fsevents@^1.2.7":
  "integrity" "sha512-Pxm6sI2MeBD7RdD12RYsqaP0nMiwx8eZBXCa6z2L+mRHm2DYrOYwihmhjpkdjUHwQhslWQjRpEgNq4XvBmaAuw=="
  "resolved" "https://registry.npmjs.org/fsevents/-/fsevents-1.2.7.tgz"
  "version" "1.2.7"
  dependencies:
    "nan" "^2.9.2"
    "node-pre-gyp" "^0.10.0"

"ftp@~0.3.10":
  "integrity" "sha1-kZfYYa2BQvPmPVqDv+TFn3MwiF0="
  "resolved" "https://registry.npmjs.org/ftp/-/ftp-0.3.10.tgz"
  "version" "0.3.10"
  dependencies:
    "readable-stream" "1.1.x"
    "xregexp" "2.0.0"

"function-bind@^1.1.1":
  "integrity" "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="
  "resolved" "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"function-bind@^1.1.2":
  "integrity" "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="
  "resolved" "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz"
  "version" "1.1.2"

"gauge@~2.7.3":
  "integrity" "sha1-LANAXHU4w51+s3sxcCLjJfsBi/c="
  "resolved" "https://registry.npmjs.org/gauge/-/gauge-2.7.4.tgz"
  "version" "2.7.4"
  dependencies:
    "aproba" "^1.0.3"
    "console-control-strings" "^1.0.0"
    "has-unicode" "^2.0.0"
    "object-assign" "^4.1.0"
    "signal-exit" "^3.0.0"
    "string-width" "^1.0.1"
    "strip-ansi" "^3.0.1"
    "wide-align" "^1.1.0"

"generate-function@^2.0.0":
  "integrity" "sha512-eeB5GfMNeevm/GRYq20ShmsaGcmI81kIX2K9XQx5miC8KdHaC6Jm0qQ8ZNeGOi7wYB8OsdxKs+Y2oVuTFuVwKQ=="
  "resolved" "https://registry.npmjs.org/generate-function/-/generate-function-2.3.1.tgz"
  "version" "2.3.1"
  dependencies:
    "is-property" "^1.0.2"

"generate-object-property@^1.1.0":
  "integrity" "sha1-nA4cQDCM6AT0eDYYuTf6iPmdUNA="
  "resolved" "https://registry.npmjs.org/generate-object-property/-/generate-object-property-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "is-property" "^1.0.0"

"get-caller-file@^1.0.1":
  "integrity" "sha512-3t6rVToeoZfYSGd8YoLFR2DJkiQrIiUrGcjvFX2mDw3bn6k2OtwHN0TNCLbBO+w8qTvimhDkv+LSscbJY1vE6w=="
  "resolved" "https://registry.npmjs.org/get-caller-file/-/get-caller-file-1.0.3.tgz"
  "version" "1.0.3"

"get-intrinsic@^1.0.2", "get-intrinsic@^1.1.3", "get-intrinsic@^1.2.1", "get-intrinsic@^1.2.2":
  "integrity" "sha512-0gSo4ml/0j98Y3lngkFEot/zhiCeWsbYIlZ+uZOVgzLyLaUw7wxUL+nCTP0XJvJg1AXulJRI3UJi8GsbDuxdGA=="
  "resolved" "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "function-bind" "^1.1.2"
    "has-proto" "^1.0.1"
    "has-symbols" "^1.0.3"
    "hasown" "^2.0.0"

"get-stdin@^4.0.1":
  "integrity" "sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4="
  "resolved" "https://registry.npmjs.org/get-stdin/-/get-stdin-4.0.1.tgz"
  "version" "4.0.1"

"get-stream@^3.0.0":
  "integrity" "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-3.0.0.tgz"
  "version" "3.0.0"

"get-stream@^4.0.0":
  "integrity" "sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w=="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@3.0.0":
  "integrity" "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-3.0.0.tgz"
  "version" "3.0.0"

"get-uri@2":
  "integrity" "sha512-x5j6Ks7FOgLD/GlvjKwgu7wdmMR55iuRHhn8hj/+gA+eSbxQvZ+AEomq+3MgVEZj1vpi738QahGbCCSIDtXtkw=="
  "resolved" "https://registry.npmjs.org/get-uri/-/get-uri-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "data-uri-to-buffer" "2"
    "debug" "4"
    "extend" "~3.0.2"
    "file-uri-to-path" "1"
    "ftp" "~0.3.10"
    "readable-stream" "3"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="
  "resolved" "https://registry.npmjs.org/get-value/-/get-value-2.0.6.tgz"
  "version" "2.0.6"

"getpass@^0.1.1":
  "integrity" "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo="
  "resolved" "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "assert-plus" "^1.0.0"

"glob-base@^0.3.0":
  "integrity" "sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q="
  "resolved" "https://registry.npmjs.org/glob-base/-/glob-base-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "glob-parent" "^2.0.0"
    "is-glob" "^2.0.0"

"glob-parent@^2.0.0":
  "integrity" "sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "is-glob" "^2.0.0"

"glob-parent@^3.1.0":
  "integrity" "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-glob" "^3.1.0"
    "path-dirname" "^1.0.0"

"glob@^5.0.15":
  "integrity" "sha1-G8k2ueAvSmA/zCIuz3Yz0wuLk7E="
  "resolved" "https://registry.npmjs.org/glob/-/glob-5.0.15.tgz"
  "version" "5.0.15"
  dependencies:
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "2 || 3"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@^7.0.0", "glob@^7.0.3", "glob@^7.1.1", "glob@^7.1.2", "glob@^7.1.3":
  "integrity" "sha512-vcfuiIxogLV4DlGBHIUOwI0IbrJ8HWPc4MU7HzviGeNho/UJDfi6B5p3sHeWIQ0KGIU0Jpxi5ZHxemQfLkkAwQ=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-7.1.3.tgz"
  "version" "7.1.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@7.0.5":
  "integrity" "sha1-tCAqaQmbu00pKnwblbZoK2fr3JU="
  "resolved" "https://registry.npmjs.org/glob/-/glob-7.0.5.tgz"
  "version" "7.0.5"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.2"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@7.1.1":
  "integrity" "sha1-gFIR3wT6rxxjo2ADBs31reULLsg="
  "resolved" "https://registry.npmjs.org/glob/-/glob-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.2"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"globals@^9.14.0", "globals@^9.18.0":
  "integrity" "sha512-S0nG3CLEQiY/ILxqtztTWH/3iRRdyBLw6KMDxnKMchrtbj2OFmehVh0WUCfW3DUrIgx/qFrJPICrq4Z4sTR9UQ=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-9.18.0.tgz"
  "version" "9.18.0"

"globby@^7.1.1":
  "integrity" "sha1-+yzP+UAfhgCUXfral0QMypcrhoA="
  "resolved" "https://registry.npmjs.org/globby/-/globby-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "array-union" "^1.0.1"
    "dir-glob" "^2.0.0"
    "glob" "^7.1.2"
    "ignore" "^3.3.5"
    "pify" "^3.0.0"
    "slash" "^1.0.0"

"gopd@^1.0.1":
  "integrity" "sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA=="
  "resolved" "https://registry.npmmirror.com/gopd/-/gopd-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "get-intrinsic" "^1.1.3"

"got@^8.0.3":
  "integrity" "sha512-qjUJ5U/hawxosMryILofZCkm3C84PLJS/0grRIpjAwu+Lkxxj5cxeCU25BG0/3mDSpXKTyZr8oh8wIgLaH0QCw=="
  "resolved" "https://registry.npmjs.org/got/-/got-8.3.2.tgz"
  "version" "8.3.2"
  dependencies:
    "@sindresorhus/is" "^0.7.0"
    "cacheable-request" "^2.1.1"
    "decompress-response" "^3.3.0"
    "duplexer3" "^0.1.4"
    "get-stream" "^3.0.0"
    "into-stream" "^3.1.0"
    "is-retry-allowed" "^1.1.0"
    "isurl" "^1.0.0-alpha5"
    "lowercase-keys" "^1.0.0"
    "mimic-response" "^1.0.0"
    "p-cancelable" "^0.4.0"
    "p-timeout" "^2.0.1"
    "pify" "^3.0.0"
    "safe-buffer" "^5.1.1"
    "timed-out" "^4.0.1"
    "url-parse-lax" "^3.0.0"
    "url-to-options" "^1.0.1"

"graceful-fs@^4.1.11", "graceful-fs@^4.1.2", "graceful-fs@^4.1.6", "graceful-fs@^4.1.9":
  "integrity" "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA=="
  "resolved" "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz"
  "version" "4.1.15"

"graceful-readlink@>= 1.0.0":
  "integrity" "sha1-TK+tdrxi8C+gObL5Tpo906ORpyU="
  "resolved" "https://registry.npmjs.org/graceful-readlink/-/graceful-readlink-1.0.1.tgz"
  "version" "1.0.1"

"growl@1.9.2":
  "integrity" "sha1-Dqd0NxXbjY3ixe3hd14bRayFwC8="
  "resolved" "https://registry.npmjs.org/growl/-/growl-1.9.2.tgz"
  "version" "1.9.2"

"gulp-insert@^0.5.0":
  "integrity" "sha1-MjE/E+SiPPWsylzl8MCAkjx3hgI="
  "resolved" "https://registry.npmjs.org/gulp-insert/-/gulp-insert-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "readable-stream" "^1.0.26-4"
    "streamqueue" "0.0.6"

"gulp-rename@^1.2.2":
  "integrity" "sha512-swzbIGb/arEoFK89tPY58vg3Ok1bw+d35PfUNwWqdo7KM4jkmuGA78JiDNqR+JeZFaeeHnRg9N7aihX3YPmsyg=="
  "resolved" "https://registry.npmjs.org/gulp-rename/-/gulp-rename-1.4.0.tgz"
  "version" "1.4.0"

"gzip-size@^4.1.0":
  "integrity" "sha1-iuCWJX6r59acRb4rZ8RIEk/7UXw="
  "resolved" "https://registry.npmjs.org/gzip-size/-/gzip-size-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "duplexer" "^0.1.1"
    "pify" "^3.0.0"

"handlebars@^4.0.1":
  "integrity" "sha1-trN8HO0DBrIh4JT8eso+wjsTG2c="
  "resolved" "https://registry.npm.taobao.org/handlebars/download/handlebars-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "neo-async" "^2.6.0"
    "optimist" "^0.6.1"
    "source-map" "^0.6.1"
  optionalDependencies:
    "uglify-js" "^3.1.4"

"har-schema@^1.0.5":
  "integrity" "sha1-0mMTX0MwfALGAq/I/pWXDAFRNp4="
  "resolved" "https://registry.npmjs.org/har-schema/-/har-schema-1.0.5.tgz"
  "version" "1.0.5"

"har-schema@^2.0.0":
  "integrity" "sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q=="
  "resolved" "https://registry.npmmirror.com/har-schema/-/har-schema-2.0.0.tgz"
  "version" "2.0.0"

"har-validator@~4.2.1":
  "integrity" "sha1-M0gdDxu/9gDdID11gSpqX7oALio="
  "resolved" "https://registry.npmjs.org/har-validator/-/har-validator-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "ajv" "^4.9.1"
    "har-schema" "^1.0.5"

"har-validator@~5.1.3":
  "integrity" "sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w=="
  "resolved" "https://registry.npmmirror.com/har-validator/-/har-validator-5.1.5.tgz"
  "version" "5.1.5"
  dependencies:
    "ajv" "^6.12.3"
    "har-schema" "^2.0.0"

"has-ansi@^2.0.0":
  "integrity" "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE="
  "resolved" "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-regex" "^2.0.0"

"has-binary@0.1.7":
  "integrity" "sha1-aOYesWIQyVRaClzOBqhzkS/h5ow="
  "resolved" "https://registry.npmjs.org/has-binary/-/has-binary-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "isarray" "0.0.1"

"has-cors@1.1.0":
  "integrity" "sha1-XkdHk/fqmEPRu5nCPu9J/xJv/zk="
  "resolved" "https://registry.npmjs.org/has-cors/-/has-cors-1.1.0.tgz"
  "version" "1.1.0"

"has-flag@^1.0.0":
  "integrity" "sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-1.0.0.tgz"
  "version" "1.0.0"

"has-flag@^3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-property-descriptors@^1.0.0", "has-property-descriptors@^1.0.1":
  "integrity" "sha512-VsX8eaIewvas0xnvinAe9bw4WfIeODpGYikiWYLH+dma0Jw6KHYqWiWfhQlgOVK8D6PvjubK5Uc4P0iIhIcNVg=="
  "resolved" "https://registry.npmmirror.com/has-property-descriptors/-/has-property-descriptors-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "get-intrinsic" "^1.2.2"

"has-proto@^1.0.1":
  "integrity" "sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg=="
  "resolved" "https://registry.npmmirror.com/has-proto/-/has-proto-1.0.1.tgz"
  "version" "1.0.1"

"has-symbol-support-x@^1.4.1":
  "integrity" "sha512-3ToOva++HaW+eCpgqZrCfN51IPB+7bJNVT6CUATzueB5Heb8o6Nam0V3HG5dlDvZU1Gn5QLcbahiKw/XVk5JJw=="
  "resolved" "https://registry.npmjs.org/has-symbol-support-x/-/has-symbol-support-x-1.4.2.tgz"
  "version" "1.4.2"

"has-symbols@^1.0.3":
  "integrity" "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A=="
  "resolved" "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz"
  "version" "1.0.3"

"has-to-string-tag-x@^1.2.0":
  "integrity" "sha512-vdbKfmw+3LoOYVr+mtxHaX5a96+0f3DljYd8JOqvOLsf5mw2Otda2qCDT9qRqLAhrjyQ0h7ual5nOiASpsGNFw=="
  "resolved" "https://registry.npmjs.org/has-to-string-tag-x/-/has-to-string-tag-x-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "has-symbol-support-x" "^1.4.1"

"has-unicode@^2.0.0":
  "integrity" "sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk="
  "resolved" "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz"
  "version" "2.0.1"

"has-value@^0.3.1":
  "integrity" "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="
  "resolved" "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="
  "resolved" "https://registry.npmjs.org/has-value/-/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha1-bWHeldkd/Km5oCCJrThL/49it3E="
  "resolved" "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="
  "resolved" "https://registry.npmjs.org/has-values/-/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"has@^1.0.1":
  "integrity" "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw=="
  "resolved" "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hash-base@^3.0.0":
  "integrity" "sha1-X8hoaEfs1zSZQDMZprCj8/auSRg="
  "resolved" "https://registry.npmjs.org/hash-base/-/hash-base-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"hash-sum@^1.0.2":
  "integrity" "sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ="
  "resolved" "https://registry.npmjs.org/hash-sum/-/hash-sum-1.0.2.tgz"
  "version" "1.0.2"

"hash.js@^1.0.0", "hash.js@^1.0.3":
  "integrity" "sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA=="
  "resolved" "https://registry.npmjs.org/hash.js/-/hash.js-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "inherits" "^2.0.3"
    "minimalistic-assert" "^1.0.1"

"hasha@^2.2.0":
  "integrity" "sha512-jZ38TU/EBiGKrmyTNNZgnvCZHNowiRI4+w/I9noMlekHTZH3KyGgvJLmhSgykeAQ9j2SYPDosM0Bg3wHfzibAQ=="
  "resolved" "https://registry.npmmirror.com/hasha/-/hasha-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "is-stream" "^1.0.1"
    "pinkie-promise" "^2.0.0"

"hasown@^2.0.0":
  "integrity" "sha512-vUptKVTpIJhcczKBbgnS+RtcuYMB8+oNzPK2/Hp3hanz8JmpATdmmgLgSaadVREkDm+e2giHwY3ZRkyjSIDDFA=="
  "resolved" "https://registry.npmmirror.com/hasown/-/hasown-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "function-bind" "^1.1.2"

"hawk@~3.1.3":
  "integrity" "sha1-B4REvXwWQLD+VA0sm3PVlnjo4cQ="
  "resolved" "https://registry.npmjs.org/hawk/-/hawk-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "boom" "2.x.x"
    "cryptiles" "2.x.x"
    "hoek" "2.x.x"
    "sntp" "1.x.x"

"he@^1.1.0", "he@1.2.x":
  "integrity" "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw=="
  "resolved" "https://registry.npmjs.org/he/-/he-1.2.0.tgz"
  "version" "1.2.0"

"he@1.1.1":
  "integrity" "sha1-k0EP0hsAlzUVH4howvJx80J+I/0="
  "resolved" "https://registry.npmjs.org/he/-/he-1.1.1.tgz"
  "version" "1.1.1"

"highcharts@^6.0.4", "highcharts@>=4.2.0":
  "integrity" "sha512-A4E89MA+kto8giic7zyLU6ZxfXnVeCUlKOyzFsah3+n4BROx4bgonl92KIBtwLud/mIWir8ahqhuhe2by9LakQ=="
  "resolved" "https://registry.npmjs.org/highcharts/-/highcharts-6.2.0.tgz"
  "version" "6.2.0"

"hmac-drbg@^1.0.0":
  "integrity" "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE="
  "resolved" "https://registry.npmjs.org/hmac-drbg/-/hmac-drbg-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "hash.js" "^1.0.3"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.1"

"hoek@2.x.x":
  "integrity" "sha1-ILt0A9POo5jpHcRxCo/xuCdKJe0="
  "resolved" "https://registry.npmjs.org/hoek/-/hoek-2.16.3.tgz"
  "version" "2.16.3"

"home-or-tmp@^2.0.0":
  "integrity" "sha1-42w/LSyufXRqhX440Y1fMqeILbg="
  "resolved" "https://registry.npmjs.org/home-or-tmp/-/home-or-tmp-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "os-homedir" "^1.0.0"
    "os-tmpdir" "^1.0.1"

"hosted-git-info@^2.1.4":
  "integrity" "sha512-7T/BxH19zbcCTa8XkMlbK5lTo1WtgkFi3GvdWEyNuc4Vex7/9Dqbnpsf4JMydcfj9HCg4zUWFTL3Za6lapg5/w=="
  "resolved" "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.7.1.tgz"
  "version" "2.7.1"

"html-comment-regex@^1.1.0":
  "integrity" "sha512-P+M65QY2JQ5Y0G9KKdlDpo0zK+/OHptU5AaBwUfAIDJZk1MYf32Frm84EcOytfJE0t5JvkAnKlmjsXDnWzCJmQ=="
  "resolved" "https://registry.npmjs.org/html-comment-regex/-/html-comment-regex-1.1.2.tgz"
  "version" "1.1.2"

"html-entities@^1.2.0":
  "integrity" "sha1-DfKTUfByEWNRXfueVUPl9u7VFi8="
  "resolved" "https://registry.npmjs.org/html-entities/-/html-entities-1.2.1.tgz"
  "version" "1.2.1"

"html-minifier@^3.2.3":
  "integrity" "sha512-LKUKwuJDhxNa3uf/LPR/KVjm/l3rBqtYeCOAekvG8F1vItxMUpueGd94i/asDDr8/1u7InxzFA5EeGjhhG5mMA=="
  "resolved" "https://registry.npmjs.org/html-minifier/-/html-minifier-3.5.21.tgz"
  "version" "3.5.21"
  dependencies:
    "camel-case" "3.0.x"
    "clean-css" "4.2.x"
    "commander" "2.17.x"
    "he" "1.2.x"
    "param-case" "2.1.x"
    "relateurl" "0.2.x"
    "uglify-js" "3.4.x"

"html-webpack-plugin@^2.28.0":
  "integrity" "sha1-f5xCG36pHsRg9WUn1430hO51N9U="
  "resolved" "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-2.30.1.tgz"
  "version" "2.30.1"
  dependencies:
    "bluebird" "^3.4.7"
    "html-minifier" "^3.2.3"
    "loader-utils" "^0.2.16"
    "lodash" "^4.17.3"
    "pretty-error" "^2.0.2"
    "toposort" "^1.0.0"

"htmlparser2@^3.8.2":
  "integrity" "sha512-J1nEUGv+MkXS0weHNWVKJJ+UrLfePxRWpN3C9bEi9fLxL2+ggW94DQvgYVXsaT30PGwYRIZKNZXuyMhp3Di4bQ=="
  "resolved" "https://registry.npmjs.org/htmlparser2/-/htmlparser2-3.10.0.tgz"
  "version" "3.10.0"
  dependencies:
    "domelementtype" "^1.3.0"
    "domhandler" "^2.3.0"
    "domutils" "^1.5.1"
    "entities" "^1.1.1"
    "inherits" "^2.0.1"
    "readable-stream" "^3.0.6"

"htmlparser2@~3.3.0":
  "integrity" "sha1-zHDQWln2VC5D8OaFyYLhTJJKnv4="
  "resolved" "https://registry.npmjs.org/htmlparser2/-/htmlparser2-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "domelementtype" "1"
    "domhandler" "2.1"
    "domutils" "1.1"
    "readable-stream" "1.0"

"http-cache-semantics@3.8.1":
  "integrity" "sha512-5ai2iksyV8ZXmnZhHH4rWPoxxistEexSi5936zIQ1bnNTW5VnA85B6P/VpXiRM017IgRvb2kKo1a//y+0wSp3w=="
  "resolved" "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.8.1.tgz"
  "version" "3.8.1"

"http-errors@~1.6.2", "http-errors@~1.6.3", "http-errors@1.6.3":
  "integrity" "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0="
  "resolved" "http://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-proxy-agent@1":
  "integrity" "sha1-zBzjjkU7+YSg93AtLdWcc9CBKEo="
  "resolved" "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "agent-base" "2"
    "debug" "2"
    "extend" "3"

"http-proxy-middleware@^0.19.1":
  "integrity" "sha1-GDx9xKoUeRUDBkmMIQza+WCApDo="
  "resolved" "https://registry.npm.taobao.org/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz?cache=0&sync_timestamp=1562701366954&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-proxy-middleware%2Fdownload%2Fhttp-proxy-middleware-0.19.1.tgz"
  "version" "0.19.1"
  dependencies:
    "http-proxy" "^1.17.0"
    "is-glob" "^4.0.0"
    "lodash" "^4.17.11"
    "micromatch" "^3.1.10"

"http-proxy@^1.13.0", "http-proxy@^1.17.0":
  "integrity" "sha512-Taqn+3nNvYRfJ3bGvKfBSRwy1v6eePlm3oc/aWVxZp57DQr5Eq3xhKJi7Z4hZpS8PC3H4qI+Yly5EmFacGuA/g=="
  "resolved" "https://registry.npmjs.org/http-proxy/-/http-proxy-1.17.0.tgz"
  "version" "1.17.0"
  dependencies:
    "eventemitter3" "^3.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"http-signature@~1.1.0":
  "integrity" "sha1-33LiZwZs0Kxn+3at+OE0qPvPkb8="
  "resolved" "https://registry.npmjs.org/http-signature/-/http-signature-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "assert-plus" "^0.2.0"
    "jsprim" "^1.2.2"
    "sshpk" "^1.7.0"

"http-signature@~1.2.0":
  "integrity" "sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ=="
  "resolved" "https://registry.npmmirror.com/http-signature/-/http-signature-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "jsprim" "^1.2.2"
    "sshpk" "^1.7.0"

"https-browserify@^1.0.0":
  "integrity" "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM="
  "resolved" "https://registry.npmjs.org/https-browserify/-/https-browserify-1.0.0.tgz"
  "version" "1.0.0"

"https-proxy-agent@1":
  "integrity" "sha1-NffabEjOTdv6JkiRrFk+5f+GceY="
  "resolved" "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "agent-base" "2"
    "debug" "2"
    "extend" "3"

"iconv-lite@^0.4.4":
  "integrity" "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"iconv-lite@0.4.23":
  "integrity" "sha512-neyTUVFtahjf0mB3dZT77u+8O0QB89jFdnBkd5P1JgYPbPaia3gXXOVL2fq8VyU2gMMD7SaN7QukTB/pmXYvDA=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.23.tgz"
  "version" "0.4.23"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"icss-replace-symbols@^1.1.0":
  "integrity" "sha1-Bupvg2ead0njhs/h/oEq5dsiPe0="
  "resolved" "https://registry.npmjs.org/icss-replace-symbols/-/icss-replace-symbols-1.1.0.tgz"
  "version" "1.1.0"

"icss-utils@^2.1.0":
  "integrity" "sha1-g/Cg7DeL8yRheLbCrZE28TWxyWI="
  "resolved" "https://registry.npmjs.org/icss-utils/-/icss-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "postcss" "^6.0.1"

"ieee754@^1.1.4":
  "integrity" "sha512-GguP+DRY+pJ3soyIiGPTvdiVXjZ+DbXOxGpXn3eMvNW4x4irjqXm4wHKscC+TfxSJ0yw/S1F24tqdMNsMZTiLA=="
  "resolved" "https://registry.npmjs.org/ieee754/-/ieee754-1.1.12.tgz"
  "version" "1.1.12"

"iferr@^0.1.5":
  "integrity" "sha1-xg7taebY/bazEEofy8ocGS3FtQE="
  "resolved" "https://registry.npmjs.org/iferr/-/iferr-0.1.5.tgz"
  "version" "0.1.5"

"ignore-walk@^3.0.1":
  "integrity" "sha512-DTVlMx3IYPe0/JJcYP7Gxg7ttZZu3IInhuEhbchuqneY9wWe5Ojy2mXLBaQFUQmo0AW2r3qG7m1mg86js+gnlQ=="
  "resolved" "https://registry.npmjs.org/ignore-walk/-/ignore-walk-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "minimatch" "^3.0.4"

"ignore@^3.2.0", "ignore@^3.3.5":
  "integrity" "sha512-Pgs951kaMm5GXP7MOvxERINe3gsaVjUWFm+UZPSq9xYriQAksyhg0csnS0KXSNRD5NmNdapXEpjxG49+AKh/ug=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-3.3.10.tgz"
  "version" "3.3.10"

"image-size@~0.5.0":
  "integrity" "sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w="
  "resolved" "https://registry.npmjs.org/image-size/-/image-size-0.5.5.tgz"
  "version" "0.5.5"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^2.1.0":
  "integrity" "sha1-ji1INIdCEhtKghi3oTfppSBJ3IA="
  "resolved" "https://registry.npmjs.org/indent-string/-/indent-string-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "repeating" "^2.0.0"

"indexes-of@^1.0.1":
  "integrity" "sha1-8w9xbI4r00bHtn0985FVZqfAVgc="
  "resolved" "https://registry.npmjs.org/indexes-of/-/indexes-of-1.0.1.tgz"
  "version" "1.0.1"

"indexof@0.0.1":
  "integrity" "sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10="
  "resolved" "https://registry.npmjs.org/indexof/-/indexof-0.0.1.tgz"
  "version" "0.0.1"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@~2.0.1", "inherits@~2.0.3", "inherits@2", "inherits@2.0.3":
  "integrity" "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz"
  "version" "2.0.3"

"inherits@2.0.1":
  "integrity" "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.1.tgz"
  "version" "2.0.1"

"ini@~1.3.0":
  "integrity" "sha512-RZY5huIKCMRWDUqZlEi72f/lmXKMvuszcMBduliQ3nnWbx9X/ZBQO7DijMEYS9EhHBb2qacRUMtC7svLwe0lcw=="
  "resolved" "https://registry.npmjs.org/ini/-/ini-1.3.5.tgz"
  "version" "1.3.5"

"inject-loader@^3.0.0":
  "integrity" "sha512-0Kd4NqMJUhknG4ECiJ/mgyHJBpfBBWZ3IKHl2BLNQiFtMO7/xiv9mmHl7mGvE0iKrBeQAZdMcQP3sMXZN0cqeg=="
  "resolved" "https://registry.npmjs.org/inject-loader/-/inject-loader-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "babel-core" "~6"

"inquirer@^0.12.0":
  "integrity" "sha1-HvK/1jUE3wvHV4X/+MLEHfEvB34="
  "resolved" "http://registry.npmjs.org/inquirer/-/inquirer-0.12.0.tgz"
  "version" "0.12.0"
  dependencies:
    "ansi-escapes" "^1.1.0"
    "ansi-regex" "^2.0.0"
    "chalk" "^1.0.0"
    "cli-cursor" "^1.0.1"
    "cli-width" "^2.0.0"
    "figures" "^1.3.5"
    "lodash" "^4.3.0"
    "readline2" "^1.0.1"
    "run-async" "^0.1.0"
    "rx-lite" "^3.1.2"
    "string-width" "^1.0.1"
    "strip-ansi" "^3.0.0"
    "through" "^2.3.6"

"interpret@^1.0.0":
  "integrity" "sha512-mT34yGKMNceBQUoVn7iCDKDntA7SC6gycMAWzGx1z/CMCTV7b2AAtXlo3nRyHZ1FelRkQbQjprHSYGwzLtkVbw=="
  "resolved" "https://registry.npmjs.org/interpret/-/interpret-1.2.0.tgz"
  "version" "1.2.0"

"into-stream@^3.1.0":
  "integrity" "sha1-lvsKk2wSur1v8XUqF9BWFqvQlMY="
  "resolved" "http://registry.npmjs.org/into-stream/-/into-stream-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "from2" "^2.1.1"
    "p-is-promise" "^1.1.0"

"invariant@^2.2.2":
  "integrity" "sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA=="
  "resolved" "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "loose-envify" "^1.0.0"

"invert-kv@^1.0.0":
  "integrity" "sha1-EEqOSqym09jNFXqO+L+rLXo//bY="
  "resolved" "https://registry.npmjs.org/invert-kv/-/invert-kv-1.0.0.tgz"
  "version" "1.0.0"

"invert-kv@^2.0.0":
  "integrity" "sha512-wPVv/y/QQ/Uiirj/vh3oP+1Ww+AWehmi1g5fFWGPF6IpCBCDVrhgHRMvrLfdYcwDh3QJbGXDW4JAuzxElLSqKA=="
  "resolved" "https://registry.npmjs.org/invert-kv/-/invert-kv-2.0.0.tgz"
  "version" "2.0.0"

"ip@^1.1.4":
  "integrity" "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo="
  "resolved" "https://registry.npmjs.org/ip/-/ip-1.1.5.tgz"
  "version" "1.1.5"

"ip@1.0.1":
  "integrity" "sha1-x+NWzeoiWucbNtcPLnGpK6TkJZA="
  "resolved" "https://registry.npmjs.org/ip/-/ip-1.0.1.tgz"
  "version" "1.0.1"

"ipaddr.js@1.8.0":
  "integrity" "sha1-6qM9bd16zo9/b+DJygRA5wZzix4="
  "resolved" "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.8.0.tgz"
  "version" "1.8.0"

"is-absolute-url@^2.0.0":
  "integrity" "sha1-UFMN+4T8yap9vnhS6Do3uTufKqY="
  "resolved" "https://registry.npmjs.org/is-absolute-url/-/is-absolute-url-2.1.0.tgz"
  "version" "2.1.0"

"is-accessor-descriptor@^0.1.6":
  "integrity" "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="
  "resolved" "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "integrity" "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ=="
  "resolved" "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="
  "resolved" "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-binary-path@^1.0.0":
  "integrity" "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg="
  "resolved" "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "binary-extensions" "^1.0.0"

"is-buffer@^1.1.5":
  "integrity" "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="
  "resolved" "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-buffer@^2.0.2":
  "integrity" "sha1-Ts8/z3ScvR5HJonhCaxmJhol5yU="
  "resolved" "http://registry.npm.taobao.org/is-buffer/download/is-buffer-2.0.3.tgz"
  "version" "2.0.3"

"is-data-descriptor@^0.1.4":
  "integrity" "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="
  "resolved" "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "integrity" "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ=="
  "resolved" "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-descriptor@^0.1.0":
  "integrity" "sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg=="
  "resolved" "https://registry.npmjs.org/is-descriptor/-/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0":
  "integrity" "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg=="
  "resolved" "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-descriptor@^1.0.2":
  "integrity" "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg=="
  "resolved" "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-directory@^0.3.1":
  "integrity" "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE="
  "resolved" "https://registry.npmjs.org/is-directory/-/is-directory-0.3.1.tgz"
  "version" "0.3.1"

"is-dotfile@^1.0.0":
  "integrity" "sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE="
  "resolved" "https://registry.npmjs.org/is-dotfile/-/is-dotfile-1.0.3.tgz"
  "version" "1.0.3"

"is-equal-shallow@^0.1.3":
  "integrity" "sha1-IjgJj8Ih3gvPpdnqxMRdY4qhxTQ="
  "resolved" "https://registry.npmjs.org/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "is-primitive" "^2.0.0"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
  "resolved" "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA=="
  "resolved" "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^1.0.0":
  "integrity" "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz"
  "version" "1.0.0"

"is-extglob@^2.1.0", "is-extglob@^2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-finite@^1.0.0":
  "integrity" "sha1-zGZ3aVYCvlUO8R6LSqYwU0K20Ko="
  "resolved" "https://registry.npmjs.org/is-finite/-/is-finite-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "number-is-nan" "^1.0.0"

"is-fullwidth-code-point@^1.0.0":
  "integrity" "sha1-754xOG8DGn8NZDr4L95QxFfvAMs="
  "resolved" "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "number-is-nan" "^1.0.0"

"is-fullwidth-code-point@^2.0.0":
  "integrity" "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="
  "resolved" "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-glob@^2.0.0", "is-glob@^2.0.1":
  "integrity" "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extglob" "^1.0.0"

"is-glob@^3.1.0":
  "integrity" "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-extglob" "^2.1.0"

"is-glob@^4.0.0":
  "integrity" "sha1-lSHHaEXMJhCoUgPd8ICpWML/q8A="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-extglob" "^2.1.1"

"is-my-ip-valid@^1.0.0":
  "integrity" "sha512-gmh/eWXROncUzRnIa1Ubrt5b8ep/MGSnfAUI3aRp+sqTCs1tv1Isl8d8F6JmkN3dXKc3ehZMrtiPN9eL03NuaQ=="
  "resolved" "https://registry.npmjs.org/is-my-ip-valid/-/is-my-ip-valid-1.0.0.tgz"
  "version" "1.0.0"

"is-my-json-valid@^2.10.0":
  "integrity" "sha512-mG0f/unGX1HZ5ep4uhRaPOS8EkAY8/j6mDRMJrutq4CqhoJWYp7qAlonIPy3TV7p3ju4TK9fo/PbnoksWmsp5Q=="
  "resolved" "https://registry.npmjs.org/is-my-json-valid/-/is-my-json-valid-2.19.0.tgz"
  "version" "2.19.0"
  dependencies:
    "generate-function" "^2.0.0"
    "generate-object-property" "^1.1.0"
    "is-my-ip-valid" "^1.0.0"
    "jsonpointer" "^4.0.0"
    "xtend" "^4.0.0"

"is-number@^0.1.1":
  "integrity" "sha1-aaevEWlj1HIG7JvZtIoUIW8eOAY="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-0.1.1.tgz"
  "version" "0.1.1"

"is-number@^2.1.0":
  "integrity" "sha1-Afy7s5NGOlSPL0ZszhbezknbkI8="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^3.0.0":
  "integrity" "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^4.0.0":
  "integrity" "sha512-rSklcAIlf1OmFdyAqbnWTLVelsQ58uvZ66S/ZyawjWqIviTWCjg2PzVGw8WUA+nNuPTqb4wgA+NszrJ+08LlgQ=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-4.0.0.tgz"
  "version" "4.0.0"

"is-object@^1.0.1":
  "integrity" "sha1-iVJojF7C/9awPsyF52ngKQMINHA="
  "resolved" "https://registry.npmjs.org/is-object/-/is-object-1.0.1.tgz"
  "version" "1.0.1"

"is-plain-obj@^1.0.0":
  "integrity" "sha1-caUMhCnfync8kqOQpKA7OfzVHT4="
  "resolved" "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og=="
  "resolved" "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-posix-bracket@^0.1.0":
  "integrity" "sha1-MzTceXdDaOkvAW5vvAqI9c1ua8Q="
  "resolved" "https://registry.npmjs.org/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz"
  "version" "0.1.1"

"is-primitive@^2.0.0":
  "integrity" "sha1-IHurkWOEmcB7Kt8kCkGochADRXU="
  "resolved" "https://registry.npmjs.org/is-primitive/-/is-primitive-2.0.0.tgz"
  "version" "2.0.0"

"is-property@^1.0.0", "is-property@^1.0.2":
  "integrity" "sha1-V/4cTkhHTt1lsJkR8msc1Ald2oQ="
  "resolved" "https://registry.npmjs.org/is-property/-/is-property-1.0.2.tgz"
  "version" "1.0.2"

"is-resolvable@^1.0.0":
  "integrity" "sha512-qgDYXFSR5WvEfuS5dMj6oTMEbrrSaM0CrFk2Yiq/gXnBvD9pMa2jGXxyhGLfvhZpuMZe18CJpFxAt3CRs42NMg=="
  "resolved" "https://registry.npmjs.org/is-resolvable/-/is-resolvable-1.1.0.tgz"
  "version" "1.1.0"

"is-retry-allowed@^1.1.0":
  "integrity" "sha1-EaBgVotnM5REAz0BJaYaINVk+zQ="
  "resolved" "https://registry.npmjs.org/is-retry-allowed/-/is-retry-allowed-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^1.0.1", "is-stream@^1.1.0":
  "integrity" "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="
  "resolved" "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-svg@^2.0.0":
  "integrity" "sha1-z2EJDaDZ77yrhyLeum8DIgjbsOk="
  "resolved" "https://registry.npmjs.org/is-svg/-/is-svg-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "html-comment-regex" "^1.1.0"

"is-typedarray@~1.0.0":
  "integrity" "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="
  "resolved" "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-utf8@^0.2.0":
  "integrity" "sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI="
  "resolved" "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz"
  "version" "0.2.1"

"is-windows@^1.0.0", "is-windows@^1.0.2":
  "integrity" "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA=="
  "resolved" "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@^1.1.0":
  "integrity" "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0="
  "resolved" "https://registry.npmjs.org/is-wsl/-/is-wsl-1.1.0.tgz"
  "version" "1.1.0"

"isarray@^1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isarray@^2.0.1":
  "integrity" "sha512-GMxXOiUirWg1xTKRipM0Ek07rX+ubx4nNVElTJdNLYmNO/2YrDkgJGw9CljXn+r4EWiDQg/8lsRdHyg2PJuUaA=="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-2.0.4.tgz"
  "version" "2.0.4"

"isarray@~1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isarray@0.0.1":
  "integrity" "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz"
  "version" "0.0.1"

"isarray@1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isbinaryfile@^3.0.0":
  "integrity" "sha512-8cJBL5tTd2OS0dM4jz07wQd5g0dCCqIhUxPIGtZfa5L6hWlvV5MHTITy/DBAsF+Oe2LS1X3krBUhNwaGUWpWxw=="
  "resolved" "https://registry.npmjs.org/isbinaryfile/-/isbinaryfile-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "buffer-alloc" "^1.2.0"

"iscroll@^5.2.0":
  "integrity" "sha1-1RMwcIi1slpPiTr0dIBEaEgYKcg="
  "resolved" "http://registry.npm.taobao.org/iscroll/download/iscroll-5.2.0.tgz"
  "version" "5.2.0"

"isexe@^2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
  "resolved" "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isobject@^3.0.1":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isstream@~0.1.2":
  "integrity" "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="
  "resolved" "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz"
  "version" "0.1.2"

"istanbul-lib-coverage@^1.2.1":
  "integrity" "sha512-PzITeunAgyGbtY1ibVIUiV679EFChHjoMNRibEIobvmrCRaIgwLxNucOSimtNWUhEib/oO7QY2imD75JVgCJWQ=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-1.2.1.tgz"
  "version" "1.2.1"

"istanbul-lib-instrument@^1.10.1":
  "integrity" "sha512-aWHxfxDqvh/ZlxR8BBaEPVSWDPUkGD63VjGQn3jcw8jCp7sHEMKcrj4xfJn/ABzdMEHiQNyvDQhqm5o8+SQg7A=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.10.2.tgz"
  "version" "1.10.2"
  dependencies:
    "babel-generator" "^6.18.0"
    "babel-template" "^6.16.0"
    "babel-traverse" "^6.18.0"
    "babel-types" "^6.18.0"
    "babylon" "^6.18.0"
    "istanbul-lib-coverage" "^1.2.1"
    "semver" "^5.3.0"

"istanbul@^0.4.0":
  "integrity" "sha1-ZcfXPUxNqE1POsMQuRj7C4Azczs="
  "resolved" "https://registry.npmjs.org/istanbul/-/istanbul-0.4.5.tgz"
  "version" "0.4.5"
  dependencies:
    "abbrev" "1.0.x"
    "async" "1.x"
    "escodegen" "1.8.x"
    "esprima" "2.7.x"
    "glob" "^5.0.15"
    "handlebars" "^4.0.1"
    "js-yaml" "3.x"
    "mkdirp" "0.5.x"
    "nopt" "3.x"
    "once" "1.x"
    "resolve" "1.1.x"
    "supports-color" "^3.1.0"
    "which" "^1.1.1"
    "wordwrap" "^1.0.0"

"isurl@^1.0.0-alpha5":
  "integrity" "sha512-1P/yWsxPlDtn7QeRD+ULKQPaIaN6yF368GZ2vDfv0AL0NwpStafjWCDDdn0k8wgFMWpVAqG7oJhxHnlud42i9w=="
  "resolved" "https://registry.npmjs.org/isurl/-/isurl-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-to-string-tag-x" "^1.2.0"
    "is-object" "^1.0.1"

"jquery@^3.3.1":
  "integrity" "sha512-m4avr8yL8kmFN8psrbFFFmB/If14iN5o9nw/NgnnM+kybDJpRsAynV2BsfpTYrTRysYUdADVD7CkUUizgkpLfg=="
  "resolved" "https://registry.npmmirror.com/jquery/-/jquery-3.7.1.tgz"
  "version" "3.7.1"

"js-base64@^2.1.9":
  "integrity" "sha512-M7kLczedRMYX4L8Mdh4MzyAMM9O5osx+4FcOQuTvr3A9F2D9S5JXheN0ewNbrvK2UatkTRhL5ejGmGSjNMiZuw=="
  "resolved" "https://registry.npmjs.org/js-base64/-/js-base64-2.5.1.tgz"
  "version" "2.5.1"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^3.0.2":
  "integrity" "sha1-mGbfOVECEw449/mWvOtlRDIJwls="
  "resolved" "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.2.tgz"
  "version" "3.0.2"

"js-yaml@^3.4.3", "js-yaml@^3.5.1", "js-yaml@3.x":
  "integrity" "sha1-r/FRswv9+o5J4F2iLnQV6d+jeEc="
  "resolved" "https://registry.npm.taobao.org/js-yaml/download/js-yaml-3.13.1.tgz"
  "version" "3.13.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"js-yaml@~3.7.0":
  "integrity" "sha1-XJZ93YN6m/3KXy3oQlOr6KHAO4A="
  "resolved" "https://registry.npm.taobao.org/js-yaml/download/js-yaml-3.7.0.tgz"
  "version" "3.7.0"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^2.6.0"

"jsbarcode@^3.9.0":
  "integrity" "sha512-/ozCd7wsa+VIHo9sUc03HneVEQrH7cVWfJolUT/WOW1m8mJ2e3iYZje6C9X3LFHdczlesqFHRpxLtbVsNtjyow=="
  "resolved" "https://registry.npmjs.org/jsbarcode/-/jsbarcode-3.11.0.tgz"
  "version" "3.11.0"

"jsbn@~0.1.0":
  "integrity" "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="
  "resolved" "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz"
  "version" "0.1.1"

"jsesc@^1.3.0":
  "integrity" "sha1-RsP+yMGJKxKwgz25vHYiF226s0s="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-1.3.0.tgz"
  "version" "1.3.0"

"jsesc@~0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-buffer@3.0.0":
  "integrity" "sha1-Wx85evx11ne96Lz8Dkfh+aPZqJg="
  "resolved" "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.0.tgz"
  "version" "3.0.0"

"json-loader@^0.5.4":
  "integrity" "sha512-QLPs8Dj7lnf3e3QYS1zkCo+4ZwqOiF9d/nZnYozTISxXWCfNs9yuky5rJw4/W34s7POaNlbZmQGaB5NiXCbP4w=="
  "resolved" "https://registry.npmjs.org/json-loader/-/json-loader-0.5.7.tgz"
  "version" "0.5.7"

"json-schema-traverse@^0.3.0":
  "integrity" "sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz"
  "version" "0.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema@0.2.3":
  "integrity" "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="
  "resolved" "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz"
  "version" "0.2.3"

"json-stable-stringify@^1.0.0", "json-stable-stringify@^1.0.1":
  "integrity" "sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8="
  "resolved" "https://registry.npmjs.org/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "jsonify" "~0.0.0"

"json-stringify-safe@~5.0.1":
  "integrity" "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="
  "resolved" "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"
  "version" "5.0.1"

"json3@3.3.2":
  "integrity" "sha1-PAQ0dD35Pi9cQq7nsZvLSDV19OE="
  "resolved" "https://registry.npmjs.org/json3/-/json3-3.3.2.tgz"
  "version" "3.3.2"

"json5@^0.5.0", "json5@^0.5.1":
  "integrity" "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE="
  "resolved" "http://registry.npmjs.org/json5/-/json5-0.5.1.tgz"
  "version" "0.5.1"

"json5@^1.0.1":
  "integrity" "sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "minimist" "^1.2.0"

"jsonfile@^2.1.0":
  "integrity" "sha512-PKllAqbgLgxHaj8TElYymKCAgrASebJrWpTnEkOaTowt23VKXXN0sUeriJ+eh7y6ufb/CC5ap11pz71/cM0hUw=="
  "resolved" "https://registry.npmmirror.com/jsonfile/-/jsonfile-2.4.0.tgz"
  "version" "2.4.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsonify@~0.0.0":
  "integrity" "sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM="
  "resolved" "https://registry.npmjs.org/jsonify/-/jsonify-0.0.0.tgz"
  "version" "0.0.0"

"jsonpointer@^4.0.0":
  "integrity" "sha1-T9kss04OnbPInIYi7PUfm5eMbLk="
  "resolved" "https://registry.npmjs.org/jsonpointer/-/jsonpointer-4.0.1.tgz"
  "version" "4.0.1"

"jsprim@^1.2.2":
  "integrity" "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI="
  "resolved" "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "assert-plus" "1.0.0"
    "extsprintf" "1.3.0"
    "json-schema" "0.2.3"
    "verror" "1.10.0"

"karma-coverage@^1.1.1":
  "integrity" "sha512-eQawj4Cl3z/CjxslYy9ariU4uDh7cCNFZHNWXWRpl0pNeblY/4wHR7M7boTYXWrn9bY0z2pZmr11eKje/S/hIw=="
  "resolved" "https://registry.npmjs.org/karma-coverage/-/karma-coverage-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "dateformat" "^1.0.6"
    "istanbul" "^0.4.0"
    "lodash" "^4.17.0"
    "minimatch" "^3.0.0"
    "source-map" "^0.5.1"

"karma-mocha@^1.3.0":
  "integrity" "sha1-7qrH/8DiAetjxGdEDStpx883eL8="
  "resolved" "https://registry.npmjs.org/karma-mocha/-/karma-mocha-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "minimist" "1.2.0"

"karma-phantomjs-launcher@^1.0.2":
  "integrity" "sha1-0jyjSAG9qYY60xjju0vUBisTrNI="
  "resolved" "https://registry.npmjs.org/karma-phantomjs-launcher/-/karma-phantomjs-launcher-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "lodash" "^4.0.1"
    "phantomjs-prebuilt" "^2.1.7"

"karma-phantomjs-shim@^1.4.0":
  "integrity" "sha512-t0h1x7btXROaGElv36TLpuoWqTnVZ/f+GJHH/qVerjbX6AENoM5brQoB9ISO3hQ6zO1k9rDSRLrY5ZZb83ANdg=="
  "resolved" "https://registry.npmjs.org/karma-phantomjs-shim/-/karma-phantomjs-shim-1.5.0.tgz"
  "version" "1.5.0"

"karma-sinon-chai@^1.3.1":
  "integrity" "sha512-Oatu8tdkfWaSveM809euI6KGcNJRdoXFilz9ozSf+vPwrM73kncu54nsfkLcMqR/iht3PXASAGK9La5oU2xDKQ=="
  "resolved" "https://registry.npmjs.org/karma-sinon-chai/-/karma-sinon-chai-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "lolex" "^1.6.0"

"karma-sourcemap-loader@^0.3.7":
  "integrity" "sha1-kTIsd/jxPUb+0GKwQuEAnUxFBdg="
  "resolved" "https://registry.npmjs.org/karma-sourcemap-loader/-/karma-sourcemap-loader-0.3.7.tgz"
  "version" "0.3.7"
  dependencies:
    "graceful-fs" "^4.1.2"

"karma-spec-reporter@0.0.31":
  "integrity" "sha1-SDDccUihVcfXoYbmMjOaDYD63sM="
  "resolved" "https://registry.npmjs.org/karma-spec-reporter/-/karma-spec-reporter-0.0.31.tgz"
  "version" "0.0.31"
  dependencies:
    "colors" "^1.1.2"

"karma-webpack@^2.0.2":
  "integrity" "sha512-2cyII34jfrAabbI2+4Rk4j95Nazl98FvZQhgSiqKUDarT317rxfv/EdzZ60CyATN4PQxJdO5ucR5bOOXkEVrXw=="
  "resolved" "http://registry.npmjs.org/karma-webpack/-/karma-webpack-2.0.13.tgz"
  "version" "2.0.13"
  dependencies:
    "async" "^2.0.0"
    "babel-runtime" "^6.0.0"
    "loader-utils" "^1.0.0"
    "lodash" "^4.0.0"
    "source-map" "^0.5.6"
    "webpack-dev-middleware" "^1.12.0"

"karma@^1.4.1", "karma@>=0.9":
  "integrity" "sha512-k5pBjHDhmkdaUccnC7gE3mBzZjcxyxYsYVaqiL2G5AqlfLyBO5nw2VdNK+O16cveEPd/gIOWULH7gkiYYwVNHg=="
  "resolved" "https://registry.npmjs.org/karma/-/karma-1.7.1.tgz"
  "version" "1.7.1"
  dependencies:
    "bluebird" "^3.3.0"
    "body-parser" "^1.16.1"
    "chokidar" "^1.4.1"
    "colors" "^1.1.0"
    "combine-lists" "^1.0.0"
    "connect" "^3.6.0"
    "core-js" "^2.2.0"
    "di" "^0.0.1"
    "dom-serialize" "^2.2.0"
    "expand-braces" "^0.1.1"
    "glob" "^7.1.1"
    "graceful-fs" "^4.1.2"
    "http-proxy" "^1.13.0"
    "isbinaryfile" "^3.0.0"
    "lodash" "^3.8.0"
    "log4js" "^0.6.31"
    "mime" "^1.3.4"
    "minimatch" "^3.0.2"
    "optimist" "^0.6.1"
    "qjobs" "^1.1.4"
    "range-parser" "^1.2.0"
    "rimraf" "^2.6.0"
    "safe-buffer" "^5.0.1"
    "socket.io" "1.7.3"
    "source-map" "^0.5.3"
    "tmp" "0.0.31"
    "useragent" "^2.1.12"

"kew@^0.7.0":
  "integrity" "sha512-IG6nm0+QtAMdXt9KvbgbGdvY50RSrw+U4sGZg+KlrSKPJEwVE5JVoI3d7RWfSMdBQneRheeAOj3lIjX5VL/9RQ=="
  "resolved" "https://registry.npmmirror.com/kew/-/kew-0.7.0.tgz"
  "version" "0.7.0"

"keyv@3.0.0":
  "integrity" "sha512-eguHnq22OE3uVoSYG0LVWNP+4ppamWr9+zWBe1bsNcovIMy6huUJFPgy4mGwCd/rnl3vOLGW1MTlu4c57CT1xA=="
  "resolved" "https://registry.npmjs.org/keyv/-/keyv-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "json-buffer" "3.0.0"

"kind-of@^3.0.2", "kind-of@^3.0.3", "kind-of@^3.2.0":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha1-IIE989cSkosgc3hpGkUGb65y3Vc="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "integrity" "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.0", "kind-of@^6.0.2":
  "integrity" "sha512-s5kLOcnH0XqDO+FvuaLX8DDjZ18CGFk7VygH40QoKPUQhW4e2rvM0rwUq0t8IQDOwYSeLK01U90OjzBTme2QqA=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-6.0.2.tgz"
  "version" "6.0.2"

"klaw@^1.0.0":
  "integrity" "sha512-TED5xi9gGQjGpNnvRWknrwAB1eL5GciPfVFOt3Vk1OJCVDQbzuSfrF3hkUQKlsgKrG1F+0t5W0m+Fje1jIt8rw=="
  "resolved" "https://registry.npmmirror.com/klaw/-/klaw-1.3.1.tgz"
  "version" "1.3.1"
  optionalDependencies:
    "graceful-fs" "^4.1.9"

"lazy-cache@^1.0.3":
  "integrity" "sha1-odePw6UEdMuAhF07O24dpJpEbo4="
  "resolved" "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz"
  "version" "1.0.4"

"lcid@^1.0.0":
  "integrity" "sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU="
  "resolved" "https://registry.npmjs.org/lcid/-/lcid-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "invert-kv" "^1.0.0"

"lcid@^2.0.0":
  "integrity" "sha512-avPEb8P8EGnwXKClwsNUgryVjllcRqtMYa49NTsbQagYuT1DcXnl1915oxWjoyGrXR6zH/Y0Zc96xWsPcoDKeA=="
  "resolved" "https://registry.npmjs.org/lcid/-/lcid-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "invert-kv" "^2.0.0"

"less-loader@^4.0.4":
  "integrity" "sha512-KNTsgCE9tMOM70+ddxp9yyt9iHqgmSs0yTZc5XH5Wo+g80RWRIYNqE58QJKm/yMud5wZEvz50ugRDuzVIkyahg=="
  "resolved" "https://registry.npmjs.org/less-loader/-/less-loader-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "clone" "^2.1.1"
    "loader-utils" "^1.1.0"
    "pify" "^3.0.0"

"less@^2.3.1 || ^3.0.0", "less@^2.7.2":
  "integrity" "sha512-KPdIJKWcEAb02TuJtaLrhue0krtRLoRoo7x6BNJIBelO00t/CCdJQUnHW5V34OnHMWzIktSalJxRO+FvytQlCQ=="
  "resolved" "https://registry.npmjs.org/less/-/less-2.7.3.tgz"
  "version" "2.7.3"
  optionalDependencies:
    "errno" "^0.1.1"
    "graceful-fs" "^4.1.2"
    "image-size" "~0.5.0"
    "mime" "^1.2.11"
    "mkdirp" "^0.5.0"
    "promise" "^7.1.1"
    "request" "2.81.0"
    "source-map" "^0.5.3"

"levn@^0.3.0", "levn@~0.3.0":
  "integrity" "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4="
  "resolved" "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"

"load-json-file@^1.0.0":
  "integrity" "sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA="
  "resolved" "https://registry.npmjs.org/load-json-file/-/load-json-file-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "parse-json" "^2.2.0"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"
    "strip-bom" "^2.0.0"

"loader-fs-cache@^1.0.0":
  "integrity" "sha1-VuC/CL2XCLJqdltoUJhAyN7J/bw="
  "resolved" "https://registry.npmjs.org/loader-fs-cache/-/loader-fs-cache-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "find-cache-dir" "^0.1.1"
    "mkdirp" "0.5.1"

"loader-runner@^2.3.0":
  "integrity" "sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw=="
  "resolved" "https://registry.npmjs.org/loader-runner/-/loader-runner-2.4.0.tgz"
  "version" "2.4.0"

"loader-utils@^0.2.16":
  "integrity" "sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g="
  "resolved" "https://registry.npmjs.org/loader-utils/-/loader-utils-0.2.17.tgz"
  "version" "0.2.17"
  dependencies:
    "big.js" "^3.1.3"
    "emojis-list" "^2.0.0"
    "json5" "^0.5.0"
    "object-assign" "^4.0.1"

"loader-utils@^1.0.0", "loader-utils@^1.0.2", "loader-utils@^1.1.0":
  "integrity" "sha512-fkpz8ejdnEMG3s37wGL07iSBDg99O9D5yflE9RGNH3hRdx9SOwYfnGYdZOUIZitN8E+E2vkq3MUMYMvPYl5ZZA=="
  "resolved" "https://registry.npmjs.org/loader-utils/-/loader-utils-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^2.0.0"
    "json5" "^1.0.1"

"locate-path@^2.0.0":
  "integrity" "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-locate" "^2.0.0"
    "path-exists" "^3.0.0"

"locate-path@^3.0.0":
  "integrity" "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-locate" "^3.0.0"
    "path-exists" "^3.0.0"

"lodash._arraycopy@^3.0.0":
  "integrity" "sha1-due3wfH7klRzdIeKVi7Qaj5Q9uE="
  "resolved" "https://registry.npmjs.org/lodash._arraycopy/-/lodash._arraycopy-3.0.0.tgz"
  "version" "3.0.0"

"lodash._arrayeach@^3.0.0":
  "integrity" "sha1-urFWsqkNPxu9XGU0AzSeXlkz754="
  "resolved" "https://registry.npmjs.org/lodash._arrayeach/-/lodash._arrayeach-3.0.0.tgz"
  "version" "3.0.0"

"lodash._baseassign@^3.0.0":
  "integrity" "sha1-jDigmVAPIVrQnlnxci/QxSv+Ck4="
  "resolved" "https://registry.npmjs.org/lodash._baseassign/-/lodash._baseassign-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "lodash._basecopy" "^3.0.0"
    "lodash.keys" "^3.0.0"

"lodash._baseclone@^3.0.0":
  "integrity" "sha1-MDUZv2OT/n5C802LYw73eU41Qrc="
  "resolved" "https://registry.npmjs.org/lodash._baseclone/-/lodash._baseclone-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "lodash._arraycopy" "^3.0.0"
    "lodash._arrayeach" "^3.0.0"
    "lodash._baseassign" "^3.0.0"
    "lodash._basefor" "^3.0.0"
    "lodash.isarray" "^3.0.0"
    "lodash.keys" "^3.0.0"

"lodash._baseclone@^4.0.0":
  "integrity" "sha1-zkKt4IOE711i+nfDD2GkbmhvhDQ="
  "resolved" "https://registry.npmjs.org/lodash._baseclone/-/lodash._baseclone-4.5.7.tgz"
  "version" "4.5.7"

"lodash._basecopy@^3.0.0":
  "integrity" "sha1-jaDmqHbPNEwK2KVIghEd08XHyjY="
  "resolved" "https://registry.npmjs.org/lodash._basecopy/-/lodash._basecopy-3.0.1.tgz"
  "version" "3.0.1"

"lodash._basecreate@^3.0.0":
  "integrity" "sha1-G8ZhYU2qf8MRt9A78WgGoCE8+CE="
  "resolved" "https://registry.npmjs.org/lodash._basecreate/-/lodash._basecreate-3.0.3.tgz"
  "version" "3.0.3"

"lodash._basefor@^3.0.0":
  "integrity" "sha1-dVC06SGO8J+tJDQ7YSAhx5tMIMI="
  "resolved" "https://registry.npmjs.org/lodash._basefor/-/lodash._basefor-3.0.3.tgz"
  "version" "3.0.3"

"lodash._bindcallback@^3.0.0":
  "integrity" "sha1-5THCdkTPi1epnhftlbNcdIeJOS4="
  "resolved" "https://registry.npmjs.org/lodash._bindcallback/-/lodash._bindcallback-3.0.1.tgz"
  "version" "3.0.1"

"lodash._getnative@^3.0.0":
  "integrity" "sha1-VwvH3t5G1hzc3mh9ZdPuy6o6r/U="
  "resolved" "https://registry.npmjs.org/lodash._getnative/-/lodash._getnative-3.9.1.tgz"
  "version" "3.9.1"

"lodash._isiterateecall@^3.0.0":
  "integrity" "sha1-UgOte6Ql+uhCRg5pbbnPPmqsBXw="
  "resolved" "https://registry.npmjs.org/lodash._isiterateecall/-/lodash._isiterateecall-3.0.9.tgz"
  "version" "3.0.9"

"lodash._stack@^4.0.0":
  "integrity" "sha1-dRqnbBuWSwR+dtFPxyoJP8teLdA="
  "resolved" "https://registry.npmjs.org/lodash._stack/-/lodash._stack-4.1.3.tgz"
  "version" "4.1.3"

"lodash.assign@^4.2.0":
  "integrity" "sha1-DZnzzNem0mHRm9rrkkUAXShYCOc="
  "resolved" "https://registry.npmjs.org/lodash.assign/-/lodash.assign-4.2.0.tgz"
  "version" "4.2.0"

"lodash.camelcase@^4.3.0":
  "integrity" "sha1-soqmKIorn8ZRA1x3EfZathkDMaY="
  "resolved" "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  "version" "4.3.0"

"lodash.clone@3.0.3":
  "integrity" "sha1-hGiMc9MrWpDKJWFpY/GJJSqZcEM="
  "resolved" "https://registry.npmjs.org/lodash.clone/-/lodash.clone-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "lodash._baseclone" "^3.0.0"
    "lodash._bindcallback" "^3.0.0"
    "lodash._isiterateecall" "^3.0.0"

"lodash.create@3.1.1":
  "integrity" "sha1-1/KEnw29p+BGgruM1yqwIkYd6+c="
  "resolved" "https://registry.npmjs.org/lodash.create/-/lodash.create-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "lodash._baseassign" "^3.0.0"
    "lodash._basecreate" "^3.0.0"
    "lodash._isiterateecall" "^3.0.0"

"lodash.defaultsdeep@4.3.2":
  "integrity" "sha1-bBpYbmxWR7DmTi15gUG4g2FYvoo="
  "resolved" "https://registry.npmjs.org/lodash.defaultsdeep/-/lodash.defaultsdeep-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "lodash._baseclone" "^4.0.0"
    "lodash._stack" "^4.0.0"
    "lodash.isplainobject" "^4.0.0"
    "lodash.keysin" "^4.0.0"
    "lodash.mergewith" "^4.0.0"
    "lodash.rest" "^4.0.0"

"lodash.isarguments@^3.0.0":
  "integrity" "sha1-L1c9hcaiQon/AGY7SRwdM4/zRYo="
  "resolved" "https://registry.npmjs.org/lodash.isarguments/-/lodash.isarguments-3.1.0.tgz"
  "version" "3.1.0"

"lodash.isarray@^3.0.0":
  "integrity" "sha1-eeTriMNqgSKvhvhEqpvNhRtfu1U="
  "resolved" "https://registry.npmjs.org/lodash.isarray/-/lodash.isarray-3.0.4.tgz"
  "version" "3.0.4"

"lodash.isplainobject@^4.0.0":
  "integrity" "sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs="
  "resolved" "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  "version" "4.0.6"

"lodash.keys@^3.0.0":
  "integrity" "sha1-TbwEcrFWvlCgsoaFXRvQsMZWCYo="
  "resolved" "https://registry.npmjs.org/lodash.keys/-/lodash.keys-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "lodash._getnative" "^3.0.0"
    "lodash.isarguments" "^3.0.0"
    "lodash.isarray" "^3.0.0"

"lodash.keysin@^4.0.0":
  "integrity" "sha1-jMP7NcLZSsxEOhhj4C+kB5nqbyg="
  "resolved" "https://registry.npmjs.org/lodash.keysin/-/lodash.keysin-4.2.0.tgz"
  "version" "4.2.0"

"lodash.memoize@^4.1.2":
  "integrity" "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4="
  "resolved" "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.merge@^4.6.1":
  "integrity" "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo="
  "resolved" "https://registry.npm.taobao.org/lodash.merge/download/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.mergewith@^4.0.0":
  "integrity" "sha512-eWw5r+PYICtEBgrBE5hhlT6aAa75f411bgDz/ZL2KZqYV03USvucsxcHUIlGTDTECs1eunpI7HOV7U+WLDvNdQ=="
  "resolved" "https://registry.npmjs.org/lodash.mergewith/-/lodash.mergewith-4.6.1.tgz"
  "version" "4.6.1"

"lodash.rest@^4.0.0":
  "integrity" "sha1-lU73UEkmIDjJbR/Jiyj9r58Hcqo="
  "resolved" "https://registry.npmjs.org/lodash.rest/-/lodash.rest-4.0.5.tgz"
  "version" "4.0.5"

"lodash.uniq@^4.5.0":
  "integrity" "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="
  "resolved" "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash@^3.8.0":
  "integrity" "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y="
  "resolved" "http://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz"
  "version" "3.10.1"

"lodash@^4.0.0", "lodash@^4.0.1", "lodash@^4.17.0", "lodash@^4.17.11", "lodash@^4.17.3", "lodash@^4.17.4", "lodash@^4.17.5", "lodash@^4.3.0", "lodash@^4.5.0":
  "integrity" "sha1-tEf2ZwoEVbv+7dETku/zMOoJdUg="
  "resolved" "https://registry.npm.taobao.org/lodash/download/lodash-4.17.15.tgz"
  "version" "4.17.15"

"log-symbols@^2.1.0":
  "integrity" "sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg=="
  "resolved" "https://registry.npmjs.org/log-symbols/-/log-symbols-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chalk" "^2.0.1"

"log4js@^0.6.31":
  "integrity" "sha1-LElBFmldb7JUgJQ9P8hy5mKlIv0="
  "resolved" "http://registry.npmjs.org/log4js/-/log4js-0.6.38.tgz"
  "version" "0.6.38"
  dependencies:
    "readable-stream" "~1.0.2"
    "semver" "~4.3.3"

"lolex@^1.6.0":
  "integrity" "sha1-OpoCg0UqR9dDnnJzG54H1zhuSfY="
  "resolved" "https://registry.npmjs.org/lolex/-/lolex-1.6.0.tgz"
  "version" "1.6.0"

"longest@^1.0.1":
  "integrity" "sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc="
  "resolved" "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz"
  "version" "1.0.1"

"loose-envify@^1.0.0":
  "integrity" "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  "resolved" "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"loud-rejection@^1.0.0":
  "integrity" "sha1-W0b4AUft7leIcPCG0Eghz5mOVR8="
  "resolved" "https://registry.npmjs.org/loud-rejection/-/loud-rejection-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "currently-unhandled" "^0.4.1"
    "signal-exit" "^3.0.0"

"lower-case@^1.1.1":
  "integrity" "sha1-miyr0bno4K6ZOkv31YdcOcQujqw="
  "resolved" "https://registry.npmjs.org/lower-case/-/lower-case-1.1.4.tgz"
  "version" "1.1.4"

"lowercase-keys@^1.0.0":
  "integrity" "sha512-G2Lj61tXDnVFFOi8VZds+SoQjtQC3dgokKdDG2mTm1tx4m50NUHBOZSBwQQHyy0V12A0JTG4icfZQH+xPyh8VA=="
  "resolved" "https://registry.npmjs.org/lowercase-keys/-/lowercase-keys-1.0.1.tgz"
  "version" "1.0.1"

"lowercase-keys@1.0.0":
  "integrity" "sha1-TjNms55/VFfjXxMkvfb4jQv8cwY="
  "resolved" "https://registry.npmjs.org/lowercase-keys/-/lowercase-keys-1.0.0.tgz"
  "version" "1.0.0"

"lru-cache@^4.0.1", "lru-cache@^4.1.1", "lru-cache@4.1.x":
  "integrity" "sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@~2.6.5":
  "integrity" "sha1-5W1jVBSO3o13B7WNFDIg/QjfD9U="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-2.6.5.tgz"
  "version" "2.6.5"

"lrz@^4.9.40":
  "integrity" "sha1-L9O0JYiiClf9eqefgVaMhF5OPXM="
  "resolved" "https://registry.npmjs.org/lrz/-/lrz-4.9.40.tgz"
  "version" "4.9.40"
  dependencies:
    "gulp-insert" "^0.5.0"
    "gulp-rename" "^1.2.2"

"make-dir@^1.0.0":
  "integrity" "sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ=="
  "resolved" "https://registry.npmjs.org/make-dir/-/make-dir-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "pify" "^3.0.0"

"map-age-cleaner@^0.1.1":
  "integrity" "sha512-bJzx6nMoP6PDLPBFmg7+xRKeFZvFboMrGlxmNj9ClvX53KrmvM5bXFXEWjbz4cz1AFn+jWJ9z/DJSz7hrs0w3w=="
  "resolved" "https://registry.npmjs.org/map-age-cleaner/-/map-age-cleaner-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "p-defer" "^1.0.0"

"map-cache@^0.2.2":
  "integrity" "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="
  "resolved" "https://registry.npmjs.org/map-cache/-/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-obj@^1.0.0", "map-obj@^1.0.1":
  "integrity" "sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0="
  "resolved" "https://registry.npmjs.org/map-obj/-/map-obj-1.0.1.tgz"
  "version" "1.0.1"

"map-visit@^1.0.0":
  "integrity" "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="
  "resolved" "https://registry.npmjs.org/map-visit/-/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"math-expression-evaluator@^1.2.14":
  "integrity" "sha1-3oGf282E3M2PrlnGrreWFbnSZqw="
  "resolved" "https://registry.npmjs.org/math-expression-evaluator/-/math-expression-evaluator-1.2.17.tgz"
  "version" "1.2.17"

"math-random@^1.0.1":
  "integrity" "sha512-rUxjysqif/BZQH2yhd5Aaq7vXMSx9NdEsQcyA07uEzIvxgI7zIr33gGsh+RU0/XjmQpCW7RsVof1vlkvQVCK5A=="
  "resolved" "https://registry.npmjs.org/math-random/-/math-random-1.0.4.tgz"
  "version" "1.0.4"

"md5.js@^1.3.4":
  "integrity" "sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg=="
  "resolved" "https://registry.npmjs.org/md5.js/-/md5.js-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"media-typer@0.3.0":
  "integrity" "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="
  "resolved" "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"mem@^4.0.0":
  "integrity" "sha512-I5u6Q1x7wxO0kdOpYBB28xueHADYps5uty/zg936CiG8NTe5sJL8EjrCuLneuDW3PlMdZBGDIn8BirEVdovZvg=="
  "resolved" "https://registry.npmjs.org/mem/-/mem-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "map-age-cleaner" "^0.1.1"
    "mimic-fn" "^1.0.0"
    "p-is-promise" "^2.0.0"

"memory-fs@^0.4.0", "memory-fs@~0.4.1":
  "integrity" "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI="
  "resolved" "https://registry.npmjs.org/memory-fs/-/memory-fs-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"meow@^3.3.0":
  "integrity" "sha1-cstmi0JSKCkKu/qFaJJYcwioAfs="
  "resolved" "https://registry.npmjs.org/meow/-/meow-3.7.0.tgz"
  "version" "3.7.0"
  dependencies:
    "camelcase-keys" "^2.0.0"
    "decamelize" "^1.1.2"
    "loud-rejection" "^1.0.0"
    "map-obj" "^1.0.1"
    "minimist" "^1.1.3"
    "normalize-package-data" "^2.3.4"
    "object-assign" "^4.0.1"
    "read-pkg-up" "^1.0.1"
    "redent" "^1.0.0"
    "trim-newlines" "^1.0.0"

"merge-descriptors@1.0.1":
  "integrity" "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="
  "resolved" "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  "version" "1.0.1"

"methods@~1.1.2":
  "integrity" "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="
  "resolved" "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^2.1.5", "micromatch@^2.3.11":
  "integrity" "sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz"
  "version" "2.3.11"
  dependencies:
    "arr-diff" "^2.0.0"
    "array-unique" "^0.2.1"
    "braces" "^1.8.2"
    "expand-brackets" "^0.1.4"
    "extglob" "^0.3.1"
    "filename-regex" "^2.0.0"
    "is-extglob" "^1.0.0"
    "is-glob" "^2.0.1"
    "kind-of" "^3.0.2"
    "normalize-path" "^2.0.1"
    "object.omit" "^2.0.0"
    "parse-glob" "^3.0.4"
    "regex-cache" "^0.4.2"

"micromatch@^3.1.10":
  "integrity" "sha1-cIWbyVyYQJUvNZoGij/En57PrCM="
  "resolved" "https://registry.npm.taobao.org/micromatch/download/micromatch-3.1.10.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromatch%2Fdownload%2Fmicromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"micromatch@^3.1.4":
  "integrity" "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"miller-rabin@^4.0.0":
  "integrity" "sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA=="
  "resolved" "https://registry.npmjs.org/miller-rabin/-/miller-rabin-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.0.0"
    "brorand" "^1.0.1"

"mime-db@~1.37.0":
  "integrity" "sha512-R3C4db6bgQhlIhPU48fUtdVmKnflq+hRdad7IyKhtFj06VPNVdk2RhiYL3UjQIlso8L+YxAtFkobT0VK+S/ybg=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.37.0.tgz"
  "version" "1.37.0"

"mime-types@^2.1.12", "mime-types@~2.1.11", "mime-types@~2.1.18", "mime-types@~2.1.19", "mime-types@~2.1.7":
  "integrity" "sha512-3iL6DbwpyLzjR3xHSFNFeb9Nz/M8WDkX33t1GFQnFOllWk8pOrh/LSrB5OXlnlW5P9LH73X6loW/eogc+F5lJg=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.21.tgz"
  "version" "2.1.21"
  dependencies:
    "mime-db" "~1.37.0"

"mime@^1.2.11", "mime@^1.3.4", "mime@1.4.1":
  "integrity" "sha512-KI1+qOZu5DcW6wayYHSzR/tXKCDC5Om4s1z2QJjDULzLcmf3DvzS7oluY4HCTrc+9FiKmWUgeNLg7W3uIQvxtQ=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-1.4.1.tgz"
  "version" "1.4.1"

"mime@^1.5.0":
  "integrity" "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  "version" "1.6.0"

"mime@1.3.x":
  "integrity" "sha1-WR2E02U6awtKO5343lqoEI5y5eA="
  "resolved" "https://registry.npmjs.org/mime/-/mime-1.3.6.tgz"
  "version" "1.3.6"

"mimic-fn@^1.0.0":
  "integrity" "sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ=="
  "resolved" "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz"
  "version" "1.2.0"

"mimic-response@^1.0.0":
  "integrity" "sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ=="
  "resolved" "https://registry.npmjs.org/mimic-response/-/mimic-response-1.0.1.tgz"
  "version" "1.0.1"

"minimalistic-assert@^1.0.0", "minimalistic-assert@^1.0.1":
  "integrity" "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A=="
  "resolved" "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimalistic-crypto-utils@^1.0.0", "minimalistic-crypto-utils@^1.0.1":
  "integrity" "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo="
  "resolved" "https://registry.npmjs.org/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.0", "minimatch@^3.0.2", "minimatch@^3.0.4", "minimatch@2 || 3":
  "integrity" "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimatch@3.0.3":
  "integrity" "sha1-Kk5AkLlrLbBqnX3wEFWmKnfJt3Q="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "brace-expansion" "^1.0.0"

"minimist@^1.1.3":
  "integrity" "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ="
  "resolved" "http://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz"
  "version" "1.2.0"

"minimist@^1.2.0":
  "integrity" "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ="
  "resolved" "https://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz"
  "version" "1.2.0"

"minimist@^1.2.6":
  "integrity" "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA=="
  "resolved" "https://registry.npmmirror.com/minimist/-/minimist-1.2.8.tgz"
  "version" "1.2.8"

"minimist@~0.0.1", "minimist@0.0.8":
  "integrity" "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0="
  "resolved" "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz"
  "version" "0.0.8"

"minimist@1.2.0":
  "integrity" "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ="
  "resolved" "http://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz"
  "version" "1.2.0"

"minipass@^2.2.1", "minipass@^2.3.4":
  "integrity" "sha512-Gi1W4k059gyRbyVUZQ4mEqLm0YIUiGYfvxhF6SIlk3ui1WVxMTGfGdQ2SInh3PDrRTVvPKgULkpJtT4RH10+VA=="
  "resolved" "https://registry.npmjs.org/minipass/-/minipass-2.3.5.tgz"
  "version" "2.3.5"
  dependencies:
    "safe-buffer" "^5.1.2"
    "yallist" "^3.0.0"

"minizlib@^1.1.1":
  "integrity" "sha512-7+4oTUOWKg7AuL3vloEWekXY2/D20cevzsrNT2kGWm+39J9hGTCBv8VI5Pm5lXZ/o3/mdR4f8rflAPhnQb8mPA=="
  "resolved" "https://registry.npmjs.org/minizlib/-/minizlib-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "minipass" "^2.2.1"

"mississippi@^2.0.0":
  "integrity" "sha512-zHo8v+otD1J10j/tC+VNoGK9keCuByhKovAvdn74dmxJl9+mWHnx6EMsDN4lgRoMI/eYo2nchAxniIbUPb5onw=="
  "resolved" "https://registry.npmjs.org/mississippi/-/mississippi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "concat-stream" "^1.5.0"
    "duplexify" "^3.4.2"
    "end-of-stream" "^1.1.0"
    "flush-write-stream" "^1.0.0"
    "from2" "^2.1.0"
    "parallel-transform" "^1.1.0"
    "pump" "^2.0.1"
    "pumpify" "^1.3.3"
    "stream-each" "^1.1.0"
    "through2" "^2.0.0"

"mixin-deep@^1.2.0":
  "integrity" "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY="
  "resolved" "https://registry.npm.taobao.org/mixin-deep/download/mixin-deep-1.3.2.tgz?cache=0&sync_timestamp=1561436244196&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmixin-deep%2Fdownload%2Fmixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp@^0.5.0", "mkdirp@^0.5.1", "mkdirp@~0.5.0", "mkdirp@~0.5.1", "mkdirp@0.5.1", "mkdirp@0.5.x":
  "integrity" "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM="
  "resolved" "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz"
  "version" "0.5.1"
  dependencies:
    "minimist" "0.0.8"

"mkdirp@^0.5.4":
  "integrity" "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw=="
  "resolved" "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.6.tgz"
  "version" "0.5.6"
  dependencies:
    "minimist" "^1.2.6"

"mkpath@1.0.0":
  "integrity" "sha1-67Opd+evHGg65v2hK1Raa6bFhT0="
  "resolved" "https://registry.npmjs.org/mkpath/-/mkpath-1.0.0.tgz"
  "version" "1.0.0"

"mocha-nightwatch@3.2.2":
  "integrity" "sha1-kby5s73gV912d8eBJeSR5Y1mZHw="
  "resolved" "https://registry.npmjs.org/mocha-nightwatch/-/mocha-nightwatch-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "browser-stdout" "1.3.0"
    "commander" "2.9.0"
    "debug" "2.2.0"
    "diff" "1.4.0"
    "escape-string-regexp" "1.0.5"
    "glob" "7.0.5"
    "growl" "1.9.2"
    "json3" "3.3.2"
    "lodash.create" "3.1.1"
    "mkdirp" "0.5.1"
    "supports-color" "3.1.2"

"mocha@^3.2.0":
  "integrity" "sha512-/6na001MJWEtYxHOV1WLfsmR4YIynkUEhBwzsb+fk2qmQ3iqsi258l/Q2MWHJMImAcNpZ8DEdYAK72NHoIQ9Eg=="
  "resolved" "https://registry.npmjs.org/mocha/-/mocha-3.5.3.tgz"
  "version" "3.5.3"
  dependencies:
    "browser-stdout" "1.3.0"
    "commander" "2.9.0"
    "debug" "2.6.8"
    "diff" "3.2.0"
    "escape-string-regexp" "1.0.5"
    "glob" "7.1.1"
    "growl" "1.9.2"
    "he" "1.1.1"
    "json3" "3.3.2"
    "lodash.create" "3.1.1"
    "mkdirp" "0.5.1"
    "supports-color" "3.1.2"

"move-concurrently@^1.0.1":
  "integrity" "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I="
  "resolved" "https://registry.npmjs.org/move-concurrently/-/move-concurrently-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "aproba" "^1.1.1"
    "copy-concurrently" "^1.0.0"
    "fs-write-stream-atomic" "^1.0.8"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.3"

"ms@^2.1.1":
  "integrity" "sha512-tgp+dl5cGk28utYktBsrFqA7HKgrhgPsg6Z/EfhWI4gl1Hwq8B/GmY/0oXZ6nF8hDVesS/FpnYaD/kOWhYQvyg=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.1.tgz"
  "version" "2.1.1"

"ms@0.7.1":
  "integrity" "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg="
  "resolved" "http://registry.npmjs.org/ms/-/ms-0.7.1.tgz"
  "version" "0.7.1"

"ms@0.7.2":
  "integrity" "sha1-riXPJRKziFodldfwN4aNhDESR2U="
  "resolved" "http://registry.npmjs.org/ms/-/ms-0.7.2.tgz"
  "version" "0.7.2"

"ms@2.0.0":
  "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  "version" "2.0.0"

"mutation-observer@^1.0.3":
  "integrity" "sha512-M/O/4rF2h776hV7qGMZUH3utZLO/jK7p8rnNgGkjKUw8zCGjRQPxB8z6+5l8+VjRUQ3dNYu4vjqXYLr+U8ZVNA=="
  "resolved" "https://registry.npmmirror.com/mutation-observer/-/mutation-observer-1.0.3.tgz"
  "version" "1.0.3"

"mute-stream@0.0.5":
  "integrity" "sha1-j7+rsKmKJT0xhDMfno3rc3L6xsA="
  "resolved" "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.5.tgz"
  "version" "0.0.5"

"nan@^2.9.2":
  "integrity" "sha512-JY7V6lRkStKcKTvHO5NVSQRv+RV+FIL5pvDoLiAtSL9pKlC5x9PKQcZDsq7m4FO4d57mkhC6Z+QhAh3Jdk5JFw=="
  "resolved" "https://registry.npmjs.org/nan/-/nan-2.12.1.tgz"
  "version" "2.12.1"

"nanomatch@^1.2.9":
  "integrity" "sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA=="
  "resolved" "https://registry.npmjs.org/nanomatch/-/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"native-promise-only@^0.8.1":
  "integrity" "sha1-IKMYwwy0X3H+et+/eyHJnBRy7xE="
  "resolved" "https://registry.npmjs.org/native-promise-only/-/native-promise-only-0.8.1.tgz"
  "version" "0.8.1"

"natural-compare@^1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="
  "resolved" "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"needle@^2.2.1":
  "integrity" "sha512-HyoqEb4wr/rsoaIDfTH2aVL9nWtQqba2/HvMv+++m8u0dz808MaagKILxtfeSN7QU7nvbQ79zk3vYOJp9zsNEA=="
  "resolved" "https://registry.npmjs.org/needle/-/needle-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "debug" "^2.1.2"
    "iconv-lite" "^0.4.4"
    "sax" "^1.2.4"

"negotiator@0.6.1":
  "integrity" "sha1-KzJxhOiZIQEXeyhWP7XnECrNDKk="
  "resolved" "https://registry.npmjs.org/negotiator/-/negotiator-0.6.1.tgz"
  "version" "0.6.1"

"neo-async@^2.5.0", "neo-async@^2.6.0":
  "integrity" "sha512-MFh0d/Wa7vkKO3Y3LlacqAEeHK0mckVqzDieUKTT+KGxi+zIpeVsFxymkIiRpbpDziHc290Xr9A1O4Om7otoRA=="
  "resolved" "https://registry.npmjs.org/neo-async/-/neo-async-2.6.0.tgz"
  "version" "2.6.0"

"netmask@~1.0.4":
  "integrity" "sha1-ICl+idhvb2QA8lDZ9Pa0wZRfzTU="
  "resolved" "https://registry.npmjs.org/netmask/-/netmask-1.0.6.tgz"
  "version" "1.0.6"

"next-tick@1":
  "integrity" "sha1-yobR/ogoFpsBICCOPchCS524NCw="
  "resolved" "https://registry.npmjs.org/next-tick/-/next-tick-1.0.0.tgz"
  "version" "1.0.0"

"nice-try@^1.0.4":
  "integrity" "sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ=="
  "resolved" "https://registry.npmjs.org/nice-try/-/nice-try-1.0.5.tgz"
  "version" "1.0.5"

"nightwatch@^0.9.12":
  "integrity" "sha1-nnlKdRS0/V9GYC02jlBRUjKrnpA="
  "resolved" "https://registry.npmjs.org/nightwatch/-/nightwatch-0.9.21.tgz"
  "version" "0.9.21"
  dependencies:
    "chai-nightwatch" "~0.1.x"
    "ejs" "2.5.7"
    "lodash.clone" "3.0.3"
    "lodash.defaultsdeep" "4.3.2"
    "minimatch" "3.0.3"
    "mkpath" "1.0.0"
    "mocha-nightwatch" "3.2.2"
    "optimist" "0.6.1"
    "proxy-agent" "2.0.0"
    "q" "1.4.1"

"no-case@^2.2.0":
  "integrity" "sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ=="
  "resolved" "https://registry.npmjs.org/no-case/-/no-case-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "lower-case" "^1.1.1"

"node-libs-browser@^2.0.0":
  "integrity" "sha512-5MQunG/oyOaBdttrL40dA7bUfPORLRWMUJLQtMg7nluxUvk5XwnLdL9twQHFAjRx/y7mIMkLKT9++qPbbk6BZA=="
  "resolved" "https://registry.npmjs.org/node-libs-browser/-/node-libs-browser-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "assert" "^1.1.1"
    "browserify-zlib" "^0.2.0"
    "buffer" "^4.3.0"
    "console-browserify" "^1.1.0"
    "constants-browserify" "^1.0.0"
    "crypto-browserify" "^3.11.0"
    "domain-browser" "^1.1.1"
    "events" "^3.0.0"
    "https-browserify" "^1.0.0"
    "os-browserify" "^0.3.0"
    "path-browserify" "0.0.0"
    "process" "^0.11.10"
    "punycode" "^1.2.4"
    "querystring-es3" "^0.2.0"
    "readable-stream" "^2.3.3"
    "stream-browserify" "^2.0.1"
    "stream-http" "^2.7.2"
    "string_decoder" "^1.0.0"
    "timers-browserify" "^2.0.4"
    "tty-browserify" "0.0.0"
    "url" "^0.11.0"
    "util" "^0.11.0"
    "vm-browserify" "0.0.4"

"node-pre-gyp@^0.10.0":
  "integrity" "sha512-d1xFs+C/IPS8Id0qPTZ4bUT8wWryfR/OzzAFxweG+uLN85oPzyo2Iw6bVlLQ/JOdgNonXLCoRyqDzDWq4iw72A=="
  "resolved" "https://registry.npmjs.org/node-pre-gyp/-/node-pre-gyp-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "detect-libc" "^1.0.2"
    "mkdirp" "^0.5.1"
    "needle" "^2.2.1"
    "nopt" "^4.0.1"
    "npm-packlist" "^1.1.6"
    "npmlog" "^4.0.2"
    "rc" "^1.2.7"
    "rimraf" "^2.6.1"
    "semver" "^5.3.0"
    "tar" "^4"

"nopt@^4.0.1":
  "integrity" "sha1-0NRoWv1UFRk8jHUFYC0NF81kR00="
  "resolved" "https://registry.npmjs.org/nopt/-/nopt-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "abbrev" "1"
    "osenv" "^0.1.4"

"nopt@3.x":
  "integrity" "sha1-xkZdvwirzU2zWTF/eaxopkayj/k="
  "resolved" "https://registry.npmjs.org/nopt/-/nopt-3.0.6.tgz"
  "version" "3.0.6"
  dependencies:
    "abbrev" "1"

"normalize-package-data@^2.3.2", "normalize-package-data@^2.3.4":
  "integrity" "sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA=="
  "resolved" "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^2.0.0", "normalize-path@^2.0.1":
  "integrity" "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^2.1.1":
  "integrity" "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI="
  "resolved" "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"normalize-url@^1.4.0":
  "integrity" "sha1-LMDWazHqIwNkWENuNiDYWVTGbDw="
  "resolved" "https://registry.npmjs.org/normalize-url/-/normalize-url-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "object-assign" "^4.0.1"
    "prepend-http" "^1.0.0"
    "query-string" "^4.1.0"
    "sort-keys" "^1.0.0"

"normalize-url@2.0.1":
  "integrity" "sha512-D6MUW4K/VzoJ4rJ01JFKxDrtY1v9wrgzCX5f2qj/lzH1m/lW6MhUZFKerVsnyjOhOsYzI9Kqqak+10l4LvLpMw=="
  "resolved" "https://registry.npmjs.org/normalize-url/-/normalize-url-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "prepend-http" "^2.0.0"
    "query-string" "^5.0.1"
    "sort-keys" "^2.0.0"

"normalize-wheel@^1.0.1":
  "integrity" "sha512-1OnlAPZ3zgrk8B91HyRj+eVv+kS5u+Z0SCsak6Xil/kmgEia50ga7zfkumayonZrImffAxPU/5WcyGhzetHNPA=="
  "resolved" "https://registry.npmmirror.com/normalize-wheel/-/normalize-wheel-1.0.1.tgz"
  "version" "1.0.1"

"npm-bundled@^1.0.1":
  "integrity" "sha512-m/e6jgWu8/v5niCUKQi9qQl8QdeEduFA96xHDDzFGqly0OOjI7c+60KM/2sppfnUU9JJagf+zs+yGhqSOFj71g=="
  "resolved" "https://registry.npmjs.org/npm-bundled/-/npm-bundled-1.0.5.tgz"
  "version" "1.0.5"

"npm-packlist@^1.1.6":
  "integrity" "sha512-7Mni4Z8Xkx0/oegoqlcao/JpPCPEMtUvsmB0q7mgvlMinykJLSRTYuFqoQLYgGY8biuxIeiHO+QNJKbCfljewQ=="
  "resolved" "https://registry.npmjs.org/npm-packlist/-/npm-packlist-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "ignore-walk" "^3.0.1"
    "npm-bundled" "^1.0.1"

"npm-run-path@^2.0.0":
  "integrity" "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8="
  "resolved" "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"npmlog@^4.0.2":
  "integrity" "sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg=="
  "resolved" "https://registry.npmjs.org/npmlog/-/npmlog-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "are-we-there-yet" "~1.1.2"
    "console-control-strings" "~1.1.0"
    "gauge" "~2.7.3"
    "set-blocking" "~2.0.0"

"nth-check@~1.0.1":
  "integrity" "sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg=="
  "resolved" "https://registry.npmjs.org/nth-check/-/nth-check-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "boolbase" "~1.0.0"

"num2fraction@^1.2.2":
  "integrity" "sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4="
  "resolved" "https://registry.npmjs.org/num2fraction/-/num2fraction-1.2.2.tgz"
  "version" "1.2.2"

"number-is-nan@^1.0.0":
  "integrity" "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0="
  "resolved" "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.1.tgz"
  "version" "1.0.1"

"oauth-sign@~0.8.1":
  "integrity" "sha1-Rqarfwrq2N6unsBWV4C31O/rnUM="
  "resolved" "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.2.tgz"
  "version" "0.8.2"

"oauth-sign@~0.9.0":
  "integrity" "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ=="
  "resolved" "https://registry.npmmirror.com/oauth-sign/-/oauth-sign-0.9.0.tgz"
  "version" "0.9.0"

"object-assign@^4.0.1", "object-assign@^4.1.0", "object-assign@^4.1.1":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
  "resolved" "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-assign@4.1.0":
  "integrity" "sha1-ejs9DpgGPUP0wD8uiubNUahog6A="
  "resolved" "https://registry.npmjs.org/object-assign/-/object-assign-4.1.0.tgz"
  "version" "4.1.0"

"object-component@0.0.3":
  "integrity" "sha1-8MaapQ78lbhmwYb0AKM3acsvEpE="
  "resolved" "https://registry.npmjs.org/object-component/-/object-component-0.0.3.tgz"
  "version" "0.0.3"

"object-copy@^0.1.0":
  "integrity" "sha1-fn2Fi3gb18mRpBupde04EnVOmYw="
  "resolved" "https://registry.npmjs.org/object-copy/-/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-hash@^1.1.4":
  "integrity" "sha512-OSuu/pU4ENM9kmREg0BdNrUDIl1heYa4mBZacJc+vVWz4GtAwu7jO8s4AIt2aGRUTqxykpWzI3Oqnsm13tTMDA=="
  "resolved" "https://registry.npmjs.org/object-hash/-/object-hash-1.3.1.tgz"
  "version" "1.3.1"

"object-inspect@^1.9.0":
  "integrity" "sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ=="
  "resolved" "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.13.1.tgz"
  "version" "1.13.1"

"object-visit@^1.0.0":
  "integrity" "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs="
  "resolved" "https://registry.npmjs.org/object-visit/-/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.omit@^2.0.0":
  "integrity" "sha1-Gpx0SCnznbuFjHbKNXmuKlTr0fo="
  "resolved" "https://registry.npmjs.org/object.omit/-/object.omit-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "for-own" "^0.1.4"
    "is-extendable" "^0.1.1"

"object.pick@^1.3.0":
  "integrity" "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="
  "resolved" "https://registry.npmjs.org/object.pick/-/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"on-finished@~2.3.0":
  "integrity" "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="
  "resolved" "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0", "once@1.x":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^1.0.0":
  "integrity" "sha1-ofeDj4MUxRbwXs78vEzP4EtO14k="
  "resolved" "http://registry.npmjs.org/onetime/-/onetime-1.1.0.tgz"
  "version" "1.1.0"

"onetime@^2.0.0":
  "integrity" "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ="
  "resolved" "https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mimic-fn" "^1.0.0"

"opener@^1.4.3":
  "integrity" "sha512-goYSy5c2UXE4Ra1xixabeVh1guIX/ZV/YokJksb6q2lubWu6UbvPQ20p542/sFIll1nl8JnCyK9oBaOcCWXwvA=="
  "resolved" "https://registry.npmjs.org/opener/-/opener-1.5.1.tgz"
  "version" "1.5.1"

"opn@^5.1.0":
  "integrity" "sha512-YF9MNdVy/0qvJvDtunAOzFw9iasOQHpVthTCvGzxt61Il64AYSGdK+rYwld7NAfk9qJ7dt+hymBNSc9LNYS+Sw=="
  "resolved" "https://registry.npmjs.org/opn/-/opn-5.4.0.tgz"
  "version" "5.4.0"
  dependencies:
    "is-wsl" "^1.1.0"

"optimist@^0.6.1", "optimist@0.6.1":
  "integrity" "sha1-2j6nRob6IaGaERwybpDrFaAZZoY="
  "resolved" "https://registry.npmjs.org/optimist/-/optimist-0.6.1.tgz"
  "version" "0.6.1"
  dependencies:
    "minimist" "~0.0.1"
    "wordwrap" "~0.0.2"

"optimize-css-assets-webpack-plugin@^2.0.0":
  "integrity" "sha1-UcrnEjDskaTBV2SXFeR0mODLRgs="
  "resolved" "https://registry.npmjs.org/optimize-css-assets-webpack-plugin/-/optimize-css-assets-webpack-plugin-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "underscore" "^1.8.3"
    "webpack-sources" "^0.1.0"

"optionator@^0.8.1", "optionator@^0.8.2":
  "integrity" "sha1-NkxeQJ0/TWMB1sC0wFu6UBgK62Q="
  "resolved" "https://registry.npmjs.org/optionator/-/optionator-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "deep-is" "~0.1.3"
    "fast-levenshtein" "~2.0.4"
    "levn" "~0.3.0"
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"
    "wordwrap" "~1.0.0"

"options@>=0.0.5":
  "integrity" "sha1-7CLTEoBrtT5zF3Pnza788cZDEo8="
  "resolved" "https://registry.npmjs.org/options/-/options-0.0.6.tgz"
  "version" "0.0.6"

"ora@^1.2.0":
  "integrity" "sha512-iMK1DOQxzzh2MBlVsU42G80mnrvUhqsMh74phHtDlrcTZPK0pH6o7l7DRshK+0YsxDyEuaOkziVdvM3T0QTzpw=="
  "resolved" "https://registry.npmjs.org/ora/-/ora-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "chalk" "^2.1.0"
    "cli-cursor" "^2.1.0"
    "cli-spinners" "^1.0.1"
    "log-symbols" "^2.1.0"

"os-browserify@^0.3.0":
  "integrity" "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc="
  "resolved" "https://registry.npmjs.org/os-browserify/-/os-browserify-0.3.0.tgz"
  "version" "0.3.0"

"os-homedir@^1.0.0", "os-homedir@^1.0.1":
  "integrity" "sha1-/7xJiDNuDoM94MFox+8VISGqf7M="
  "resolved" "https://registry.npmjs.org/os-homedir/-/os-homedir-1.0.2.tgz"
  "version" "1.0.2"

"os-locale@^1.4.0":
  "integrity" "sha1-IPnxeuKe00XoveWDsT0gCYA8FNk="
  "resolved" "http://registry.npmjs.org/os-locale/-/os-locale-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "lcid" "^1.0.0"

"os-locale@^3.0.0":
  "integrity" "sha512-Z8l3R4wYWM40/52Z+S265okfFj8Kt2cC2MKY+xNi3kFs+XGI7WXu/I309QQQYbRW4ijiZ+yxs9pqEhJh0DqW3Q=="
  "resolved" "https://registry.npmjs.org/os-locale/-/os-locale-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "execa" "^1.0.0"
    "lcid" "^2.0.0"
    "mem" "^4.0.0"

"os-tmpdir@^1.0.0":
  "integrity" "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="
  "resolved" "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"os-tmpdir@^1.0.1", "os-tmpdir@~1.0.1":
  "integrity" "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="
  "resolved" "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"osenv@^0.1.4":
  "integrity" "sha512-0CWcCECdMVc2Rw3U5w9ZjqX6ga6ubk1xDVKxtBQPK7wis/0F2r9T6k4ydGYhecl7YUBxBVxhL5oisPsNxAPe2g=="
  "resolved" "https://registry.npmjs.org/osenv/-/osenv-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "os-homedir" "^1.0.0"
    "os-tmpdir" "^1.0.0"

"p-cancelable@^0.4.0":
  "integrity" "sha512-HNa1A8LvB1kie7cERyy21VNeHb2CWJJYqyyC2o3klWFfMGlFmWv2Z7sFgZH8ZiaYL95ydToKTFVXgMV/Os0bBQ=="
  "resolved" "http://registry.npmjs.org/p-cancelable/-/p-cancelable-0.4.1.tgz"
  "version" "0.4.1"

"p-defer@^1.0.0":
  "integrity" "sha1-n26xgvbJqozXQwBKfU+WsZaw+ww="
  "resolved" "https://registry.npmjs.org/p-defer/-/p-defer-1.0.0.tgz"
  "version" "1.0.0"

"p-finally@^1.0.0":
  "integrity" "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="
  "resolved" "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-is-promise@^1.1.0":
  "integrity" "sha1-nJRWmJ6fZYgBewQ01WCXZ1w9oF4="
  "resolved" "https://registry.npmjs.org/p-is-promise/-/p-is-promise-1.1.0.tgz"
  "version" "1.1.0"

"p-is-promise@^2.0.0":
  "integrity" "sha512-pzQPhYMCAgLAKPWD2jC3Se9fEfrD9npNos0y150EeqZll7akhEgGhTW/slB6lHku8AvYGiJ+YJ5hfHKePPgFWg=="
  "resolved" "https://registry.npmjs.org/p-is-promise/-/p-is-promise-2.0.0.tgz"
  "version" "2.0.0"

"p-limit@^1.0.0":
  "integrity" "sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "p-try" "^1.0.0"

"p-limit@^1.1.0":
  "integrity" "sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "p-try" "^1.0.0"

"p-limit@^2.0.0":
  "integrity" "sha512-NhURkNcrVB+8hNfLuysU8enY5xn2KXphsHBaC2YmRNTZRc7RWusw6apSpdEj3jo4CMb6W9nrF6tTnsJsJeyu6g=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^2.0.0":
  "integrity" "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-limit" "^1.1.0"

"p-locate@^3.0.0":
  "integrity" "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-limit" "^2.0.0"

"p-timeout@^2.0.1":
  "integrity" "sha512-88em58dDVB/KzPEx1X0N3LwFfYZPyDc4B6eF38M1rk9VTZMbxXXgjugz8mmwpS9Ox4BDZ+t6t3QP5+/gazweIA=="
  "resolved" "https://registry.npmjs.org/p-timeout/-/p-timeout-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "p-finally" "^1.0.0"

"p-try@^1.0.0":
  "integrity" "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M="
  "resolved" "https://registry.npmjs.org/p-try/-/p-try-1.0.0.tgz"
  "version" "1.0.0"

"p-try@^2.0.0":
  "integrity" "sha512-hMp0onDKIajHfIkdRk3P4CdCmErkYAxxDtP3Wx/4nZ3aGlau2VKh3mZpcuFkH27WQkL/3WBCPOktzA9ZOAnMQQ=="
  "resolved" "https://registry.npmjs.org/p-try/-/p-try-2.0.0.tgz"
  "version" "2.0.0"

"pac-proxy-agent@1":
  "integrity" "sha512-QBELCWyLYPgE2Gj+4wUEiMscHrQ8nRPBzYItQNOHWavwBt25ohZHQC4qnd5IszdVVrFbLsQ+dPkm6eqdjJAmwQ=="
  "resolved" "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "agent-base" "2"
    "debug" "2"
    "extend" "3"
    "get-uri" "2"
    "http-proxy-agent" "1"
    "https-proxy-agent" "1"
    "pac-resolver" "~2.0.0"
    "raw-body" "2"
    "socks-proxy-agent" "2"

"pac-resolver@~2.0.0":
  "integrity" "sha1-mbiNLxk/ve78HJpSnB8yYKtSd80="
  "resolved" "https://registry.npmjs.org/pac-resolver/-/pac-resolver-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "co" "~3.0.6"
    "degenerator" "~1.0.2"
    "ip" "1.0.1"
    "netmask" "~1.0.4"
    "thunkify" "~2.1.1"

"pako@~1.0.5":
  "integrity" "sha512-6i0HVbUfcKaTv+EG8ZTr75az7GFXcLYk9UyLEg7Notv/Ma+z/UG3TCoz6GiNeOrn1E/e63I0X/Hpw18jHOTUnA=="
  "resolved" "https://registry.npmjs.org/pako/-/pako-1.0.8.tgz"
  "version" "1.0.8"

"parallel-transform@^1.1.0":
  "integrity" "sha1-1BDwZbBdojCB/NEPKIVMKb2jOwY="
  "resolved" "https://registry.npmjs.org/parallel-transform/-/parallel-transform-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "cyclist" "~0.2.2"
    "inherits" "^2.0.3"
    "readable-stream" "^2.1.5"

"param-case@2.1.x":
  "integrity" "sha1-35T9jPZTHs915r75oIWPvHK+Ikc="
  "resolved" "https://registry.npmjs.org/param-case/-/param-case-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "no-case" "^2.2.0"

"parse-asn1@^5.0.0":
  "integrity" "sha512-VrPoetlz7B/FqjBLD2f5wBVZvsZVLnRUrxVLfRYhGXCODa/NWE4p3Wp+6+aV3ZPL3KM7/OZmxDIwwijD7yuucg=="
  "resolved" "https://registry.npmjs.org/parse-asn1/-/parse-asn1-5.1.3.tgz"
  "version" "5.1.3"
  dependencies:
    "asn1.js" "^4.0.0"
    "browserify-aes" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.0"
    "pbkdf2" "^3.0.3"
    "safe-buffer" "^5.1.1"

"parse-glob@^3.0.4":
  "integrity" "sha1-ssN2z7EfNVE7rdFz7wu246OIORw="
  "resolved" "https://registry.npmjs.org/parse-glob/-/parse-glob-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "glob-base" "^0.3.0"
    "is-dotfile" "^1.0.0"
    "is-extglob" "^1.0.0"
    "is-glob" "^2.0.0"

"parse-json@^2.2.0":
  "integrity" "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck="
  "resolved" "https://registry.npmjs.org/parse-json/-/parse-json-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "error-ex" "^1.2.0"

"parsejson@0.0.3":
  "integrity" "sha1-q343WfIJ7OmUN5c/fQ8fZK4OZKs="
  "resolved" "https://registry.npmjs.org/parsejson/-/parsejson-0.0.3.tgz"
  "version" "0.0.3"
  dependencies:
    "better-assert" "~1.0.0"

"parseqs@0.0.5":
  "integrity" "sha1-1SCKNzjkZ2bikbouoXNoSSGouJ0="
  "resolved" "https://registry.npmjs.org/parseqs/-/parseqs-0.0.5.tgz"
  "version" "0.0.5"
  dependencies:
    "better-assert" "~1.0.0"

"parseuri@0.0.5":
  "integrity" "sha1-gCBKUNTbt3m/3G6+J3jZDkvOMgo="
  "resolved" "https://registry.npmjs.org/parseuri/-/parseuri-0.0.5.tgz"
  "version" "0.0.5"
  dependencies:
    "better-assert" "~1.0.0"

"parseurl@~1.3.2":
  "integrity" "sha1-/CidTtiZMRlGDBViUyYs3I3mW/M="
  "resolved" "https://registry.npmjs.org/parseurl/-/parseurl-1.3.2.tgz"
  "version" "1.3.2"

"pascalcase@^0.1.1":
  "integrity" "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="
  "resolved" "https://registry.npmjs.org/pascalcase/-/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-browserify@0.0.0":
  "integrity" "sha1-oLhwcpquIUAFt9UDLsLLuw+0RRo="
  "resolved" "https://registry.npmjs.org/path-browserify/-/path-browserify-0.0.0.tgz"
  "version" "0.0.0"

"path-dirname@^1.0.0":
  "integrity" "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="
  "resolved" "https://registry.npmjs.org/path-dirname/-/path-dirname-1.0.2.tgz"
  "version" "1.0.2"

"path-exists@^2.0.0":
  "integrity" "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pinkie-promise" "^2.0.0"

"path-exists@^3.0.0":
  "integrity" "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz"
  "version" "3.0.0"

"path-is-absolute@^1.0.0", "path-is-absolute@^1.0.1":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-is-inside@^1.0.1":
  "integrity" "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM="
  "resolved" "https://registry.npmjs.org/path-is-inside/-/path-is-inside-1.0.2.tgz"
  "version" "1.0.2"

"path-key@^2.0.0", "path-key@^2.0.1":
  "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-parse@^1.0.6":
  "integrity" "sha512-GSmOT2EbHrINBf9SR7CDELwlJ8AENk3Qn7OikK4nFYAu3Ote2+JYNVvkpAEQm3/TLNEJFD/xZJjzyxg3KBWOzw=="
  "resolved" "https://registry.npmjs.org/path-parse/-/path-parse-1.0.6.tgz"
  "version" "1.0.6"

"path-to-regexp@^1.7.0":
  "integrity" "sha1-Wf3g9DW62suhA6hOnTvGTpa5k30="
  "resolved" "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "isarray" "0.0.1"

"path-to-regexp@0.1.7":
  "integrity" "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="
  "resolved" "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  "version" "0.1.7"

"path-type@^1.0.0":
  "integrity" "sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE="
  "resolved" "https://registry.npmjs.org/path-type/-/path-type-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"path-type@^3.0.0":
  "integrity" "sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg=="
  "resolved" "https://registry.npmjs.org/path-type/-/path-type-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "pify" "^3.0.0"

"pbkdf2@^3.0.3":
  "integrity" "sha512-U/il5MsrZp7mGg3mSQfn742na2T+1/vHDCG5/iTI3X9MKUuYUZVLQhyRsg06mCgDBTd57TxzgZt7P+fYfjRLtA=="
  "resolved" "https://registry.npmjs.org/pbkdf2/-/pbkdf2-3.0.17.tgz"
  "version" "3.0.17"
  dependencies:
    "create-hash" "^1.1.2"
    "create-hmac" "^1.1.4"
    "ripemd160" "^2.0.1"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"pend@~1.2.0":
  "integrity" "sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg=="
  "resolved" "https://registry.npmmirror.com/pend/-/pend-1.2.0.tgz"
  "version" "1.2.0"

"performance-now@^0.2.0":
  "integrity" "sha1-M+8wxcd9TqIcWlOGnZG1bY8lVeU="
  "resolved" "https://registry.npmjs.org/performance-now/-/performance-now-0.2.0.tgz"
  "version" "0.2.0"

"performance-now@^2.1.0":
  "integrity" "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="
  "resolved" "https://registry.npmmirror.com/performance-now/-/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"phantomjs-prebuilt@^2.1.16", "phantomjs-prebuilt@^2.1.7":
  "integrity" "sha512-PIiRzBhW85xco2fuj41FmsyuYHKjKuXWmhjy3A/Y+CMpN/63TV+s9uzfVhsUwFe0G77xWtHBG8xmXf5BqEUEuQ=="
  "resolved" "https://registry.npmmirror.com/phantomjs-prebuilt/-/phantomjs-prebuilt-2.1.16.tgz"
  "version" "2.1.16"
  dependencies:
    "es6-promise" "^4.0.3"
    "extract-zip" "^1.6.5"
    "fs-extra" "^1.0.0"
    "hasha" "^2.2.0"
    "kew" "^0.7.0"
    "progress" "^1.1.8"
    "request" "^2.81.0"
    "request-progress" "^2.0.1"
    "which" "^1.2.10"

"pify@^2.0.0":
  "integrity" "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="
  "resolved" "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^3.0.0":
  "integrity" "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="
  "resolved" "https://registry.npmjs.org/pify/-/pify-3.0.0.tgz"
  "version" "3.0.0"

"pinkie-promise@^2.0.0":
  "integrity" "sha1-ITXW36ejWMBprJsXh3YogihFD/o="
  "resolved" "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "pinkie" "^2.0.0"

"pinkie@^2.0.0":
  "integrity" "sha1-clVrgM+g1IqXToDnckjoDtT3+HA="
  "resolved" "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz"
  "version" "2.0.4"

"pkg-dir@^1.0.0":
  "integrity" "sha1-ektQio1bstYp1EcFb/TpyTFM89Q="
  "resolved" "https://registry.npmjs.org/pkg-dir/-/pkg-dir-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "find-up" "^1.0.0"

"pkg-dir@^2.0.0":
  "integrity" "sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s="
  "resolved" "https://registry.npmjs.org/pkg-dir/-/pkg-dir-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "^2.1.0"

"pluralize@^1.2.1":
  "integrity" "sha1-0aIUg/0iu0HlihL6NCGCMUCJfEU="
  "resolved" "https://registry.npmjs.org/pluralize/-/pluralize-1.2.1.tgz"
  "version" "1.2.1"

"pngjs@^3.3.0":
  "integrity" "sha512-1n3Z4p3IOxArEs1VRXnZ/RXdfEniAUS9jb68g58FIXMNkPJeZd+Qh4Uq7/e0LVxAQGos1eIUrqrt4FpjdnEd+Q=="
  "resolved" "https://registry.npmjs.org/pngjs/-/pngjs-3.3.3.tgz"
  "version" "3.3.3"

"posix-character-classes@^0.1.0":
  "integrity" "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="
  "resolved" "https://registry.npmjs.org/posix-character-classes/-/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"postcss-calc@^5.2.0":
  "integrity" "sha1-d7rnypKK2FcW4v2kLyYb98HWW14="
  "resolved" "http://registry.npmjs.org/postcss-calc/-/postcss-calc-5.3.1.tgz"
  "version" "5.3.1"
  dependencies:
    "postcss" "^5.0.2"
    "postcss-message-helpers" "^2.0.0"
    "reduce-css-calc" "^1.2.6"

"postcss-colormin@^2.1.8":
  "integrity" "sha1-ZjFBfV8OkJo9fsJrJMio0eT5bks="
  "resolved" "https://registry.npmjs.org/postcss-colormin/-/postcss-colormin-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "colormin" "^1.0.5"
    "postcss" "^5.0.13"
    "postcss-value-parser" "^3.2.3"

"postcss-convert-values@^2.3.4":
  "integrity" "sha1-u9hZPFwf0uPRwyK7kl3K6Nrk1i0="
  "resolved" "https://registry.npmjs.org/postcss-convert-values/-/postcss-convert-values-2.6.1.tgz"
  "version" "2.6.1"
  dependencies:
    "postcss" "^5.0.11"
    "postcss-value-parser" "^3.1.2"

"postcss-discard-comments@^2.0.4":
  "integrity" "sha1-vv6J+v1bPazlzM5Rt2uBUUvgDj0="
  "resolved" "http://registry.npmjs.org/postcss-discard-comments/-/postcss-discard-comments-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "postcss" "^5.0.14"

"postcss-discard-duplicates@^2.0.1":
  "integrity" "sha1-uavye4isGIFYpesSq8riAmO5GTI="
  "resolved" "https://registry.npmjs.org/postcss-discard-duplicates/-/postcss-discard-duplicates-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "postcss" "^5.0.4"

"postcss-discard-empty@^2.0.1":
  "integrity" "sha1-0rS9nVztXr2Nyt52QMfXzX9PkrU="
  "resolved" "http://registry.npmjs.org/postcss-discard-empty/-/postcss-discard-empty-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "postcss" "^5.0.14"

"postcss-discard-overridden@^0.1.1":
  "integrity" "sha1-ix6vVU9ob7KIzYdMVWZ7CqNmjVg="
  "resolved" "http://registry.npmjs.org/postcss-discard-overridden/-/postcss-discard-overridden-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "postcss" "^5.0.16"

"postcss-discard-unused@^2.2.1":
  "integrity" "sha1-vOMLLMWR/8Y0Mitfs0ZLbZNPRDM="
  "resolved" "http://registry.npmjs.org/postcss-discard-unused/-/postcss-discard-unused-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "postcss" "^5.0.14"
    "uniqs" "^2.0.0"

"postcss-filter-plugins@^2.0.0":
  "integrity" "sha512-T53GVFsdinJhgwm7rg1BzbeBRomOg9y5MBVhGcsV0CxurUdVj1UlPdKtn7aqYA/c/QVkzKMjq2bSV5dKG5+AwQ=="
  "resolved" "https://registry.npmjs.org/postcss-filter-plugins/-/postcss-filter-plugins-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "postcss" "^5.0.4"

"postcss-load-config@^1.1.0":
  "integrity" "sha1-U56a/J3chiASHr+djDZz4M5Q0oo="
  "resolved" "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cosmiconfig" "^2.1.0"
    "object-assign" "^4.1.0"
    "postcss-load-options" "^1.2.0"
    "postcss-load-plugins" "^2.3.0"

"postcss-load-options@^1.2.0":
  "integrity" "sha1-sJixVZ3awt8EvAuzdfmaXP4rbYw="
  "resolved" "https://registry.npmjs.org/postcss-load-options/-/postcss-load-options-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cosmiconfig" "^2.1.0"
    "object-assign" "^4.1.0"

"postcss-load-plugins@^2.3.0":
  "integrity" "sha1-dFdoEWWZrKLwCfrUJrABdQSdjZI="
  "resolved" "https://registry.npmjs.org/postcss-load-plugins/-/postcss-load-plugins-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "cosmiconfig" "^2.1.1"
    "object-assign" "^4.1.0"

"postcss-merge-idents@^2.1.5":
  "integrity" "sha1-TFUwMTwI4dWzu/PSu8dH4njuonA="
  "resolved" "http://registry.npmjs.org/postcss-merge-idents/-/postcss-merge-idents-2.1.7.tgz"
  "version" "2.1.7"
  dependencies:
    "has" "^1.0.1"
    "postcss" "^5.0.10"
    "postcss-value-parser" "^3.1.1"

"postcss-merge-longhand@^2.0.1":
  "integrity" "sha1-I9kM0Sewp3mUkVMyc5A0oaTz1lg="
  "resolved" "https://registry.npmjs.org/postcss-merge-longhand/-/postcss-merge-longhand-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "postcss" "^5.0.4"

"postcss-merge-rules@^2.0.3":
  "integrity" "sha1-0d9d+qexrMO+VT8OnhDofGG19yE="
  "resolved" "https://registry.npmjs.org/postcss-merge-rules/-/postcss-merge-rules-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "browserslist" "^1.5.2"
    "caniuse-api" "^1.5.2"
    "postcss" "^5.0.4"
    "postcss-selector-parser" "^2.2.2"
    "vendors" "^1.0.0"

"postcss-message-helpers@^2.0.0":
  "integrity" "sha1-pPL0+rbk/gAvCu0ABHjN9S+bpg4="
  "resolved" "https://registry.npmjs.org/postcss-message-helpers/-/postcss-message-helpers-2.0.0.tgz"
  "version" "2.0.0"

"postcss-minify-font-values@^1.0.2":
  "integrity" "sha1-S1jttWZB66fIR0qzUmyv17vey2k="
  "resolved" "http://registry.npmjs.org/postcss-minify-font-values/-/postcss-minify-font-values-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "object-assign" "^4.0.1"
    "postcss" "^5.0.4"
    "postcss-value-parser" "^3.0.2"

"postcss-minify-gradients@^1.0.1":
  "integrity" "sha1-Xb2hE3NwP4PPtKPqOIHY11/15uE="
  "resolved" "http://registry.npmjs.org/postcss-minify-gradients/-/postcss-minify-gradients-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "postcss" "^5.0.12"
    "postcss-value-parser" "^3.3.0"

"postcss-minify-params@^1.0.4":
  "integrity" "sha1-rSzgcTc7lDs9kwo/pZo1jCjW8fM="
  "resolved" "http://registry.npmjs.org/postcss-minify-params/-/postcss-minify-params-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "alphanum-sort" "^1.0.1"
    "postcss" "^5.0.2"
    "postcss-value-parser" "^3.0.2"
    "uniqs" "^2.0.0"

"postcss-minify-selectors@^2.0.4":
  "integrity" "sha1-ssapjAByz5G5MtGkllCBFDEXNb8="
  "resolved" "http://registry.npmjs.org/postcss-minify-selectors/-/postcss-minify-selectors-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "alphanum-sort" "^1.0.2"
    "has" "^1.0.1"
    "postcss" "^5.0.14"
    "postcss-selector-parser" "^2.0.0"

"postcss-modules-extract-imports@^1.2.0":
  "integrity" "sha512-6jt9XZwUhwmRUhb/CkyJY020PYaPJsCyt3UjbaWo6XEbH/94Hmv6MP7fG2C5NDU/BcHzyGYxNtHvM+LTf9HrYw=="
  "resolved" "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "postcss" "^6.0.1"

"postcss-modules-local-by-default@^1.2.0":
  "integrity" "sha1-99gMOYxaOT+nlkRmvRlQCn1hwGk="
  "resolved" "https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "css-selector-tokenizer" "^0.7.0"
    "postcss" "^6.0.1"

"postcss-modules-scope@^1.1.0":
  "integrity" "sha1-1upkmUx5+XtipytCb75gVqGUu5A="
  "resolved" "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "css-selector-tokenizer" "^0.7.0"
    "postcss" "^6.0.1"

"postcss-modules-values@^1.3.0":
  "integrity" "sha1-7P+p1+GSUYOJ9CrQ6D9yrsRW6iA="
  "resolved" "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "icss-replace-symbols" "^1.1.0"
    "postcss" "^6.0.1"

"postcss-normalize-charset@^1.1.0":
  "integrity" "sha1-757nEhLX/nWceO0WL2HtYrXLk/E="
  "resolved" "http://registry.npmjs.org/postcss-normalize-charset/-/postcss-normalize-charset-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "postcss" "^5.0.5"

"postcss-normalize-url@^3.0.7":
  "integrity" "sha1-EI90s/L82viRov+j6kWSJ5/HgiI="
  "resolved" "http://registry.npmjs.org/postcss-normalize-url/-/postcss-normalize-url-3.0.8.tgz"
  "version" "3.0.8"
  dependencies:
    "is-absolute-url" "^2.0.0"
    "normalize-url" "^1.4.0"
    "postcss" "^5.0.14"
    "postcss-value-parser" "^3.2.3"

"postcss-ordered-values@^2.1.0":
  "integrity" "sha1-7sbCpntsQSqNsgQud/6NpD+VwR0="
  "resolved" "https://registry.npmjs.org/postcss-ordered-values/-/postcss-ordered-values-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "postcss" "^5.0.4"
    "postcss-value-parser" "^3.0.1"

"postcss-reduce-idents@^2.2.2":
  "integrity" "sha1-wsbSDMlYKE9qv75j92Cb9AkFmtM="
  "resolved" "http://registry.npmjs.org/postcss-reduce-idents/-/postcss-reduce-idents-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "postcss" "^5.0.4"
    "postcss-value-parser" "^3.0.2"

"postcss-reduce-initial@^1.0.0":
  "integrity" "sha1-aPgGlfBF0IJjqHmtJA343WT2ROo="
  "resolved" "http://registry.npmjs.org/postcss-reduce-initial/-/postcss-reduce-initial-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "postcss" "^5.0.4"

"postcss-reduce-transforms@^1.0.3":
  "integrity" "sha1-/3b02CEkN7McKYpC0uFEQCV3GuE="
  "resolved" "http://registry.npmjs.org/postcss-reduce-transforms/-/postcss-reduce-transforms-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has" "^1.0.1"
    "postcss" "^5.0.8"
    "postcss-value-parser" "^3.0.1"

"postcss-selector-parser@^2.0.0", "postcss-selector-parser@^2.2.2":
  "integrity" "sha1-+UN3iGBsPJrO4W/+jYsWKX8nu5A="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "flatten" "^1.0.2"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-svgo@^2.1.1":
  "integrity" "sha1-tt8YqmE7Zm4TPwittSGcJoSsEI0="
  "resolved" "http://registry.npmjs.org/postcss-svgo/-/postcss-svgo-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "is-svg" "^2.0.0"
    "postcss" "^5.0.14"
    "postcss-value-parser" "^3.2.3"
    "svgo" "^0.7.0"

"postcss-unique-selectors@^2.0.2":
  "integrity" "sha1-mB1X0p3csz57Hf4f1DuGSfkzyh0="
  "resolved" "http://registry.npmjs.org/postcss-unique-selectors/-/postcss-unique-selectors-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "alphanum-sort" "^1.0.1"
    "postcss" "^5.0.4"
    "uniqs" "^2.0.0"

"postcss-value-parser@^3.0.1", "postcss-value-parser@^3.0.2", "postcss-value-parser@^3.1.1", "postcss-value-parser@^3.1.2", "postcss-value-parser@^3.2.3", "postcss-value-parser@^3.3.0":
  "integrity" "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="
  "resolved" "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz"
  "version" "3.3.1"

"postcss-zindex@^2.0.1":
  "integrity" "sha1-0hCd3AVbka9n/EyzsCWUZjnSryI="
  "resolved" "http://registry.npmjs.org/postcss-zindex/-/postcss-zindex-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "has" "^1.0.1"
    "postcss" "^5.0.4"
    "uniqs" "^2.0.0"

"postcss@^5.0.10":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.11":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.12":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.13":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.14", "postcss@^5.2.16":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.16":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.2":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.4":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.5":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.6":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.8":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^6.0.1", "postcss@^6.0.17", "postcss@^6.0.8":
  "integrity" "sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-6.0.23.tgz"
  "version" "6.0.23"
  dependencies:
    "chalk" "^2.4.1"
    "source-map" "^0.6.1"
    "supports-color" "^5.4.0"

"prelude-ls@~1.1.2":
  "integrity" "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="
  "resolved" "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz"
  "version" "1.1.2"

"prepend-http@^1.0.0":
  "integrity" "sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw="
  "resolved" "https://registry.npmjs.org/prepend-http/-/prepend-http-1.0.4.tgz"
  "version" "1.0.4"

"prepend-http@^2.0.0":
  "integrity" "sha1-6SQ0v6XqjBn0HN/UAddBo8gZ2Jc="
  "resolved" "https://registry.npmjs.org/prepend-http/-/prepend-http-2.0.0.tgz"
  "version" "2.0.0"

"preserve@^0.2.0":
  "integrity" "sha1-gV7R9uvGWSb4ZbMQwHE7yzMVzks="
  "resolved" "https://registry.npmjs.org/preserve/-/preserve-0.2.0.tgz"
  "version" "0.2.0"

"prettier@^1.7.0":
  "integrity" "sha512-ZzWuos7TI5CKUeQAtFd6Zhm2s6EpAD/ZLApIhsF9pRvRtM1RFo61dM/4MSRUA0SuLugA/zgrZD8m0BaY46Og7g=="
  "resolved" "https://registry.npmjs.org/prettier/-/prettier-1.16.4.tgz"
  "version" "1.16.4"

"pretty-error@^2.0.2":
  "integrity" "sha1-X0+HyPkeWuPzuoerTPXgOxoX8aM="
  "resolved" "https://registry.npmjs.org/pretty-error/-/pretty-error-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "renderkid" "^2.0.1"
    "utila" "~0.4"

"private@^0.1.6", "private@^0.1.8":
  "integrity" "sha512-VvivMrbvd2nKkiG38qjULzlc+4Vx4wm/whI9pQD35YrARNnhxeiRktSOhSukRLFNlzg6Br/cJPet5J/u19r/mg=="
  "resolved" "https://registry.npmjs.org/private/-/private-0.1.8.tgz"
  "version" "0.1.8"

"process-nextick-args@~2.0.0":
  "integrity" "sha512-MtEC1TqN0EU5nephaJ4rAtThHtC86dNN9qCuEhtshvpVBkAW5ZO7BASN9REnF9eoXGcRub+pFuKEpOHE+HbEMw=="
  "resolved" "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.0.tgz"
  "version" "2.0.0"

"process@^0.11.10":
  "integrity" "sha1-czIwDoQBYb2j5podHZGn1LwW8YI="
  "resolved" "https://registry.npmjs.org/process/-/process-0.11.10.tgz"
  "version" "0.11.10"

"progress@^1.1.8":
  "integrity" "sha1-4mDHj2Fhzdmw5WzD4Khd4Xx6V74="
  "resolved" "http://registry.npmjs.org/progress/-/progress-1.1.8.tgz"
  "version" "1.1.8"

"promise-inflight@^1.0.1":
  "integrity" "sha1-mEcocL8igTL8vdhoEputEsPAKeM="
  "resolved" "https://registry.npmjs.org/promise-inflight/-/promise-inflight-1.0.1.tgz"
  "version" "1.0.1"

"promise@^7.1.1":
  "integrity" "sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg=="
  "resolved" "https://registry.npmjs.org/promise/-/promise-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "asap" "~2.0.3"

"proxy-addr@~2.0.4":
  "integrity" "sha512-5erio2h9jp5CHGwcybmxmVqHmnCBZeewlfJ0pex+UW7Qny7OOZXTtH56TGNyBizkgiOwhJtMKrVzDTeKcySZwA=="
  "resolved" "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "forwarded" "~0.1.2"
    "ipaddr.js" "1.8.0"

"proxy-agent@2.0.0":
  "integrity" "sha1-V+tTR6qAXXTsaByyVknbo5yTNJk="
  "resolved" "http://registry.npmjs.org/proxy-agent/-/proxy-agent-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "agent-base" "2"
    "debug" "2"
    "extend" "3"
    "http-proxy-agent" "1"
    "https-proxy-agent" "1"
    "lru-cache" "~2.6.5"
    "pac-proxy-agent" "1"
    "socks-proxy-agent" "2"

"prr@~1.0.1":
  "integrity" "sha1-0/wRS6BplaRexok/SEzrHXj19HY="
  "resolved" "https://registry.npmjs.org/prr/-/prr-1.0.1.tgz"
  "version" "1.0.1"

"pseudomap@^1.0.2":
  "integrity" "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="
  "resolved" "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz"
  "version" "1.0.2"

"psl@^1.1.28":
  "integrity" "sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag=="
  "resolved" "https://registry.npmmirror.com/psl/-/psl-1.9.0.tgz"
  "version" "1.9.0"

"public-encrypt@^4.0.0":
  "integrity" "sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q=="
  "resolved" "https://registry.npmjs.org/public-encrypt/-/public-encrypt-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "browserify-rsa" "^4.0.0"
    "create-hash" "^1.1.0"
    "parse-asn1" "^5.0.0"
    "randombytes" "^2.0.1"
    "safe-buffer" "^5.1.2"

"pump@^2.0.0":
  "integrity" "sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA=="
  "resolved" "https://registry.npmjs.org/pump/-/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pump@^2.0.1":
  "integrity" "sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA=="
  "resolved" "https://registry.npmjs.org/pump/-/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pump@^3.0.0":
  "integrity" "sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww=="
  "resolved" "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pumpify@^1.3.3":
  "integrity" "sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ=="
  "resolved" "https://registry.npmjs.org/pumpify/-/pumpify-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "duplexify" "^3.6.0"
    "inherits" "^2.0.3"
    "pump" "^2.0.0"

"punycode@^1.2.4":
  "integrity" "sha1-wNWmOycYgArY4esPpSachN1BhF4="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^1.4.1":
  "integrity" "sha1-wNWmOycYgArY4esPpSachN1BhF4="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^2.1.0":
  "integrity" "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="
  "resolved" "https://registry.npmmirror.com/punycode/-/punycode-2.3.1.tgz"
  "version" "2.3.1"

"punycode@^2.1.1":
  "integrity" "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="
  "resolved" "https://registry.npmmirror.com/punycode/-/punycode-2.3.1.tgz"
  "version" "2.3.1"

"punycode@1.3.2":
  "integrity" "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz"
  "version" "1.3.2"

"q@^1.1.2":
  "integrity" "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc="
  "resolved" "https://registry.npmjs.org/q/-/q-1.5.1.tgz"
  "version" "1.5.1"

"q@1.4.1":
  "integrity" "sha1-VXBbzZPF82c1MMLCy8DCs63cKG4="
  "resolved" "https://registry.npmjs.org/q/-/q-1.4.1.tgz"
  "version" "1.4.1"

"qjobs@^1.1.4":
  "integrity" "sha512-8YOJEHtxpySA3fFDyCRxA+UUV+fA+rTWnuWvylOK/NCjhY+b4ocCtmu8TtsWb+mYeU+GCHf/S66KZF/AsteKHg=="
  "resolved" "https://registry.npmjs.org/qjobs/-/qjobs-1.2.0.tgz"
  "version" "1.2.0"

"qrcode@^1.3.0":
  "integrity" "sha512-SH7V13AcJusH3GT8bMNOGz4w0L+LjcpNOU/NiOgtBhT/5DoWeZE6D5ntMJnJ84AMkoaM4kjJJoHoh9g++8lWFg=="
  "resolved" "https://registry.npmjs.org/qrcode/-/qrcode-1.3.3.tgz"
  "version" "1.3.3"
  dependencies:
    "can-promise" "0.0.1"
    "dijkstrajs" "^1.0.1"
    "isarray" "^2.0.1"
    "pngjs" "^3.3.0"
    "yargs" "^12.0.5"

"qs@^6.11.2":
  "integrity" "sha512-tDNIz22aBzCDxLtVH++VnTfzxlfeK5CbqohpSqpJgj1Wg/cQbStNAz3NuqCs5vV+pjBsK4x4pN9HlVh7rcYRiA=="
  "resolved" "https://registry.npmmirror.com/qs/-/qs-6.11.2.tgz"
  "version" "6.11.2"
  dependencies:
    "side-channel" "^1.0.4"

"qs@~6.4.0":
  "integrity" "sha1-E+JtKK1rD/qpExLNO/cI7TUecjM="
  "resolved" "https://registry.npmjs.org/qs/-/qs-6.4.0.tgz"
  "version" "6.4.0"

"qs@~6.5.2":
  "integrity" "sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA=="
  "resolved" "https://registry.npmmirror.com/qs/-/qs-6.5.3.tgz"
  "version" "6.5.3"

"qs@6.5.2":
  "integrity" "sha512-N5ZAX4/LxJmF+7wN74pUD6qAh9/wnvdQcjq9TZjevvXzSUo7bfmw91saqMjzGS2xq91/odN2dW/WOl7qQHNDGA=="
  "resolved" "https://registry.npmmirror.com/qs/-/qs-6.5.2.tgz"
  "version" "6.5.2"

"query-string@^4.1.0":
  "integrity" "sha1-u7aTucqRXCMlFbIosaArYJBD2+s="
  "resolved" "https://registry.npmjs.org/query-string/-/query-string-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"query-string@^5.0.1":
  "integrity" "sha512-gjWOsm2SoGlgLEdAGt7a6slVOk9mGiXmPFMqrEhLQ68rhQuBnpfs3+EmlvqKyxnCo9/PPlF+9MtY02S1aFg+Jw=="
  "resolved" "http://registry.npmjs.org/query-string/-/query-string-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "decode-uri-component" "^0.2.0"
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"querystring-es3@^0.2.0":
  "integrity" "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM="
  "resolved" "https://registry.npmjs.org/querystring-es3/-/querystring-es3-0.2.1.tgz"
  "version" "0.2.1"

"querystring@^0.2.0", "querystring@0.2.0":
  "integrity" "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="
  "resolved" "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz"
  "version" "0.2.0"

"randomatic@^3.0.0":
  "integrity" "sha512-TuDE5KxZ0J461RVjrJZCJc+J+zCkTb1MbH9AQUq68sMhOMcy9jLcb3BrZKgp9q9Ncltdg4QVqWrH02W2EFFVYw=="
  "resolved" "https://registry.npmjs.org/randomatic/-/randomatic-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "is-number" "^4.0.0"
    "kind-of" "^6.0.0"
    "math-random" "^1.0.1"

"randombytes@^2.0.0", "randombytes@^2.0.1", "randombytes@^2.0.5":
  "integrity" "sha512-CIQ5OFxf4Jou6uOKe9t1AOgqpeU5fd70A8NPdHSGeYXqXsPe6peOwI0cUl88RWZ6sP1vPMV3avd/R6cZ5/sP1A=="
  "resolved" "https://registry.npmjs.org/randombytes/-/randombytes-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "safe-buffer" "^5.1.0"

"randomfill@^1.0.3":
  "integrity" "sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw=="
  "resolved" "https://registry.npmjs.org/randomfill/-/randomfill-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "randombytes" "^2.0.5"
    "safe-buffer" "^5.1.0"

"range-parser@^1.0.3", "range-parser@^1.2.0", "range-parser@~1.2.0":
  "integrity" "sha1-9JvmtIeJTdxA3MlKMi9hEJLgDV4="
  "resolved" "https://registry.npmjs.org/range-parser/-/range-parser-1.2.0.tgz"
  "version" "1.2.0"

"raw-body@2", "raw-body@2.3.3":
  "integrity" "sha512-9esiElv1BrZoI3rCDuOuKCBRbuApGGaDPQfjSflGxdy4oyzqghxu6klEkkVIvBje+FF0BX9coEv8KqW6X/7njw=="
  "resolved" "https://registry.npmjs.org/raw-body/-/raw-body-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "bytes" "3.0.0"
    "http-errors" "1.6.3"
    "iconv-lite" "0.4.23"
    "unpipe" "1.0.0"

"rc@^1.2.7":
  "integrity" "sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw=="
  "resolved" "https://registry.npmjs.org/rc/-/rc-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "deep-extend" "^0.6.0"
    "ini" "~1.3.0"
    "minimist" "^1.2.0"
    "strip-json-comments" "~2.0.1"

"read-pkg-up@^1.0.1":
  "integrity" "sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI="
  "resolved" "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "find-up" "^1.0.0"
    "read-pkg" "^1.0.0"

"read-pkg@^1.0.0":
  "integrity" "sha1-9f+qXs0pyzHAR0vKfXVra7KePyg="
  "resolved" "https://registry.npmjs.org/read-pkg/-/read-pkg-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "load-json-file" "^1.0.0"
    "normalize-package-data" "^2.3.2"
    "path-type" "^1.0.0"

"readable-stream@^1.0.26-2", "readable-stream@^1.0.26-4", "readable-stream@1 || 2", "readable-stream@1.1.x":
  "integrity" "sha1-fPTFTvZI44EwhMY23SB54WbAgdk="
  "resolved" "http://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz"
  "version" "1.1.14"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.1"
    "isarray" "0.0.1"
    "string_decoder" "~0.10.x"

"readable-stream@^2.0.0":
  "integrity" "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw=="
  "resolved" "http://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.0.1":
  "integrity" "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw=="
  "resolved" "http://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.0.2":
  "integrity" "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw=="
  "resolved" "http://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.0.6":
  "integrity" "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.1.5":
  "integrity" "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw=="
  "resolved" "http://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.2.2":
  "integrity" "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw=="
  "resolved" "http://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.3.3":
  "integrity" "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw=="
  "resolved" "http://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.3.6":
  "integrity" "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw=="
  "resolved" "http://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.6":
  "integrity" "sha512-DkN66hPyqDhnIQ6Jcsvx9bFjhw214O4poMBcIMgPVpQvNy9a0e0Uhg5SqySyDKAmUlwt8LonTBz1ezOnM8pUdA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@~1.0.2":
  "integrity" "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw="
  "resolved" "http://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz"
  "version" "1.0.34"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.1"
    "isarray" "0.0.1"
    "string_decoder" "~0.10.x"

"readable-stream@~2.3.6":
  "integrity" "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw=="
  "resolved" "http://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@1.0":
  "integrity" "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw="
  "resolved" "http://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz"
  "version" "1.0.34"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.1"
    "isarray" "0.0.1"
    "string_decoder" "~0.10.x"

"readable-stream@3":
  "integrity" "sha512-DkN66hPyqDhnIQ6Jcsvx9bFjhw214O4poMBcIMgPVpQvNy9a0e0Uhg5SqySyDKAmUlwt8LonTBz1ezOnM8pUdA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readdirp@^2.0.0", "readdirp@^2.2.1":
  "integrity" "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ=="
  "resolved" "https://registry.npmjs.org/readdirp/-/readdirp-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "graceful-fs" "^4.1.11"
    "micromatch" "^3.1.10"
    "readable-stream" "^2.0.2"

"readline2@^1.0.1":
  "integrity" "sha1-QQWWCP/BVHV7cV2ZidGZ/783LjU="
  "resolved" "https://registry.npmjs.org/readline2/-/readline2-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "code-point-at" "^1.0.0"
    "is-fullwidth-code-point" "^1.0.0"
    "mute-stream" "0.0.5"

"rechoir@^0.6.2":
  "integrity" "sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q="
  "resolved" "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz"
  "version" "0.6.2"
  dependencies:
    "resolve" "^1.1.6"

"redent@^1.0.0":
  "integrity" "sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94="
  "resolved" "https://registry.npmjs.org/redent/-/redent-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "indent-string" "^2.1.0"
    "strip-indent" "^1.0.1"

"reduce-css-calc@^1.2.6":
  "integrity" "sha1-dHyRTgSWFKTJz7umKYca0dKSdxY="
  "resolved" "http://registry.npmjs.org/reduce-css-calc/-/reduce-css-calc-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "balanced-match" "^0.4.2"
    "math-expression-evaluator" "^1.2.14"
    "reduce-function-call" "^1.0.1"

"reduce-function-call@^1.0.1":
  "integrity" "sha1-WiAL+S4ON3UXUv5FsKszD9S2vpk="
  "resolved" "https://registry.npmjs.org/reduce-function-call/-/reduce-function-call-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "balanced-match" "^0.4.2"

"regenerate@^1.2.1":
  "integrity" "sha512-1G6jJVDWrt0rK99kBjvEtziZNCICAuvIPkSiUFIQxVP06RCVpq3dmDo2oi6ABpYaDYaTRr67BEhL8r1wgEZZKg=="
  "resolved" "https://registry.npmjs.org/regenerate/-/regenerate-1.4.0.tgz"
  "version" "1.4.0"

"regenerator-runtime@^0.10.5":
  "integrity" "sha1-M2w+/BIgrc7dosn6tntaeVWjNlg="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.10.5.tgz"
  "version" "0.10.5"

"regenerator-runtime@^0.11.0":
  "integrity" "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz"
  "version" "0.11.1"

"regenerator-runtime@^0.14.0":
  "integrity" "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="
  "resolved" "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"
  "version" "0.14.1"

"regenerator-transform@^0.10.0":
  "integrity" "sha512-PJepbvDbuK1xgIgnau7Y90cwaAmO/LCLMI2mPvaXq2heGMR3aWW5/BQvYrhJ8jgmQjXewXvBjzfqKcVOmhjZ6Q=="
  "resolved" "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.10.1.tgz"
  "version" "0.10.1"
  dependencies:
    "babel-runtime" "^6.18.0"
    "babel-types" "^6.19.0"
    "private" "^0.1.6"

"regex-cache@^0.4.2":
  "integrity" "sha512-nVIZwtCjkC9YgvWkpM55B5rBhBYRZhAaJbgcFYXXsHnbZ9UZI9nnVWYZpBlCqv9ho2eZryPnWrZGsOdPwVWXWQ=="
  "resolved" "https://registry.npmjs.org/regex-cache/-/regex-cache-0.4.4.tgz"
  "version" "0.4.4"
  dependencies:
    "is-equal-shallow" "^0.1.3"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A=="
  "resolved" "https://registry.npmjs.org/regex-not/-/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regexpu-core@^1.0.0":
  "integrity" "sha1-hqdj9Y7k18L2sQLkdkBQ3n7ZDGs="
  "resolved" "https://registry.npmjs.org/regexpu-core/-/regexpu-core-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "regenerate" "^1.2.1"
    "regjsgen" "^0.2.0"
    "regjsparser" "^0.1.4"

"regexpu-core@^2.0.0":
  "integrity" "sha1-SdA4g3uNz4v6W5pCE5k45uoq4kA="
  "resolved" "https://registry.npmjs.org/regexpu-core/-/regexpu-core-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "regenerate" "^1.2.1"
    "regjsgen" "^0.2.0"
    "regjsparser" "^0.1.4"

"regjsgen@^0.2.0":
  "integrity" "sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc="
  "resolved" "https://registry.npmjs.org/regjsgen/-/regjsgen-0.2.0.tgz"
  "version" "0.2.0"

"regjsparser@^0.1.4":
  "integrity" "sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw="
  "resolved" "https://registry.npmjs.org/regjsparser/-/regjsparser-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "jsesc" "~0.5.0"

"relateurl@0.2.x":
  "integrity" "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="
  "resolved" "https://registry.npmjs.org/relateurl/-/relateurl-0.2.7.tgz"
  "version" "0.2.7"

"remove-trailing-separator@^1.0.1":
  "integrity" "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="
  "resolved" "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"renderkid@^2.0.1":
  "integrity" "sha512-FsygIxevi1jSiPY9h7vZmBFUbAOcbYm9UwyiLNdVsLRs/5We9Ob5NMPbGYUTWiLq5L+ezlVdE0A8bbME5CWTpg=="
  "resolved" "https://registry.npmjs.org/renderkid/-/renderkid-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "css-select" "^1.1.0"
    "dom-converter" "~0.2"
    "htmlparser2" "~3.3.0"
    "strip-ansi" "^3.0.0"
    "utila" "^0.4.0"

"repeat-element@^1.1.2":
  "integrity" "sha512-ahGq0ZnV5m5XtZLMb+vP76kcAM5nkLqk0lpqAuojSKGgQtn4eRi4ZZGm2olo2zKFH+sMsWaqOCW1dqAnOru72g=="
  "resolved" "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.3.tgz"
  "version" "1.1.3"

"repeat-string@^0.2.2":
  "integrity" "sha1-x6jTI2BoNiBZp+RlH8aITosftK4="
  "resolved" "https://registry.npmjs.org/repeat-string/-/repeat-string-0.2.2.tgz"
  "version" "0.2.2"

"repeat-string@^1.5.2", "repeat-string@^1.6.1":
  "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc="
  "resolved" "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"repeating@^2.0.0":
  "integrity" "sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo="
  "resolved" "https://registry.npmjs.org/repeating/-/repeating-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-finite" "^1.0.0"

"request-progress@^2.0.1":
  "integrity" "sha512-dxdraeZVUNEn9AvLrxkgB2k6buTlym71dJk1fk4v8j3Ou3RKNm07BcgbHdj2lLgYGfqX71F+awb1MR+tWPFJzA=="
  "resolved" "https://registry.npmmirror.com/request-progress/-/request-progress-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "throttleit" "^1.0.0"

"request@^2.81.0":
  "integrity" "sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw=="
  "resolved" "https://registry.npmmirror.com/request/-/request-2.88.2.tgz"
  "version" "2.88.2"
  dependencies:
    "aws-sign2" "~0.7.0"
    "aws4" "^1.8.0"
    "caseless" "~0.12.0"
    "combined-stream" "~1.0.6"
    "extend" "~3.0.2"
    "forever-agent" "~0.6.1"
    "form-data" "~2.3.2"
    "har-validator" "~5.1.3"
    "http-signature" "~1.2.0"
    "is-typedarray" "~1.0.0"
    "isstream" "~0.1.2"
    "json-stringify-safe" "~5.0.1"
    "mime-types" "~2.1.19"
    "oauth-sign" "~0.9.0"
    "performance-now" "^2.1.0"
    "qs" "~6.5.2"
    "safe-buffer" "^5.1.2"
    "tough-cookie" "~2.5.0"
    "tunnel-agent" "^0.6.0"
    "uuid" "^3.3.2"

"request@2.81.0":
  "integrity" "sha1-xpKJRqDgbF+Nb4qTM0af/aRimKA="
  "resolved" "https://registry.npmjs.org/request/-/request-2.81.0.tgz"
  "version" "2.81.0"
  dependencies:
    "aws-sign2" "~0.6.0"
    "aws4" "^1.2.1"
    "caseless" "~0.12.0"
    "combined-stream" "~1.0.5"
    "extend" "~3.0.0"
    "forever-agent" "~0.6.1"
    "form-data" "~2.1.1"
    "har-validator" "~4.2.1"
    "hawk" "~3.1.3"
    "http-signature" "~1.1.0"
    "is-typedarray" "~1.0.0"
    "isstream" "~0.1.2"
    "json-stringify-safe" "~5.0.1"
    "mime-types" "~2.1.7"
    "oauth-sign" "~0.8.1"
    "performance-now" "^0.2.0"
    "qs" "~6.4.0"
    "safe-buffer" "^5.0.1"
    "stringstream" "~0.0.4"
    "tough-cookie" "~2.3.0"
    "tunnel-agent" "^0.6.0"
    "uuid" "^3.0.0"

"require-directory@^2.1.1":
  "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
  "resolved" "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^1.1.0":
  "integrity" "sha1-UpyczvJzgK3+yaL5ZbZJu+5jZBg="
  "resolved" "https://registry.npmjs.org/require-from-string/-/require-from-string-1.2.1.tgz"
  "version" "1.2.1"

"require-main-filename@^1.0.1":
  "integrity" "sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE="
  "resolved" "https://registry.npmjs.org/require-main-filename/-/require-main-filename-1.0.1.tgz"
  "version" "1.0.1"

"require-uncached@^1.0.2":
  "integrity" "sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM="
  "resolved" "https://registry.npmjs.org/require-uncached/-/require-uncached-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "caller-path" "^0.1.0"
    "resolve-from" "^1.0.0"

"requires-port@^1.0.0":
  "integrity" "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="
  "resolved" "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resize-observer-polyfill@^1.5.0", "resize-observer-polyfill@^1.5.1":
  "integrity" "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="
  "resolved" "https://registry.npmmirror.com/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-from@^1.0.0":
  "integrity" "sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-1.0.1.tgz"
  "version" "1.0.1"

"resolve-url@^0.2.1":
  "integrity" "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="
  "resolved" "https://registry.npmjs.org/resolve-url/-/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.1.6", "resolve@^1.10.0", "resolve@^1.4.0":
  "integrity" "sha512-3sUr9aq5OfSg2S9pNtPA9hL1FVEAjvfOC4leW0SNf/mpnaakz2a9femSd6LqAww2RaFctwyf1lCqnTHuF1rxDg=="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "path-parse" "^1.0.6"

"resolve@1.1.x":
  "integrity" "sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-1.1.7.tgz"
  "version" "1.1.7"

"responselike@1.0.2":
  "integrity" "sha1-kYcg7ztjHFZCvgaPFa3lpG9Loec="
  "resolved" "https://registry.npmjs.org/responselike/-/responselike-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "lowercase-keys" "^1.0.0"

"restore-cursor@^1.0.1":
  "integrity" "sha1-NGYfRohjJ/7SmRR5FSJS35LapUE="
  "resolved" "https://registry.npmjs.org/restore-cursor/-/restore-cursor-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "exit-hook" "^1.0.0"
    "onetime" "^1.0.0"

"restore-cursor@^2.0.0":
  "integrity" "sha1-n37ih/gv0ybU/RYpI9YhKe7g368="
  "resolved" "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "onetime" "^2.0.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "integrity" "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg=="
  "resolved" "https://registry.npmjs.org/ret/-/ret-0.1.15.tgz"
  "version" "0.1.15"

"right-align@^0.1.1":
  "integrity" "sha1-YTObci/mo1FWiSENJOFMlhSGE+8="
  "resolved" "https://registry.npmjs.org/right-align/-/right-align-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "align-text" "^0.1.1"

"rimraf@^2.5.4", "rimraf@^2.6.0", "rimraf@^2.6.1", "rimraf@^2.6.2", "rimraf@~2.6.2":
  "integrity" "sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA=="
  "resolved" "https://registry.npmjs.org/rimraf/-/rimraf-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "glob" "^7.1.3"

"ripemd160@^2.0.0", "ripemd160@^2.0.1":
  "integrity" "sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA=="
  "resolved" "https://registry.npmjs.org/ripemd160/-/ripemd160-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"

"run-async@^0.1.0":
  "integrity" "sha1-yK1KXhEGYeQCp9IbUw4AnyX444k="
  "resolved" "https://registry.npmjs.org/run-async/-/run-async-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "once" "^1.3.0"

"run-queue@^1.0.0", "run-queue@^1.0.3":
  "integrity" "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec="
  "resolved" "https://registry.npmjs.org/run-queue/-/run-queue-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "aproba" "^1.1.1"

"rx-lite@^3.1.2":
  "integrity" "sha1-Gc5QLKVyZl87ZHsQk5+X/RYV8QI="
  "resolved" "https://registry.npmjs.org/rx-lite/-/rx-lite-3.1.2.tgz"
  "version" "3.1.2"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@^5.1.1", "safe-buffer@^5.1.2", "safe-buffer@~5.1.0", "safe-buffer@~5.1.1", "safe-buffer@5.1.2":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-regex@^1.1.0":
  "integrity" "sha1-QKNmnzsHfR6UPURinhV91IAjvy4="
  "resolved" "https://registry.npmjs.org/safe-regex/-/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@^2.0.2", "safer-buffer@^2.1.0", "safer-buffer@>= 2.1.2 < 3", "safer-buffer@~2.1.0":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"samsam@^1.1.3", "samsam@1.x":
  "integrity" "sha512-1HwIYD/8UlOtFS3QO3w7ey+SdSDFE4HRNLZoZRYVQefrOY3l17epswImeB1ijgJFQJodIaHcwkp3r/myBjFVbg=="
  "resolved" "https://registry.npmjs.org/samsam/-/samsam-1.3.0.tgz"
  "version" "1.3.0"

"sax@^1.2.4":
  "integrity" "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="
  "resolved" "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz"
  "version" "1.2.4"

"sax@~1.2.1":
  "integrity" "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="
  "resolved" "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz"
  "version" "1.2.4"

"schema-utils@^0.3.0":
  "integrity" "sha1-9YdyIs4+kx7a4DnxfrNxbnE3+M8="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "ajv" "^5.0.0"

"select2@^4.0.7-rc.0":
  "integrity" "sha512-1JeB87s6oN/TDxQQYCvS5EFoQyvV6eYMZZ0AeA4tdFDYWN3BAGZ8npr17UBFddU0lgAt3H0yjX3X6/ekOj1yjw=="
  "resolved" "https://registry.npmmirror.com/select2/-/select2-4.0.13.tgz"
  "version" "4.0.13"

"select2@^4.1.0-rc.0":
  "integrity" "sha512-Hr9TdhyHCZUtwznEH2CBf7967mEM0idtJ5nMtjvk3Up5tPukOLXbHUNmh10oRfeNIhj+3GD3niu+g6sVK+gK0A=="
  "resolved" "https://registry.npmmirror.com/select2/-/select2-4.1.0-rc.0.tgz"
  "version" "4.1.0-rc.0"

"selenium-server@^3.0.1":
  "integrity" "sha512-pL7T1YtAqOEXiBbTx0KdZMkE2U7PYucemd7i0nDLcxcR1APXYZlJfNr5hrvL3mZgwXb7AJEZPINzC6mDU3eP5g=="
  "resolved" "https://registry.npmjs.org/selenium-server/-/selenium-server-3.141.59.tgz"
  "version" "3.141.59"

"semver@^5.3.0", "semver@^5.4.1", "semver@^5.5.0", "semver@2 || 3 || 4 || 5":
  "integrity" "sha512-RS9R6R35NYgQn++fkDWaOmqGoj4Ek9gGs+DPxNUZKuwE183xjJroKvyo1IzVFeXvUrvmALy6FWD5xrdJT25gMg=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-5.6.0.tgz"
  "version" "5.6.0"

"semver@~4.3.3":
  "integrity" "sha1-MAvG4OhjdPe6YQaLWx7NV/xlMto="
  "resolved" "http://registry.npmjs.org/semver/-/semver-4.3.6.tgz"
  "version" "4.3.6"

"semver@~5.0.1":
  "integrity" "sha1-d0Zt5YnNXTyV8TiqeLxWmjy10no="
  "resolved" "http://registry.npmjs.org/semver/-/semver-5.0.3.tgz"
  "version" "5.0.3"

"send@0.16.2":
  "integrity" "sha512-E64YFPUssFHEFBvpbbjr44NCLtI1AohxQ8ZSiJjQLskAdKuriYEP6VyGEsRDH8ScozGpkaX1BGvhanqCwkcEZw=="
  "resolved" "https://registry.npmjs.org/send/-/send-0.16.2.tgz"
  "version" "0.16.2"
  dependencies:
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "destroy" "~1.0.4"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "~1.6.2"
    "mime" "1.4.1"
    "ms" "2.0.0"
    "on-finished" "~2.3.0"
    "range-parser" "~1.2.0"
    "statuses" "~1.4.0"

"serialize-javascript@^1.4.0":
  "integrity" "sha512-A5MOagrPFga4YaKQSWHryl7AXvbQkEqpw4NNYMTNYUNV51bA8ABHgYFpqKx+YFFrw59xMV1qGH1R4AgoNIVgCw=="
  "resolved" "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-1.6.1.tgz"
  "version" "1.6.1"

"serve-static@1.13.2":
  "integrity" "sha512-p/tdJrO4U387R9oMjb1oj7qSMaMfmOyd4j9hOFoxZe2baQszgHcSWjuya/CiT5kgZZKRudHNOA0pYXOl8rQ5nw=="
  "resolved" "https://registry.npmjs.org/serve-static/-/serve-static-1.13.2.tgz"
  "version" "1.13.2"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.2"
    "send" "0.16.2"

"set-blocking@^2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-blocking@~2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-function-length@^1.1.1":
  "integrity" "sha512-4DBHDoyHlM1IRPGYcoxexgh67y4ueR53FKV1yyxwFMY7aCqcN/38M1+SwZ/qJQ8iLv7+ck385ot4CcisOAPT9w=="
  "resolved" "https://registry.npmmirror.com/set-function-length/-/set-function-length-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "define-data-property" "^1.1.1"
    "function-bind" "^1.1.2"
    "get-intrinsic" "^1.2.2"
    "gopd" "^1.0.1"
    "has-property-descriptors" "^1.0.1"

"set-value@^2.0.0", "set-value@^2.0.1":
  "integrity" "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs="
  "resolved" "https://registry.npm.taobao.org/set-value/download/set-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setimmediate@^1.0.4":
  "integrity" "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="
  "resolved" "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.1.0":
  "integrity" "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ=="
  "resolved" "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"sha.js@^2.4.0", "sha.js@^2.4.8":
  "integrity" "sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ=="
  "resolved" "http://registry.npmjs.org/sha.js/-/sha.js-2.4.11.tgz"
  "version" "2.4.11"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"shebang-command@^1.2.0":
  "integrity" "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shelljs@^0.7.5", "shelljs@^0.7.6":
  "integrity" "sha1-3svPh0sNHl+3LhSxZKloMEjprLM="
  "resolved" "https://registry.npmjs.org/shelljs/-/shelljs-0.7.8.tgz"
  "version" "0.7.8"
  dependencies:
    "glob" "^7.0.0"
    "interpret" "^1.0.0"
    "rechoir" "^0.6.2"

"side-channel@^1.0.4":
  "integrity" "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw=="
  "resolved" "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.0"
    "get-intrinsic" "^1.0.2"
    "object-inspect" "^1.9.0"

"signal-exit@^3.0.0", "signal-exit@^3.0.2":
  "integrity" "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0="
  "resolved" "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.2.tgz"
  "version" "3.0.2"

"sinon-chai@^2.8.0", "sinon-chai@>=2.9.0 <4":
  "integrity" "sha512-9stIF1utB0ywNHNT7RgiXbdmen8QDCRsrTjw+G9TgKt1Yexjiv8TOWZ6WHsTPz57Yky3DIswZvEqX8fpuHNDtQ=="
  "resolved" "https://registry.npmjs.org/sinon-chai/-/sinon-chai-2.14.0.tgz"
  "version" "2.14.0"

"sinon@^1.4.0 || ^2.1.0 || ^3.0.0 || ^4.0.0", "sinon@^2.1.0", "sinon@>=2.1.0 <5":
  "integrity" "sha512-vFTrO9Wt0ECffDYIPSP/E5bBugt0UjcBQOfQUMh66xzkyPEnhl/vM2LRZi2ajuTdkH07sA6DzrM6KvdvGIH8xw=="
  "resolved" "https://registry.npmjs.org/sinon/-/sinon-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "diff" "^3.1.0"
    "formatio" "1.2.0"
    "lolex" "^1.6.0"
    "native-promise-only" "^0.8.1"
    "path-to-regexp" "^1.7.0"
    "samsam" "^1.1.3"
    "text-encoding" "0.6.4"
    "type-detect" "^4.0.0"

"slash@^1.0.0":
  "integrity" "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU="
  "resolved" "https://registry.npmjs.org/slash/-/slash-1.0.0.tgz"
  "version" "1.0.0"

"slice-ansi@0.0.4":
  "integrity" "sha1-7b+JA/ZvfOL46v1s7tZeJkyDGzU="
  "resolved" "http://registry.npmjs.org/slice-ansi/-/slice-ansi-0.0.4.tgz"
  "version" "0.0.4"

"smart-buffer@^1.0.13":
  "integrity" "sha1-fxFLW2X6s+KjWqd1uxLw0cZJvxY="
  "resolved" "https://registry.npmjs.org/smart-buffer/-/smart-buffer-1.1.15.tgz"
  "version" "1.1.15"

"snapdragon-node@^2.0.1":
  "integrity" "sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw=="
  "resolved" "https://registry.npmjs.org/snapdragon-node/-/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ=="
  "resolved" "https://registry.npmjs.org/snapdragon-util/-/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg=="
  "resolved" "https://registry.npmjs.org/snapdragon/-/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"sntp@1.x.x":
  "integrity" "sha1-ZUEYTMkK7qbG57NeJlkIJEPGYZg="
  "resolved" "https://registry.npmjs.org/sntp/-/sntp-1.0.9.tgz"
  "version" "1.0.9"
  dependencies:
    "hoek" "2.x.x"

"socket.io-adapter@0.5.0":
  "integrity" "sha1-y21LuL7IHhB4uZZ3+c7QBGBmu4s="
  "resolved" "https://registry.npmjs.org/socket.io-adapter/-/socket.io-adapter-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "debug" "2.3.3"
    "socket.io-parser" "2.3.1"

"socket.io-client@1.7.3":
  "integrity" "sha1-sw6GqhDV7zVGYBwJzeR2Xjgdo3c="
  "resolved" "https://registry.npmjs.org/socket.io-client/-/socket.io-client-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "backo2" "1.0.2"
    "component-bind" "1.0.0"
    "component-emitter" "1.2.1"
    "debug" "2.3.3"
    "engine.io-client" "1.8.3"
    "has-binary" "0.1.7"
    "indexof" "0.0.1"
    "object-component" "0.0.3"
    "parseuri" "0.0.5"
    "socket.io-parser" "2.3.1"
    "to-array" "0.1.4"

"socket.io-parser@2.3.1":
  "integrity" "sha1-3VMgJRA85Clpcya+/WQAX8/ltKA="
  "resolved" "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-2.3.1.tgz"
  "version" "2.3.1"
  dependencies:
    "component-emitter" "1.1.2"
    "debug" "2.2.0"
    "isarray" "0.0.1"
    "json3" "3.3.2"

"socket.io@1.7.3":
  "integrity" "sha1-uK+cq6AJSeVo42nxMn6pvp6iRhs="
  "resolved" "https://registry.npmjs.org/socket.io/-/socket.io-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "debug" "2.3.3"
    "engine.io" "1.8.3"
    "has-binary" "0.1.7"
    "object-assign" "4.1.0"
    "socket.io-adapter" "0.5.0"
    "socket.io-client" "1.7.3"
    "socket.io-parser" "2.3.1"

"socks-proxy-agent@2":
  "integrity" "sha512-sFtmYqdUK5dAMh85H0LEVFUCO7OhJJe1/z2x/Z6mxp3s7/QPf1RkZmpZy+BpuU0bEjcV9npqKjq9Y3kwFUjnxw=="
  "resolved" "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "agent-base" "2"
    "extend" "3"
    "socks" "~1.1.5"

"socks@~1.1.5":
  "integrity" "sha1-W4t/x8jzQcU+0FbpKbe/Tei6e1o="
  "resolved" "https://registry.npmjs.org/socks/-/socks-1.1.10.tgz"
  "version" "1.1.10"
  dependencies:
    "ip" "^1.1.4"
    "smart-buffer" "^1.0.13"

"sort-keys@^1.0.0":
  "integrity" "sha1-RBttTTRnmPG05J6JIK37oOVD+a0="
  "resolved" "https://registry.npmjs.org/sort-keys/-/sort-keys-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-plain-obj" "^1.0.0"

"sort-keys@^2.0.0":
  "integrity" "sha1-ZYU1WEhh7JfXMNbPQYIuH1ZoQSg="
  "resolved" "https://registry.npmjs.org/sort-keys/-/sort-keys-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "is-plain-obj" "^1.0.0"

"source-list-map@^2.0.0":
  "integrity" "sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw=="
  "resolved" "https://registry.npmjs.org/source-list-map/-/source-list-map-2.0.1.tgz"
  "version" "2.0.1"

"source-list-map@~0.1.7":
  "integrity" "sha1-xVCyq1Qn9rPyH1r+rYjE9Vh7IQY="
  "resolved" "https://registry.npmjs.org/source-list-map/-/source-list-map-0.1.8.tgz"
  "version" "0.1.8"

"source-map-resolve@^0.5.0":
  "integrity" "sha512-MjqsvNwyz1s0k81Goz/9vRBe9SZdB09Bdw+/zYyO+3CuPk6fouTaxscHkgtE8jKvf01kVfl8riHzERQ/kefaSA=="
  "resolved" "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.5.2.tgz"
  "version" "0.5.2"
  dependencies:
    "atob" "^2.1.1"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-support@^0.4.15":
  "integrity" "sha512-try0/JqxPLF9nOjvSta7tVondkP5dwgyLDjVoyMDlmjugT2lRZ1OfsrYTkCd2hkDnJTKRbO/Rl3orm8vlsUzbA=="
  "resolved" "https://registry.npmjs.org/source-map-support/-/source-map-support-0.4.18.tgz"
  "version" "0.4.18"
  dependencies:
    "source-map" "^0.5.6"

"source-map-url@^0.4.0":
  "integrity" "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM="
  "resolved" "https://registry.npmjs.org/source-map-url/-/source-map-url-0.4.0.tgz"
  "version" "0.4.0"

"source-map@^0.5.1":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.5.3", "source-map@~0.5.1":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.5.6":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.5.7":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.1", "source-map@~0.6.0", "source-map@~0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@~0.2.0":
  "integrity" "sha1-2rc/vPwrqBm03gO9b26qSBZLP50="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "amdefine" ">=0.0.4"

"source-map@~0.5.3":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"spdx-correct@^3.0.0":
  "integrity" "sha512-lr2EZCctC2BNR7j7WzJ2FpDznxky1sjfxvvYEyzxNyb6lZXHODmEoJeFu4JupYlkfha1KZpJyoqiJ7pgA1qq8Q=="
  "resolved" "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha512-2XQACfElKi9SlVb1CYadKDXvoajPgBVPn/gOQLrTvHdElaVhr7ZEbqJaRnJLVNeaI4cMEAgVCeBMKF6MWRDCRA=="
  "resolved" "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.2.0.tgz"
  "version" "2.2.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha512-Yg6D3XpRD4kkOmTpdgbUiEJFKghJH03fiC1OPll5h/0sO6neh2jqRDVHOQ4o/LMea0tgCkbMgea5ip/e+MkWyg=="
  "resolved" "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha512-uBIcIl3Ih6Phe3XHK1NqboJLdGfwr1UN3k6wSD1dZpmPsIkb8AGNbZYJ1fOBk834+Gxy8rpfDxrS6XLEMZMY2g=="
  "resolved" "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.3.tgz"
  "version" "3.0.3"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw=="
  "resolved" "https://registry.npmjs.org/split-string/-/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="
  "resolved" "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"sshpk@^1.7.0":
  "integrity" "sha512-HXXqVUq7+pcKeLqqZj6mHFUMvXtOJt1uoUx09pFW6011inTMxqI8BA8PM95myrIyyKwdnzjdFjLiE6KBPVtJIg=="
  "resolved" "https://registry.npmjs.org/sshpk/-/sshpk-1.16.1.tgz"
  "version" "1.16.1"
  dependencies:
    "asn1" "~0.2.3"
    "assert-plus" "^1.0.0"
    "bcrypt-pbkdf" "^1.0.0"
    "dashdash" "^1.12.0"
    "ecc-jsbn" "~0.1.1"
    "getpass" "^0.1.1"
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.0.2"
    "tweetnacl" "~0.14.0"

"ssri@^5.2.4":
  "integrity" "sha512-XRSIPqLij52MtgoQavH/x/dU1qVKtWUAAZeOHsR9c2Ddi4XerFy3mc1alf+dLJKl9EUIm/Ht+EowFkTUOA6GAQ=="
  "resolved" "https://registry.npmjs.org/ssri/-/ssri-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "safe-buffer" "^5.1.1"

"stackframe@^1.0.4":
  "integrity" "sha512-to7oADIniaYwS3MhtCa/sQhrxidCCQiF/qp4/m5iN3ipf0Y7Xlri0f6eG29r08aL7JYl8n32AF3Q5GYBZ7K8vw=="
  "resolved" "https://registry.npmjs.org/stackframe/-/stackframe-1.0.4.tgz"
  "version" "1.0.4"

"static-extend@^0.1.1":
  "integrity" "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="
  "resolved" "https://registry.npmjs.org/static-extend/-/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@~1.4.0":
  "integrity" "sha512-zhSCtt8v2NDrRlPQpCNtw/heZLtfUDqxBM1udqikb/Hbk52LK4nQSwr10u77iopCW5LsyHpuXS0GnEc48mLeew=="
  "resolved" "https://registry.npmjs.org/statuses/-/statuses-1.4.0.tgz"
  "version" "1.4.0"

"statuses@~1.3.1":
  "integrity" "sha1-+vUbnrdKrvOzrPStX2Gr8ky3uT4="
  "resolved" "https://registry.npmjs.org/statuses/-/statuses-1.3.1.tgz"
  "version" "1.3.1"

"stream-browserify@^2.0.1":
  "integrity" "sha512-nX6hmklHs/gr2FuxYDltq8fJA1GDlxKQCz8O/IM4atRqBH8OORmBNgfvW5gG10GT/qQ9u0CzIvr2X5Pkt6ntqg=="
  "resolved" "https://registry.npmjs.org/stream-browserify/-/stream-browserify-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "inherits" "~2.0.1"
    "readable-stream" "^2.0.2"

"stream-each@^1.1.0":
  "integrity" "sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw=="
  "resolved" "https://registry.npmjs.org/stream-each/-/stream-each-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "end-of-stream" "^1.1.0"
    "stream-shift" "^1.0.0"

"stream-http@^2.7.2":
  "integrity" "sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw=="
  "resolved" "https://registry.npmjs.org/stream-http/-/stream-http-2.8.3.tgz"
  "version" "2.8.3"
  dependencies:
    "builtin-status-codes" "^3.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.3.6"
    "to-arraybuffer" "^1.0.0"
    "xtend" "^4.0.0"

"stream-shift@^1.0.0":
  "integrity" "sha1-1cdSgl5TZ+eG944Y5EXqIjoVWVI="
  "resolved" "https://registry.npmjs.org/stream-shift/-/stream-shift-1.0.0.tgz"
  "version" "1.0.0"

"streamqueue@0.0.6":
  "integrity" "sha1-ZvX17JTpuK8knkrsLdH3Qb/pTeM="
  "resolved" "https://registry.npmjs.org/streamqueue/-/streamqueue-0.0.6.tgz"
  "version" "0.0.6"
  dependencies:
    "readable-stream" "^1.0.26-2"

"strict-uri-encode@^1.0.0":
  "integrity" "sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM="
  "resolved" "https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-1.1.0.tgz"
  "version" "1.1.0"

"string_decoder@^1.0.0":
  "integrity" "sha512-6YqyX6ZWEYguAxgZzHGL7SsCeGx3V2TtOTqZz1xSTSWnqsbWwbptafNyvf/ACquZUXV3DANr5BDIwNYe1mN42w=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "safe-buffer" "~5.1.0"

"string_decoder@^1.1.1":
  "integrity" "sha512-6YqyX6ZWEYguAxgZzHGL7SsCeGx3V2TtOTqZz1xSTSWnqsbWwbptafNyvf/ACquZUXV3DANr5BDIwNYe1mN42w=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "safe-buffer" "~5.1.0"

"string_decoder@~0.10.x":
  "integrity" "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz"
  "version" "0.10.31"

"string_decoder@~1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-width@^1.0.1", "string-width@^1.0.2", "string-width@^1.0.2 || 2":
  "integrity" "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "code-point-at" "^1.0.0"
    "is-fullwidth-code-point" "^1.0.0"
    "strip-ansi" "^3.0.0"

"string-width@^2.0.0", "string-width@^2.1.1":
  "integrity" "sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^4.0.0"

"stringstream@~0.0.4":
  "integrity" "sha512-87GEBAkegbBcweToUrdzf3eLhWNg06FJTebl4BVJz/JgWy8CvEr9dRtX5qWphiynMSQlxxi+QqN0z5T32SLlhA=="
  "resolved" "https://registry.npmjs.org/stringstream/-/stringstream-0.0.6.tgz"
  "version" "0.0.6"

"strip-ansi@^3.0.0", "strip-ansi@^3.0.1":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^4.0.0":
  "integrity" "sha1-qEeQIusaw2iocTibY1JixQXuNo8="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-regex" "^3.0.0"

"strip-bom@^2.0.0":
  "integrity" "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4="
  "resolved" "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "is-utf8" "^0.2.0"

"strip-bom@^3.0.0":
  "integrity" "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM="
  "resolved" "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"strip-eof@^1.0.0":
  "integrity" "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="
  "resolved" "http://registry.npmjs.org/strip-eof/-/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-indent@^1.0.1":
  "integrity" "sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI="
  "resolved" "https://registry.npmjs.org/strip-indent/-/strip-indent-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "get-stdin" "^4.0.1"

"strip-json-comments@~2.0.1":
  "integrity" "sha1-PFMZQukIwml8DsNEhYwobHygpgo="
  "resolved" "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz"
  "version" "2.0.1"

"supports-color@^2.0.0":
  "integrity" "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz"
  "version" "2.0.0"

"supports-color@^3.1.0":
  "integrity" "sha1-ZawFBLOVQXHYpklGsq48u4pfVPY="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-3.2.3.tgz"
  "version" "3.2.3"
  dependencies:
    "has-flag" "^1.0.0"

"supports-color@^3.2.3":
  "integrity" "sha1-ZawFBLOVQXHYpklGsq48u4pfVPY="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-3.2.3.tgz"
  "version" "3.2.3"
  dependencies:
    "has-flag" "^1.0.0"

"supports-color@^5.3.0", "supports-color@^5.4.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@3.1.2":
  "integrity" "sha1-cqJiiU2dQIuVbKBf83su2KbiotU="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "has-flag" "^1.0.0"

"svgo@^0.7.0":
  "integrity" "sha1-n1dyQTlSE1xv779Ar+ak+qiLS7U="
  "resolved" "https://registry.npmjs.org/svgo/-/svgo-0.7.2.tgz"
  "version" "0.7.2"
  dependencies:
    "coa" "~1.0.1"
    "colors" "~1.1.2"
    "csso" "~2.3.1"
    "js-yaml" "~3.7.0"
    "mkdirp" "~0.5.1"
    "sax" "~1.2.1"
    "whet.extend" "~0.9.9"

"table@^3.7.8":
  "integrity" "sha1-K7xULw/amGGnVdOUf+/Ys/UThV8="
  "resolved" "http://registry.npmjs.org/table/-/table-3.8.3.tgz"
  "version" "3.8.3"
  dependencies:
    "ajv" "^4.7.0"
    "ajv-keywords" "^1.0.0"
    "chalk" "^1.1.1"
    "lodash" "^4.0.0"
    "slice-ansi" "0.0.4"
    "string-width" "^2.0.0"

"tapable@^0.2.7", "tapable@~0.2.5":
  "integrity" "sha512-2wsvQ+4GwBvLPLWsNfLCDYGsW6xb7aeC6utq2Qh0PFwgEy7K7dsma9Jsmb2zSQj7GvYAyUGSntLtsv++GmgL1A=="
  "resolved" "https://registry.npmjs.org/tapable/-/tapable-0.2.9.tgz"
  "version" "0.2.9"

"tar@^4":
  "integrity" "sha512-LzHF64s5chPQQS0IYBn9IN5h3i98c12bo4NCO7e0sGM2llXQ3p2FGC5sdENN4cTW48O915Sh+x+EXx7XW96xYQ=="
  "resolved" "https://registry.npmjs.org/tar/-/tar-4.4.8.tgz"
  "version" "4.4.8"
  dependencies:
    "chownr" "^1.1.1"
    "fs-minipass" "^1.2.5"
    "minipass" "^2.3.4"
    "minizlib" "^1.1.1"
    "mkdirp" "^0.5.0"
    "safe-buffer" "^5.1.2"
    "yallist" "^3.0.2"

"test-exclude@^4.2.1":
  "integrity" "sha512-SYbXgY64PT+4GAL2ocI3HwPa4Q4TBKm0cwAVeKOt/Aoc0gSpNRjJX8w0pA1LMKZ3LBmd8pYBqApFNQLII9kavA=="
  "resolved" "https://registry.npmjs.org/test-exclude/-/test-exclude-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "arrify" "^1.0.1"
    "micromatch" "^2.3.11"
    "object-assign" "^4.1.0"
    "read-pkg-up" "^1.0.1"
    "require-main-filename" "^1.0.1"

"text-encoding@0.6.4":
  "integrity" "sha1-45mpgiV6J22uQou5KEXLcb3CbRk="
  "resolved" "http://registry.npmjs.org/text-encoding/-/text-encoding-0.6.4.tgz"
  "version" "0.6.4"

"text-table@^0.2.0", "text-table@~0.2.0":
  "integrity" "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="
  "resolved" "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  "version" "0.2.0"

"throttle-debounce@^1.0.1":
  "integrity" "sha512-XH8UiPCQcWNuk2LYePibW/4qL97+ZQ1AN3FNXwZRBNPPowo/NRU5fAlDCSNBJIYCKbioZfuYtMhG4quqoJhVzg=="
  "resolved" "https://registry.npmmirror.com/throttle-debounce/-/throttle-debounce-1.1.0.tgz"
  "version" "1.1.0"

"throttleit@^1.0.0":
  "integrity" "sha512-vDZpf9Chs9mAdfY046mcPt8fg5QSZr37hEH4TXYBnDF+izxgrbRGUAAaBvIk/fJm9aOFCGFd1EsNg5AZCbnQCQ=="
  "resolved" "https://registry.npmmirror.com/throttleit/-/throttleit-1.0.1.tgz"
  "version" "1.0.1"

"through@^2.3.6":
  "integrity" "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="
  "resolved" "http://registry.npmjs.org/through/-/through-2.3.8.tgz"
  "version" "2.3.8"

"through2@^2.0.0":
  "integrity" "sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ=="
  "resolved" "https://registry.npmjs.org/through2/-/through2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"thunkify@~2.1.1":
  "integrity" "sha1-+qDp0jDFGsyVyhOjYawFyn4EVT0="
  "resolved" "https://registry.npmjs.org/thunkify/-/thunkify-2.1.2.tgz"
  "version" "2.1.2"

"time-stamp@^2.0.0":
  "integrity" "sha512-zxke8goJQpBeEgD82CXABeMh0LSJcj7CXEd0OHOg45HgcofF7pxNwZm9+RknpxpDhwN4gFpySkApKfFYfRQnUA=="
  "resolved" "https://registry.npmjs.org/time-stamp/-/time-stamp-2.2.0.tgz"
  "version" "2.2.0"

"timed-out@^4.0.1":
  "integrity" "sha1-8y6srFoXW+ol1/q1Zas+2HQe9W8="
  "resolved" "https://registry.npmjs.org/timed-out/-/timed-out-4.0.1.tgz"
  "version" "4.0.1"

"timers-browserify@^2.0.4":
  "integrity" "sha512-YvC1SV1XdOUaL6gx5CoGroT3Gu49pK9+TZ38ErPldOWW4j49GI1HKs9DV+KGq/w6y+LZ72W1c8cKz2vzY+qpzg=="
  "resolved" "https://registry.npmjs.org/timers-browserify/-/timers-browserify-2.0.10.tgz"
  "version" "2.0.10"
  dependencies:
    "setimmediate" "^1.0.4"

"tmp@0.0.31", "tmp@0.0.x":
  "integrity" "sha1-jzirlDjhcxXl29izZX6L+yd65Kc="
  "resolved" "https://registry.npmjs.org/tmp/-/tmp-0.0.31.tgz"
  "version" "0.0.31"
  dependencies:
    "os-tmpdir" "~1.0.1"

"to-array@0.1.4":
  "integrity" "sha1-F+bBH3PdTz10zaek/zI46a2b+JA="
  "resolved" "https://registry.npmjs.org/to-array/-/to-array-0.1.4.tgz"
  "version" "0.1.4"

"to-arraybuffer@^1.0.0":
  "integrity" "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M="
  "resolved" "https://registry.npmjs.org/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz"
  "version" "1.0.1"

"to-fast-properties@^1.0.3":
  "integrity" "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc="
  "resolved" "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-1.0.3.tgz"
  "version" "1.0.3"

"to-object-path@^0.3.0":
  "integrity" "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="
  "resolved" "https://registry.npmjs.org/to-object-path/-/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex@^3.0.1", "to-regex@^3.0.2":
  "integrity" "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw=="
  "resolved" "https://registry.npmjs.org/to-regex/-/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"toposort@^1.0.0":
  "integrity" "sha1-LmhELZ9k7HILjMieZEOsbKqVACk="
  "resolved" "https://registry.npmjs.org/toposort/-/toposort-1.0.7.tgz"
  "version" "1.0.7"

"tough-cookie@~2.3.0":
  "integrity" "sha512-TZ6TTfI5NtZnuyy/Kecv+CnoROnyXn2DN97LontgQpCwsX2XyLYCC0ENhYkehSOwAp8rTQKc/NUIF7BkQ5rKLA=="
  "resolved" "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.4.tgz"
  "version" "2.3.4"
  dependencies:
    "punycode" "^1.4.1"

"tough-cookie@~2.5.0":
  "integrity" "sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g=="
  "resolved" "https://registry.npmmirror.com/tough-cookie/-/tough-cookie-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "psl" "^1.1.28"
    "punycode" "^2.1.1"

"trim-newlines@^1.0.0":
  "integrity" "sha1-WIeWa7WCpFA6QetST301ARgVphM="
  "resolved" "https://registry.npmjs.org/trim-newlines/-/trim-newlines-1.0.0.tgz"
  "version" "1.0.0"

"trim-right@^1.0.1":
  "integrity" "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM="
  "resolved" "https://registry.npmjs.org/trim-right/-/trim-right-1.0.1.tgz"
  "version" "1.0.1"

"tryer@^1.0.0":
  "integrity" "sha512-c3zayb8/kWWpycWYg87P71E1S1ZL6b6IJxfb5fvsUgsf0S2MVGaDhDXXjDMpdCpfWXqptc+4mXwmiy1ypXqRAA=="
  "resolved" "https://registry.npmjs.org/tryer/-/tryer-1.0.1.tgz"
  "version" "1.0.1"

"tty-browserify@0.0.0":
  "integrity" "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY="
  "resolved" "https://registry.npmjs.org/tty-browserify/-/tty-browserify-0.0.0.tgz"
  "version" "0.0.0"

"tunnel-agent@^0.6.0":
  "integrity" "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0="
  "resolved" "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "safe-buffer" "^5.0.1"

"tweetnacl@^0.14.3", "tweetnacl@~0.14.0":
  "integrity" "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q="
  "resolved" "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz"
  "version" "0.14.5"

"type-check@~0.3.2":
  "integrity" "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I="
  "resolved" "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "prelude-ls" "~1.1.2"

"type-detect@^1.0.0":
  "integrity" "sha1-diIXzAbbJY7EiQihKY6LlRIejqI="
  "resolved" "https://registry.npmjs.org/type-detect/-/type-detect-1.0.0.tgz"
  "version" "1.0.0"

"type-detect@^4.0.0":
  "integrity" "sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g=="
  "resolved" "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz"
  "version" "4.0.8"

"type-detect@0.1.1":
  "integrity" "sha1-C6XsKohWQORw6k6FBZcZANrFiCI="
  "resolved" "https://registry.npmjs.org/type-detect/-/type-detect-0.1.1.tgz"
  "version" "0.1.1"

"type-is@~1.6.16":
  "integrity" "sha512-HRkVv/5qY2G6I8iab9cI7v1bOIdhm94dVjQCPFElW9W+3GeDOSHmy2EBYe4VTApuzolPcmgFTN3ftVJRKR2J9Q=="
  "resolved" "https://registry.npmjs.org/type-is/-/type-is-1.6.16.tgz"
  "version" "1.6.16"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.18"

"typedarray@^0.0.6":
  "integrity" "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="
  "resolved" "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz"
  "version" "0.0.6"

"uglify-js@^2.8.27":
  "integrity" "sha1-KcVzMUgFe7Th913zW3qcty5qWd0="
  "resolved" "https://registry.npmjs.org/uglify-js/-/uglify-js-2.8.29.tgz"
  "version" "2.8.29"
  dependencies:
    "source-map" "~0.5.1"
    "yargs" "~3.10.0"
  optionalDependencies:
    "uglify-to-browserify" "~1.0.0"

"uglify-js@^3.1.4", "uglify-js@3.4.x":
  "integrity" "sha512-8CJsbKOtEbnJsTyv6LE6m6ZKniqMiFWmm9sRbopbkGs3gMPPfd3Fh8iIA4Ykv5MgaTbqHr4BaoGLJLZNhsrW1Q=="
  "resolved" "https://registry.npmjs.org/uglify-js/-/uglify-js-3.4.9.tgz"
  "version" "3.4.9"
  dependencies:
    "commander" "~2.17.1"
    "source-map" "~0.6.1"

"uglify-to-browserify@~1.0.0":
  "integrity" "sha1-bgkk1r2mta/jSeOabWMoUKD4grc="
  "resolved" "https://registry.npmjs.org/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz"
  "version" "1.0.2"

"ultron@1.0.x":
  "integrity" "sha1-rOEWq1V80Zc4ak6I9GhTeMiy5Po="
  "resolved" "https://registry.npmjs.org/ultron/-/ultron-1.0.2.tgz"
  "version" "1.0.2"

"underscore@^1.8.3":
  "integrity" "sha512-5/4etnCkd9c8gwgowi5/om/mYO5ajCaOgdzj/oW+0eQV9WxKBDZw5+ycmKmeaTXjInS/W0BzpGLo2xR2aBwZdg=="
  "resolved" "https://registry.npmjs.org/underscore/-/underscore-1.9.1.tgz"
  "version" "1.9.1"

"union-value@^1.0.0":
  "integrity" "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc="
  "resolved" "https://registry.npm.taobao.org/union-value/download/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"uniq@^1.0.1":
  "integrity" "sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8="
  "resolved" "https://registry.npmjs.org/uniq/-/uniq-1.0.1.tgz"
  "version" "1.0.1"

"uniqs@^2.0.0":
  "integrity" "sha1-/+3ks2slKQaW5uFl1KWe25mOawI="
  "resolved" "https://registry.npmjs.org/uniqs/-/uniqs-2.0.0.tgz"
  "version" "2.0.0"

"unique-filename@^1.1.0":
  "integrity" "sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ=="
  "resolved" "https://registry.npmjs.org/unique-filename/-/unique-filename-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "unique-slug" "^2.0.0"

"unique-slug@^2.0.0":
  "integrity" "sha512-n9cU6+gITaVu7VGj1Z8feKMmfAjEAQGhwD9fE3zvpRRa0wEIx8ODYkVGfSc94M2OX00tUFV8wH3zYbm1I8mxFg=="
  "resolved" "https://registry.npmjs.org/unique-slug/-/unique-slug-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "imurmurhash" "^0.1.4"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
  "resolved" "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unset-value@^1.0.0":
  "integrity" "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="
  "resolved" "https://registry.npmjs.org/unset-value/-/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"upath@^1.1.0":
  "integrity" "sha512-bzpH/oBhoS/QI/YtbkqCg6VEiPYjSZtrHQM6/QnJS6OL9pKUFLqb3aFh4Scvwm45+7iAgiMkLhSbaZxUqmrprw=="
  "resolved" "https://registry.npmjs.org/upath/-/upath-1.1.0.tgz"
  "version" "1.1.0"

"upper-case@^1.1.1":
  "integrity" "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg="
  "resolved" "https://registry.npmjs.org/upper-case/-/upper-case-1.1.3.tgz"
  "version" "1.1.3"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "integrity" "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="
  "resolved" "https://registry.npmjs.org/urix/-/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-loader@^0.5.8":
  "integrity" "sha512-B7QYFyvv+fOBqBVeefsxv6koWWtjmHaMFT6KZWti4KRw8YUD/hOU+3AECvXuzyVawIBx3z7zQRejXCDSO5kk1Q=="
  "resolved" "https://registry.npmjs.org/url-loader/-/url-loader-0.5.9.tgz"
  "version" "0.5.9"
  dependencies:
    "loader-utils" "^1.0.2"
    "mime" "1.3.x"

"url-parse-lax@^3.0.0":
  "integrity" "sha1-FrXK/Afb42dsGxmZF3gj1lA6yww="
  "resolved" "https://registry.npmjs.org/url-parse-lax/-/url-parse-lax-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "prepend-http" "^2.0.0"

"url-to-options@^1.0.1":
  "integrity" "sha1-FQWgOiiaSMvXpDTvuu7FBV9WM6k="
  "resolved" "https://registry.npmjs.org/url-to-options/-/url-to-options-1.0.1.tgz"
  "version" "1.0.1"

"url@^0.11.0":
  "integrity" "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE="
  "resolved" "https://registry.npmjs.org/url/-/url-0.11.0.tgz"
  "version" "0.11.0"
  dependencies:
    "punycode" "1.3.2"
    "querystring" "0.2.0"

"use@^3.1.0":
  "integrity" "sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ=="
  "resolved" "https://registry.npmjs.org/use/-/use-3.1.1.tgz"
  "version" "3.1.1"

"user-home@^2.0.0":
  "integrity" "sha1-nHC/2Babwdy/SGBODwS4tJzenp8="
  "resolved" "https://registry.npmjs.org/user-home/-/user-home-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "os-homedir" "^1.0.0"

"useragent@^2.1.12":
  "integrity" "sha512-4AoH4pxuSvHCjqLO04sU6U/uE65BYza8l/KKBS0b0hnUPWi+cQ2BpeTEwejCSx9SPV5/U03nniDTrWx5NrmKdw=="
  "resolved" "https://registry.npmjs.org/useragent/-/useragent-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "lru-cache" "4.1.x"
    "tmp" "0.0.x"

"util-deprecate@^1.0.1", "util-deprecate@~1.0.1":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util@^0.11.0":
  "integrity" "sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ=="
  "resolved" "https://registry.npmjs.org/util/-/util-0.11.1.tgz"
  "version" "0.11.1"
  dependencies:
    "inherits" "2.0.3"

"util@0.10.3":
  "integrity" "sha1-evsa/lCAUkZInj23/g7TeTNqwPk="
  "resolved" "https://registry.npmjs.org/util/-/util-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "inherits" "2.0.1"

"utila@^0.4.0", "utila@~0.4":
  "integrity" "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="
  "resolved" "https://registry.npmjs.org/utila/-/utila-0.4.0.tgz"
  "version" "0.4.0"

"utils-merge@1.0.1":
  "integrity" "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="
  "resolved" "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@^3.0.0", "uuid@^3.3.2":
  "integrity" "sha512-yXJmeNaw3DnnKAOKJE51sL/ZaYfWJRl1pK9dr19YFCu0ObS231AB1/LbqTKRAQ5kw8A90rA6fr4riOUpTZvQZA=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-3.3.2.tgz"
  "version" "3.3.2"

"v-select2-component@^0.4.7":
  "integrity" "sha512-z/juA8qlRj8u66ktJ0CupUgtNbe63YT+EzqNjq3HwJkTbAE87d2By+L0rSqfLGKtBBAQ4T56Sk7QOMSvRw9OsQ=="
  "resolved" "https://registry.npmmirror.com/v-select2-component/-/v-select2-component-0.4.7.tgz"
  "version" "0.4.7"
  dependencies:
    "jquery" "^3.3.1"
    "select2" "^4.0.7-rc.0"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew=="
  "resolved" "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"vary@~1.1.2":
  "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
  "resolved" "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  "version" "1.1.2"

"vconsole-webpack-plugin@^1.8.0":
  "integrity" "sha512-rqRPWZ+reNLxysT+xJSpwp+HIWB/WgMRq7xe/hjEb6lY/LY1fmqfGvejnZPHhIoF53zjl8iQLFk7/zQuGi1XVA=="
  "resolved" "https://registry.npmmirror.com/vconsole-webpack-plugin/-/vconsole-webpack-plugin-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "resize-observer-polyfill" "^1.5.1"
    "vconsole" "^3.15"

"vconsole@^3.15", "vconsole@^3.15.1":
  "integrity" "sha512-KH8XLdrq9T5YHJO/ixrjivHfmF2PC2CdVoK6RWZB4yftMykYIaXY1mxZYAic70vADM54kpMQF+dYmvl5NRNy1g=="
  "resolved" "https://registry.npmmirror.com/vconsole/-/vconsole-3.15.1.tgz"
  "version" "3.15.1"
  dependencies:
    "@babel/runtime" "^7.17.2"
    "copy-text-to-clipboard" "^3.0.1"
    "core-js" "^3.11.0"
    "mutation-observer" "^1.0.3"

"vendors@^1.0.0":
  "integrity" "sha512-w/hry/368nO21AN9QljsaIhb9ZiZtZARoVH5f3CsFbawdLdayCgKRPup7CggujvySMxx0I91NOyxdVENohprLQ=="
  "resolved" "https://registry.npmjs.org/vendors/-/vendors-1.0.2.tgz"
  "version" "1.0.2"

"verror@1.10.0":
  "integrity" "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA="
  "resolved" "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "core-util-is" "1.0.2"
    "extsprintf" "^1.2.0"

"vm-browserify@0.0.4":
  "integrity" "sha1-XX6kW7755Kb/ZflUOOCofDV9WnM="
  "resolved" "https://registry.npmjs.org/vm-browserify/-/vm-browserify-0.0.4.tgz"
  "version" "0.0.4"
  dependencies:
    "indexof" "0.0.1"

"void-elements@^2.0.0":
  "integrity" "sha1-wGavtYK7HLQSjWDqkjkulNXp2+w="
  "resolved" "https://registry.npmjs.org/void-elements/-/void-elements-2.0.1.tgz"
  "version" "2.0.1"

"vue-flatpickr-component@^8.1.7":
  "integrity" "sha512-T9aapLERf5XrisKUHw8QVByFpN9UB583Bhu6+HtzvhCcfXqIYBtRc3rQC0ZhFSRk3CNMo7533U+B5Qs4WAbhyA=="
  "resolved" "https://registry.npmjs.org/vue-flatpickr-component/-/vue-flatpickr-component-8.1.7.tgz"
  "version" "8.1.7"
  dependencies:
    "flatpickr" "^4.6.6"

"vue-highcharts@0.0.10":
  "integrity" "sha1-pLkAmO3nemXMqH5ivth1SeHhyYU="
  "resolved" "https://registry.npmjs.org/vue-highcharts/-/vue-highcharts-0.0.10.tgz"
  "version" "0.0.10"

"vue-hot-reload-api@^2.2.0":
  "integrity" "sha512-NpznMQoe/DzMG7nJjPkJKT7FdEn9xXfnntG7POfTmqnSaza97ylaBf1luZDh4IgV+vgUoR//id5pf8Ru+Ym+0g=="
  "resolved" "https://registry.npmjs.org/vue-hot-reload-api/-/vue-hot-reload-api-2.3.2.tgz"
  "version" "2.3.2"

"vue-loader@^13.0.4":
  "integrity" "sha512-ACCwbfeC6HjY2pnDii+Zer+MZ6sdOtwvLmDXRK/BoD3WNR551V22R6KEagwHoTRJ0ZlIhpCBkptpCU6+Ri/05w=="
  "resolved" "https://registry.npmjs.org/vue-loader/-/vue-loader-13.7.3.tgz"
  "version" "13.7.3"
  dependencies:
    "consolidate" "^0.14.0"
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.1.0"
    "lru-cache" "^4.1.1"
    "postcss" "^6.0.8"
    "postcss-load-config" "^1.1.0"
    "postcss-selector-parser" "^2.0.0"
    "prettier" "^1.7.0"
    "resolve" "^1.4.0"
    "source-map" "^0.6.1"
    "vue-hot-reload-api" "^2.2.0"
    "vue-style-loader" "^3.0.0"
    "vue-template-es2015-compiler" "^1.6.0"

"vue-resource@^1.3.4":
  "integrity" "sha512-o6V4wNgeqP+9v9b2bPXrr20CGNQPEXjpbUWdZWq9GJhqVeAGcYoeTtn/D4q059ZiyN0DIrDv/ADrQUmlUQcsmg=="
  "resolved" "https://registry.npmjs.org/vue-resource/-/vue-resource-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "got" "^8.0.3"

"vue-router@^2.7.0":
  "integrity" "sha512-MC4jacHBhTPKtmcfzvaj2N7g6jgJ/Z/eIjZdt+yUaUOM1iKC0OUIlO/xCtz6OZFFTNUJs/1YNro2GN/lE+nOXA=="
  "resolved" "https://registry.npmjs.org/vue-router/-/vue-router-2.8.1.tgz"
  "version" "2.8.1"

"vue-select@^3.20.4":
  "integrity" "sha512-pXIsDUnBR1075qHNEM7mKgX7YnHI3MfCO+7VmXSA1Hywplpqi52jOa0j6EHWQU6MnaW/mBZPuaQtgp3yvks2Kw=="
  "resolved" "https://registry.npmmirror.com/vue-select/-/vue-select-3.20.4.tgz"
  "version" "3.20.4"

"vue-style-loader@^3.0.0", "vue-style-loader@^3.0.1":
  "integrity" "sha512-ICtVdK/p+qXWpdSs2alWtsXt9YnDoYjQe0w5616j9+/EhjoxZkbun34uWgsMFnC1MhrMMwaWiImz3K2jK1Yp2Q=="
  "resolved" "https://registry.npmjs.org/vue-style-loader/-/vue-style-loader-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.0.2"

"vue-template-compiler@^2.0.0", "vue-template-compiler@^2.4.2":
  "integrity" "sha512-OakxDGyrmMQViCjkakQFbDZlG0NibiOzpLauOfyCUVRQc9yPmTqpiz9nF0VeA+dFkXegetw0E5x65BFhhLXO0A=="
  "resolved" "https://registry.npmjs.org/vue-template-compiler/-/vue-template-compiler-2.6.6.tgz"
  "version" "2.6.6"
  dependencies:
    "de-indent" "^1.0.2"
    "he" "^1.1.0"

"vue-template-es2015-compiler@^1.6.0":
  "integrity" "sha512-cliV19VHLJqFUYbz/XeWXe5CO6guzwd0yrrqqp0bmjlMP3ZZULY7fu8RTC4+3lmHwo6ESVDHFDsvjB15hcR5IA=="
  "resolved" "https://registry.npmjs.org/vue-template-es2015-compiler/-/vue-template-es2015-compiler-1.8.2.tgz"
  "version" "1.8.2"

"vue-wechat-title@^2.0.4":
  "integrity" "sha512-bCfKnJpbquqNY5ygNN7nRgNHGhyinWd277ecVh0Z15Tk8nS69catrvQSJIuRxXUOxoO/4Y32c6IfNbQUA8oh0g=="
  "resolved" "https://registry.npmjs.org/vue-wechat-title/-/vue-wechat-title-2.0.5.tgz"
  "version" "2.0.5"

"vue@^2.0.0", "vue@^2.2.0", "vue@^2.5.17", "vue@>=1.0.0", "vue@2.x":
  "integrity" "sha512-Y2DdOZD8sxApS+iUlwv1v8U1qN41kq6Kw45lM6nVZKhygeWA49q7VCCXkjXqeDBXgurrKWkYQ9cJeEJwAq0b9Q=="
  "resolved" "https://registry.npmjs.org/vue/-/vue-2.6.6.tgz"
  "version" "2.6.6"

"vuex@^2.3.1":
  "integrity" "sha512-5oJPOJySBgSgSzoeO+gZB/BbN/XsapgIF6tz34UwJqnGZMQurzIO3B4KIBf862gfc9ya+oduY5sSkq+5/oOilQ=="
  "resolved" "https://registry.npmjs.org/vuex/-/vuex-2.5.0.tgz"
  "version" "2.5.0"

"watchpack@^1.3.1":
  "integrity" "sha512-i6dHe3EyLjMmDlU1/bGQpEw25XSjkJULPuAVKCbNRefQVq48yXKUpwg538F7AZTf9kyr57zj++pQFltUa5H7yA=="
  "resolved" "https://registry.npmjs.org/watchpack/-/watchpack-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "chokidar" "^2.0.2"
    "graceful-fs" "^4.1.2"
    "neo-async" "^2.5.0"

"webpack-bundle-analyzer@^2.2.1":
  "integrity" "sha512-rwxyfecTAxoarCC9VlHlIpfQCmmJ/qWD5bpbjkof+7HrNhTNZIwZITxN6CdlYL2axGmwNUQ+tFgcSOiNXMf/sQ=="
  "resolved" "https://registry.npmjs.org/webpack-bundle-analyzer/-/webpack-bundle-analyzer-2.13.1.tgz"
  "version" "2.13.1"
  dependencies:
    "acorn" "^5.3.0"
    "bfj-node4" "^5.2.0"
    "chalk" "^2.3.0"
    "commander" "^2.13.0"
    "ejs" "^2.5.7"
    "express" "^4.16.2"
    "filesize" "^3.5.11"
    "gzip-size" "^4.1.0"
    "lodash" "^4.17.4"
    "mkdirp" "^0.5.1"
    "opener" "^1.4.3"
    "ws" "^4.0.0"

"webpack-dev-middleware@^1.10.0", "webpack-dev-middleware@^1.12.0":
  "integrity" "sha512-FCrqPy1yy/sN6U/SaEZcHKRXGlqU0DUaEBL45jkUYoB8foVb6wCnbIJ1HKIx+qUFTW+3JpVcCJCxZ8VATL4e+A=="
  "resolved" "https://registry.npmjs.org/webpack-dev-middleware/-/webpack-dev-middleware-1.12.2.tgz"
  "version" "1.12.2"
  dependencies:
    "memory-fs" "~0.4.1"
    "mime" "^1.5.0"
    "path-is-absolute" "^1.0.0"
    "range-parser" "^1.0.3"
    "time-stamp" "^2.0.0"

"webpack-hot-middleware@^2.18.0":
  "integrity" "sha512-pPlmcdoR2Fn6UhYjAhp1g/IJy1Yc9hD+T6O9mjRcWV2pFbBjIFoJXhP0CoD0xPOhWJuWXuZXGBga9ybbOdzXpg=="
  "resolved" "https://registry.npmjs.org/webpack-hot-middleware/-/webpack-hot-middleware-2.24.3.tgz"
  "version" "2.24.3"
  dependencies:
    "ansi-html" "0.0.7"
    "html-entities" "^1.2.0"
    "querystring" "^0.2.0"
    "strip-ansi" "^3.0.0"

"webpack-merge@^4.1.0":
  "integrity" "sha512-4p8WQyS98bUJcCvFMbdGZyZmsKuWjWVnVHnAS3FFg0HDaRVrPbkivx2RYCre8UiemD67RsiFFLfn4JhLAin8Vw=="
  "resolved" "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "lodash" "^4.17.5"

"webpack-sources@^0.1.0":
  "integrity" "sha1-qh86vw8NdNtxEcQOUAuE+WZkB1A="
  "resolved" "https://registry.npmjs.org/webpack-sources/-/webpack-sources-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "source-list-map" "~0.1.7"
    "source-map" "~0.5.3"

"webpack-sources@^1.0.1":
  "integrity" "sha512-OiVgSrbGu7NEnEvQJJgdSFPl2qWKkWq5lHMhgiToIiN9w34EBnjYzSYs+VbL5KoYiLNtFFa7BZIKxRED3I32pA=="
  "resolved" "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "source-list-map" "^2.0.0"
    "source-map" "~0.6.1"

"webpack@^1 || ^2 || ^3", "webpack@^1.0.0 || ^2.0.0 || ^3.0.0", "webpack@^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^2.2.0", "webpack@^2.6.1", "webpack@1 || ^2 || ^2.1.0-beta || ^2.2.0-rc || ^3", "webpack@2 || 3 || 4":
  "integrity" "sha512-MjAA0ZqO1ba7ZQJRnoCdbM56mmFpipOPUv/vQpwwfSI42p5PVDdoiuK2AL2FwFUVgT859Jr43bFZXRg/LNsqvg=="
  "resolved" "https://registry.npmjs.org/webpack/-/webpack-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "acorn" "^5.0.0"
    "acorn-dynamic-import" "^2.0.0"
    "ajv" "^4.7.0"
    "ajv-keywords" "^1.1.1"
    "async" "^2.1.2"
    "enhanced-resolve" "^3.3.0"
    "interpret" "^1.0.0"
    "json-loader" "^0.5.4"
    "json5" "^0.5.1"
    "loader-runner" "^2.3.0"
    "loader-utils" "^0.2.16"
    "memory-fs" "~0.4.1"
    "mkdirp" "~0.5.0"
    "node-libs-browser" "^2.0.0"
    "source-map" "^0.5.3"
    "supports-color" "^3.1.0"
    "tapable" "~0.2.5"
    "uglify-js" "^2.8.27"
    "watchpack" "^1.3.1"
    "webpack-sources" "^1.0.1"
    "yargs" "^6.0.0"

"weixin-js-sdk@^1.4.0-test":
  "integrity" "sha512-Gph1WAWB2YN/lMOFB/ymb+hbU/wYazzJgu6PMMktCy9cSCeW5wA6Zwt0dpahJbJ+RJEwtTv2x9iIu0U4enuVSQ=="
  "resolved" "https://registry.npmmirror.com/weixin-js-sdk/-/weixin-js-sdk-1.6.5.tgz"
  "version" "1.6.5"

"whet.extend@~0.9.9":
  "integrity" "sha1-+HfVv2SMl+WqVC+twW1qJZucEaE="
  "resolved" "https://registry.npmjs.org/whet.extend/-/whet.extend-0.9.9.tgz"
  "version" "0.9.9"

"which-module@^1.0.0":
  "integrity" "sha1-u6Y8qGGUiZT/MHc2CJ47lgJsKk8="
  "resolved" "https://registry.npmjs.org/which-module/-/which-module-1.0.0.tgz"
  "version" "1.0.0"

"which-module@^2.0.0":
  "integrity" "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="
  "resolved" "https://registry.npmjs.org/which-module/-/which-module-2.0.0.tgz"
  "version" "2.0.0"

"which@^1.1.1", "which@^1.2.10", "which@^1.2.9":
  "integrity" "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ=="
  "resolved" "https://registry.npmjs.org/which/-/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"wide-align@^1.1.0":
  "integrity" "sha512-QGkOQc8XL6Bt5PwnsExKBPuMKBxnGxWWW3fU55Xt4feHozMUhdUMaBCk290qpm/wG5u/RSKzwdAC4i51YigihA=="
  "resolved" "https://registry.npmjs.org/wide-align/-/wide-align-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "string-width" "^1.0.2 || 2"

"window-or-global@^1.0.1":
  "integrity" "sha1-2+RboqKRqrxW1iz2bEW3+jIpRt4="
  "resolved" "https://registry.npmjs.org/window-or-global/-/window-or-global-1.0.1.tgz"
  "version" "1.0.1"

"window-size@0.1.0":
  "integrity" "sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0="
  "resolved" "https://registry.npmjs.org/window-size/-/window-size-0.1.0.tgz"
  "version" "0.1.0"

"wordwrap@^1.0.0", "wordwrap@~1.0.0":
  "integrity" "sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus="
  "resolved" "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz"
  "version" "1.0.0"

"wordwrap@~0.0.2":
  "integrity" "sha1-o9XabNXAvAAI03I0u68b7WMFkQc="
  "resolved" "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.3.tgz"
  "version" "0.0.3"

"wordwrap@0.0.2":
  "integrity" "sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8="
  "resolved" "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz"
  "version" "0.0.2"

"wrap-ansi@^2.0.0":
  "integrity" "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU="
  "resolved" "http://registry.npmjs.org/wrap-ansi/-/wrap-ansi-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "string-width" "^1.0.1"
    "strip-ansi" "^3.0.1"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write@^0.2.1":
  "integrity" "sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c="
  "resolved" "https://registry.npmjs.org/write/-/write-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "mkdirp" "^0.5.1"

"ws@^4.0.0":
  "integrity" "sha512-ZGh/8kF9rrRNffkLFV4AzhvooEclrOH0xaugmqGsIfFgOE/pIz4fMc4Ef+5HSQqTEug2S9JZIWDR47duDSLfaA=="
  "resolved" "http://registry.npmjs.org/ws/-/ws-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "async-limiter" "~1.0.0"
    "safe-buffer" "~5.1.0"

"ws@1.1.2":
  "integrity" "sha1-iiRPoFJAHgjJiGz0SoUYnh/UBn8="
  "resolved" "https://registry.npmjs.org/ws/-/ws-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "options" ">=0.0.5"
    "ultron" "1.0.x"

"wtf-8@1.0.0":
  "integrity" "sha1-OS2LotDxw00e4tYw8V0O+2jhBIo="
  "resolved" "https://registry.npmjs.org/wtf-8/-/wtf-8-1.0.0.tgz"
  "version" "1.0.0"

"xmlhttprequest-ssl@1.5.3":
  "integrity" "sha1-GFqIjATspGw+QHDZn3tJ3jUomS0="
  "resolved" "https://registry.npmjs.org/xmlhttprequest-ssl/-/xmlhttprequest-ssl-1.5.3.tgz"
  "version" "1.5.3"

"xregexp@2.0.0":
  "integrity" "sha1-UqY+VsoLhKfzpfPWGHLxJq16WUM="
  "resolved" "https://registry.npmjs.org/xregexp/-/xregexp-2.0.0.tgz"
  "version" "2.0.0"

"xtend@^4.0.0", "xtend@~4.0.1":
  "integrity" "sha1-pcbVMr5lbiPbgg77lDofBJmNY68="
  "resolved" "https://registry.npmjs.org/xtend/-/xtend-4.0.1.tgz"
  "version" "4.0.1"

"y18n@^3.2.1 || ^4.0.0", "y18n@^4.0.0":
  "integrity" "sha512-r9S/ZyXu/Xu9q1tYlpsLIsa3EeLXXk0VwlxqTcFRfg9EhMW+17kbt9G0NrgCmhGb5vT2hyhJZLfDGx+7+5Uj/w=="
  "resolved" "https://registry.npmjs.org/y18n/-/y18n-4.0.0.tgz"
  "version" "4.0.0"

"y18n@^3.2.1":
  "integrity" "sha1-bRX7qITAhnnA136I53WegR4H+kE="
  "resolved" "https://registry.npmjs.org/y18n/-/y18n-3.2.1.tgz"
  "version" "3.2.1"

"yallist@^2.1.2":
  "integrity" "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz"
  "version" "2.1.2"

"yallist@^3.0.0", "yallist@^3.0.2":
  "integrity" "sha512-S+Zk8DEWE6oKpV+vI3qWkaK+jSbIK86pCwe2IF/xwIpQ8jEuxpw9NyaGjmp9+BoJv5FV2piqCDcoCtStppiq2A=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-3.0.3.tgz"
  "version" "3.0.3"

"yargs-parser@^11.1.1":
  "integrity" "sha512-C6kB/WJDiaxONLJQnF8ccx9SEeoTTLek8RVbaOIsrAUS8VrBEXfmeSnCZxygc+XC2sNMBIwOOnfcxiynjHsVSQ=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-11.1.1.tgz"
  "version" "11.1.1"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^4.2.0":
  "integrity" "sha1-KczqwNxPA8bIe0qfIX3RjJ90hxw="
  "resolved" "http://registry.npmjs.org/yargs-parser/-/yargs-parser-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "camelcase" "^3.0.0"

"yargs@^12.0.5":
  "integrity" "sha512-Lhz8TLaYnxq/2ObqHDql8dX8CJi97oHxrjUcYtzKbbykPtVW9WB+poxI+NM2UIzsMgNCZTIf0AQwsjK5yMAqZw=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-12.0.5.tgz"
  "version" "12.0.5"
  dependencies:
    "cliui" "^4.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^3.0.0"
    "get-caller-file" "^1.0.1"
    "os-locale" "^3.0.0"
    "require-directory" "^2.1.1"
    "require-main-filename" "^1.0.1"
    "set-blocking" "^2.0.0"
    "string-width" "^2.0.0"
    "which-module" "^2.0.0"
    "y18n" "^3.2.1 || ^4.0.0"
    "yargs-parser" "^11.1.1"

"yargs@^6.0.0":
  "integrity" "sha1-eC7CHvQDNF+DCoCMo9UTr1YGUgg="
  "resolved" "http://registry.npmjs.org/yargs/-/yargs-6.6.0.tgz"
  "version" "6.6.0"
  dependencies:
    "camelcase" "^3.0.0"
    "cliui" "^3.2.0"
    "decamelize" "^1.1.1"
    "get-caller-file" "^1.0.1"
    "os-locale" "^1.4.0"
    "read-pkg-up" "^1.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^1.0.1"
    "set-blocking" "^2.0.0"
    "string-width" "^1.0.2"
    "which-module" "^1.0.0"
    "y18n" "^3.2.1"
    "yargs-parser" "^4.2.0"

"yargs@~3.10.0":
  "integrity" "sha1-9+572FfdfB0tOMDnTvvWgdFDH9E="
  "resolved" "http://registry.npmjs.org/yargs/-/yargs-3.10.0.tgz"
  "version" "3.10.0"
  dependencies:
    "camelcase" "^1.0.2"
    "cliui" "^2.1.0"
    "decamelize" "^1.0.0"
    "window-size" "0.1.0"

"yauzl@^2.10.0":
  "integrity" "sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g=="
  "resolved" "https://registry.npmmirror.com/yauzl/-/yauzl-2.10.0.tgz"
  "version" "2.10.0"
  dependencies:
    "buffer-crc32" "~0.2.3"
    "fd-slicer" "~1.1.0"

"yeast@0.1.2":
  "integrity" "sha1-AI4G2AlDIMNy28L47XagymyKxBk="
  "resolved" "https://registry.npmjs.org/yeast/-/yeast-0.1.2.tgz"
  "version" "0.1.2"
