import Vue from 'vue';
import Router from 'vue-router';
import * as URL from '_/URLs';
import store from '../vuex';
import {LEAGUE} from "../modules/URLs";
import {CAR_NEXT} from "../modules/URLs";
import CarDetail from '../app/car/CarDetail.vue';

Vue.use(Router);

export default new Router({
	mode: 'history',
	routes: [
		{
			path: URL.CAR_ADD_EMPLOYEE,
			name: 'addemployee',
			meta: {
				requireAuth: true,
				title: '新增员工',
			},
			component(resolve){
				require(['@/app/userdcar/addemployee'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.CAR_ADD_PPERSON,
			name: 'login',
			meta: {
				requireAuth: true,
				title: '登录',
			},
			component(resolve){
				require(['@/app/login/login'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.CAR_ADD_CAR,
			name: 'caradd',
			meta: {
				requireAuth: true,
				title: '新增车辆',
			},
			component(resolve){
				require(['@/app/car/CarAdd'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.CAR_PRE,
			name: 'pre',
			meta: {
				requireAuth: true,
				title: '交易预登记',
			},
			component(resolve){
				require(['@/app/userdcar/pre'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.CAR_NEXT,
			name: 'next',
			meta: {
				requireAuth: true,
				title: '交易车辆信息',
			},
			component(resolve){
				require(['@/app/userdcar/next'], comp => {
					resolve(comp);
				});
			}
		},
		{
			path: URL.CAR_CHECK,
			name: 'check',
			meta: {
				requireAuth: true,
				title: '照片资料检查',
			},
			component(resolve){
				require(['@/app/userdcar/check'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.LOGIN,
			name: 'login',
			meta: {
				requireAuth: false,
				title: '登录',
			},
			component(resolve){
				require(['@/app/login/login'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.INDEX,
			name: 'index',
			meta: {
				requireAuth: true,
				title: '首页',
			},
			component(resolve){
				require(['@/app/home/<USER>'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.DEMO_YDY,
			name: 'ydy',
			meta: {
				requireAuth: false,
			},
			component(resolve){
				require(['@/app/demo/ydy'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.DEMO_SJ,
			name: 'sj',
			meta: {
				requireAuth: false,
			},
			component(resolve){
				require(['@/app/demo/sj'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.CHANGE_GROUP,
			name: 'changegroup',
			meta: {
				requireAuth: true,
				title: '档口切换',
			},
			component(resolve){
				require(['@/app/home/<USER>'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.BILL,
			name: 'bill',
			meta: {
				requireAuth: true,
				title: '账单',
			},
			component(resolve){
				require(['@/app/bill/bill'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.BILL_DETAIL + '/:id',
			name: 'billdetail',
			meta: {
				requireAuth: true,
				title: '账单详情',
			},
			component(resolve){
				require(['@/app/bill/billDetail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PRODUCT,
			name: 'product',
			meta: {
				requireAuth: true,
				title: '商品管理',
			},
			component(resolve){
				require(['@/app/product/product'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.NEW_PRODUCT,
			name: 'newproduct',
			meta: {
				requireAuth: true,
				title: '自建商品',
			},
			component(resolve){
				require(['@/app/product/newProduct'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.EDIT_PRODUCT + '/:id',
			name: 'editproduct',
			meta: {
				requireAuth: true,
				title: '编辑商品',
			},
			component(resolve){
				require(['@/app/product/editProduct'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.EDIT_TRACE + '/:id',
			name: 'edittrace',
			meta: {
				requireAuth: true,
				title: '编辑追溯信息',
			},
			component(resolve){
				require(['@/app/product/trace'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.ADD_PRODUCT,
			name: 'addproduct',
			meta: {
				requireAuth: true,
				title: '从商品库添加商品',
			},
			component(resolve){
				require(['@/app/product/addProduct'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.SETTING,
			name: 'setting',
			meta: {
				requireAuth: true,
				title: '我的账户',
			},
			component(resolve){
				require(['@/app/login/setting'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.REPORT,
			name: 'report',
			meta: {
				requireAuth: true,
				title: '销售情况统计',
			},
			component(resolve){
				require(['@/app/report/index'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.VIP_SALE,
			name: 'vipsale',
			meta: {
				requireAuth: true,
				title: '会员销售统计',
			},
			component(resolve){
				require(['@/app/report/vip/list'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.VIP_REPORT,
			name: 'vipreport',
			meta: {
				requireAuth: true,
				title: '会员销售流水',
			},
			component(resolve){
				require(['@/app/report/vip/detail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.SALE,
			name: 'sale',
			meta: {
				requireAuth: true,
				title: '每日销售情况',
			},
			component(resolve){
				require(['@/app/report/sale/list'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.SALE + '/:id',
			name: 'salechart',
			meta: {
				requireAuth: true,
				title: '每日销售明细',
			},
			component(resolve){
				require(['@/app/report/sale/chart'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.SALE_CHART + '/:id/:type',
			name: 'saledetail',
			meta: {
				requireAuth: true,
				title: '收银明细',
			},
			component(resolve){
				require(['@/app/report/sale/detail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PUBLICITY,
			name: 'publicity',
			meta: {
				requireAuth: true,
				title: '价格公示',
			},
			component(resolve){
				require(['@/app/publicity/index'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PUBLIC_ADD,
			name: 'publicityadd',
			meta: {
				requireAuth: true,
				title: '选择商品',
			},
			component(resolve){
				require(['@/app/publicity/add'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.MONEY_CHANGE,
			name: 'moneychange',
			meta: {
				requireAuth: true,
				title: '资金变动明细',
			},
			component(resolve){
				require(['@/app/money/change'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.MONEY_CHANGE + '/:id',
			name: 'moneychangedetail',
			meta: {
				requireAuth: true,
				title: '账户收银明细',
			},
			component(resolve){
				require(['@/app/money/change'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.CHANGE_LABEL,
			name: 'changelabel',
			meta: {
				requireAuth: true,
				title: '每日资金变动',
			},
			component(resolve){
				require(['@/app/money/labels'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.LABEL_SAOBEI,
			name: 'labelsaobei',
			meta: {
				requireAuth: true,
				title: '收银结算',
				keepAlive: true,
			},
			component(resolve){
				require(['@/app/money/labelSaobei'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.MONEY_DETAIL + '/:id',
			name: 'moneydetail',
			meta: {
				requireAuth: true,
				title: '结算单明细',
			},
			component(resolve){
				require(['@/app/money/computeDetail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.WITHDRAW_INDEX,
			name: 'withdrawindex',
			meta: {
				requireAuth: true,
				title: '余额管理',
			},
			component(resolve){
				require(['@/app/withdraw/main'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.WITHDRAW,
			name: 'withdraw',
			meta: {
				requireAuth: true,
				title: '提现',
			},
			component(resolve){
				require(['@/app/withdraw/withdraw'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.WITHDRAW_RECORD,
			name: 'withdrawrecord',
			meta: {
				requireAuth: true,
				title: '提现记录',
			},
			component(resolve){
				require(['@/app/withdraw/withdrawRecord'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.BINDCARD,
			name: 'bindcard',
			meta: {
				requireAuth: true,
				title: '绑卡',
			},
			component(resolve){
				require(['@/app/withdraw/bind'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.ACCOUNT_RECORD,
			name: 'accountrecord',
			meta: {
				requireAuth: true,
				title: '金额变动记录',
			},
			component(resolve){
				require(['@/app/withdraw/accountRecord'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.INCOME_DETAIL + '/:id',
			name: 'incomedetail',
			meta: {
				requireAuth: true,
				title: '每日收银明细',
			},
			component(resolve){
				require(['@/app/money/incomeDetail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.APPRAISE,
			name: 'appraise',
			meta: {
				requireAuth: false,
				title: '档口评价',
			},
			component(resolve){
				require(['@/app/info/appraise'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.RECEIPT + '/:id',
			name: 'receipt',
			meta: {
				requireAuth: false,
				title: '订单详情',
			},
			component(resolve){
				require(['@/app/info/receipt'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.RECEIPT_DETAIL + '/:id',
			name: 'receiptdetail',
			meta: {
				requireAuth: false,
				title: '商品追溯',
			},
			component(resolve){
				require(['@/app/info/receiptDetail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.POS_TRACE + '/:id' + '/:orgzId',
			name: 'postrace',
			meta: {
				requireAuth: false,
				title: '商品详情',
			},
			component(resolve){
				require(['@/app/info/posTrace'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.SJZ_TRACE + '/:id',
			name: 'sjztrace',
			meta: {
				requireAuth: false,
				title: '商品追溯',
			},
			component(resolve){
				require(['@/app/info/sjzTrace'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.ACTIVITY,
			name: 'activty',
			meta: {
				requireAuth: true,
				title: '邀请充值',
			},
			component(resolve){
				require(['@/app/activity/index'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.DEPOSIT,
			name: 'deposit',
			meta: {
				requireAuth: true,
				title: '预存金',
			},
			component(resolve){
				require(['@/app/deposit/pay'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.DEPOSIT_LIST,
			name: 'depositinfo',
			meta: {
				requireAuth: true,
				title: '流水记录',
			},
			component(resolve){
				require(['@/app/deposit/list'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.TYPE_LIST,
			name: 'typelist',
			meta: {
				requireAuth: true,
				title: '前台分类管理',
			},
			component(resolve){
				require(['@/app/product/typeList'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.MY_EVALUATION,
			name: 'myevaluation',
			meta: {
				requireAuth: true,
				title: '我的评价',
			},
			component(resolve){
				require(['@/app/evaluation/evaluation'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.ORDER_DETAIL + '/:id',
			name: 'orderdetail',
			meta: {
				requireAuth: true,
				title: '订单详情',
			},
			component(resolve){
				require(['@/app/order/detail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PURCHASE,
			name: 'purchase',
			meta: {
				requireAuth: true,
				title: '采购',
			},
			component(resolve){
				require(['@/app/purchase/purchase'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PURCHASE_INSTOCK,
			name: 'instock',
			meta: {
				requireAuth: true,
				title: '采购入库',
			},
			component(resolve){
				require(['@/app/purchase/inStock'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PURCHASE_ADDINSTOCK,
			name: 'addinstock',
			meta: {
				requireAuth: true,
				title: '新建采购入库单',
			},
			component(resolve){
				require(['@/app/purchase/addInStock'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PURCHASE_OUTSTOCK,
			name: 'outstock',
			meta: {
				requireAuth: true,
				title: '采购退货',
			},
			component(resolve){
				require(['@/app/purchase/outStock'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PURCHASE_ADDOUTSTOCK,
			name: 'addoutstock',
			meta: {
				requireAuth: true,
				title: '新建采购退货单',
			},
			component(resolve){
				require(['@/app/purchase/addOutStock'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PURCHASE_CHOOSEPRO,
			name: 'choosepro',
			meta: {
				requireAuth: true,
				title: '选择商品',
			},
			component(resolve){
				require(['@/app/purchase/choosePro'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PURCHASE_SUPPLIER,
			name: 'supplier',
			meta: {
				requireAuth: true,
				title: '供应商管理',
			},
			component(resolve){
				require(['@/app/purchase/supplier/list'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PURCHASE_SUPPLIER + '/:id',
			name: 'supplieredit',
			meta: {
				requireAuth: true,
				title: '编辑供应商'
			},
			component(resolve){
				require(['@/app/purchase/supplier/edit'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.SUPPLIER_TYPE,
			name: 'suppliertype',
			meta: {
				requireAuth: true,
				title: '供应商分类',
			},
			component(resolve){
				require(['@/app/purchase/supplier/type'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PURCHASE_CONFIRM,
			name: 'purchaseconfirm',
			meta: {
				requireAuth: true,
				title: '确认收货',
			},
			component(resolve){
				require(['@/app/purchase/restock/confirm'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PURCHASE_PAY,
			name: 'purchasepay',
			meta: {
				requireAuth: true,
				title: '要货订单收款',
			},
			component(resolve){
				require(['@/app/purchase/restock/pay'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PURCHASE_RESULT,
			name: 'purchaseresult',
			meta: {
				requireAuth: true,
				title: '支付结果',
			},
			component(resolve){
				require(['@/app/purchase/restock/result'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.INSPECT,
			name: 'inspect',
			meta: {
				requireAuth: true,
				title: '新建检疫检证',
			},
			component(resolve){
				require(['@/app/inspect/edit'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.INSPECT + '/:no',
			name: 'inspectno',
			meta: {
				requireAuth: true,
				title: '关联采购订单',
			},
			component(resolve){
				require(['@/app/inspect/sales'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.STOCK,
			name: 'stock',
			meta: {
				requireAuth: true,
				title: '库存管理',
			},
			component(resolve){
				require(['@/app/stock/index'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.STOCK_INQUIRE,
			name: 'stockinquire',
			meta: {
				requireAuth: true,
				title: '库存查询',
			},
			component(resolve){
				require(['@/app/stock/inquireStock'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.STOCK_REPORT,
			name: 'stockreport',
			meta: {
				requireAuth: true,
				title: '库存流水报表',
			},
			component(resolve){
				require(['@/app/stock/stockReport'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.REPORT_DETAIL + '/:code',
			name: 'reportdetail',
			meta: {
				requireAuth: true,
				title: '单据详情',
			},
			component(resolve){
				require(['@/app/stock/reportDetail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.STOCK_CHECK,
			name: 'stockcheck',
			meta: {
				requireAuth: true,
				title: '库存盘点',
			},
			component(resolve){
				require(['@/app/stock/check'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.STOCK_CHECK + '/:id',
			name: 'checkedit',
			meta: {
				requireAuth: true,
				title: '新增库存盘点',
			},
			component(resolve){
				require(['@/app/stock/checkEdit'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.ADD_PRO,
			name: 'addpro',
			meta: {
				requireAuth: true,
				title: '选择商品',
			},
			component(resolve){
				require(['@/app/stock/choosePro'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.RESTOCK,
			name: 'restock',
			meta: {
				requireAuth: true,
				title: '要货订单',
			},
			component(resolve){
				require(['@/app/purchase/restock/list'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.RESTOCK + '/:id',
			name: 'restockedit',
			meta: {
				requireAuth: true,
				title: '新增要货订单',
			},
			component(resolve){
				require(['@/app/purchase/restock/edit'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.RESTOCK_PRO,
			name: 'restockpro',
			meta: {
				requireAuth: true,
				title: '选择商品',
			},
			component(resolve){
				require(['@/app/purchase/restock/product'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.COUPON,
			name: 'coupon',
			meta: {
				requireAuth: true,
				title: '优惠券',
			},
			component(resolve){
				require(['@/app/coupon/index'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.COUPON + '/:id',
			name: 'editcoupon',
			meta: {
				requireAuth: true,
				title: '新建优惠券',
			},
			component(resolve){
				require(['@/app/coupon/new'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.COUPON_PRO + '/:type',
			name: 'couponpro',
			meta: {
				requireAuth: true,
				title: '商品范围',
			},
			component(resolve){
				require(['@/app/coupon/couponPro'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.COUPON_VIP,
			name: 'couponvip',
			meta: {
				requireAuth: true,
				title: '会员范围',
			},
			component(resolve){
				require(['@/app/coupon/couponVip'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.SCRABBLE,
			name: 'scrabble',
			meta: {
				requireAuth: true,
				title: '线上订单',
			},
			component(resolve){
				require(['@/app/scrabble/scrabble'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.ORDER_MANAGE,
			name: 'ordermanage',
			meta: {
				requireAuth: true,
				title: '订单管理',
			},
			component(resolve){
				require(['@/app/scrabble/orderManage'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.SCRABBLE_DETAIL + '/:id',
			name: 'scrabbleDetail',
			meta: {
				requireAuth: true,
				title: '订单详情',
			},
			component(resolve){
				require(['@/app/scrabble/detail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.POS_SET,
			name: 'posset',
			meta: {
				requireAuth: true,
				title: 'pos列表',
			},
			component(resolve){
				require(['@/app/posSet/index'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.POS_DETAIL,
			name: 'posdetail',
			meta: {
				requireAuth: true,
				title: '热键设置详情',
			},
			component(resolve){
				require(['@/app/posSet/detail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.POS_CHOOSE_PRO,
			name: 'poschoosepro',
			meta: {
				requireAuth: true,
				title: '选择热键商品',
			},
			component(resolve){
				require(['@/app/posSet/choosePro'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.HYDROELECTRIC,
			name: 'hydroelectric',
			meta: {
				requireAuth: true,
				title: '水电管理',
			},
			component(resolve){
				require(['@/app/withdraw/hydroelectric'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.RECHARGE,
			name: 'recharge',
			meta: {
				requireAuth: true,
				title: '充值',
			},
			component(resolve){
				require(['@/app/withdraw/recharge'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.RECHARGE_LIST,
			name: 'rechargeList',
			meta: {
				requireAuth: true,
				title: '充值记录',
			},
			component(resolve){
				require(['@/app/withdraw/rechargeList'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.UUID_CONFIRM,
			name: 'uuidConfirm',
			meta: {
				requireAuth: true,
				title: '确认绑定',
			},
			component(resolve){
				require(['@/app/uuid/confirm'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.INSPECTION_MSG,
			name: 'inspectionmsg',
			meta: {
				requireAuth: true,
				title: '巡检处理',
			},
			component(resolve){
				require(['@/app/inspection/list'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.INSPECTION_BILL + '/:id/:amount?',
			name: 'inspectionbill',
			meta: {
				requireAuth: true,
				title: '账单详情',
			},
			component(resolve){
				require(['@/app/inspection/billDetail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.INSPECTION_NOTICE + '/:id',
			name: 'inspectionnotice',
			meta: {
				requireAuth: true,
				title: '巡检通知',
			},
			component(resolve){
				require(['@/app/inspection/notice'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.INSPECTION_DEAL + '/:id',
			name: 'inspectiondeal',
			meta: {
				requireAuth: true,
				title: '整改处理',
			},
			component(resolve){
				require(['@/app/inspection/deal'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.INSPECTION_DEAL_DETAIL + '/:id',
			name: 'inspectiondealdetail',
			meta: {
				requireAuth: true,
				title: '处理详情',
			},
			component(resolve){
				require(['@/app/inspection/dealDetail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.PAY_RESULT,
			name: 'payresult',
			meta: {
				requireAuth: false,
				title: '支付结果',
			},
			component(resolve){
				require(['@/app/status/payResult'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.LEAGUE,
			name: 'league',
			meta: {
				requireAuth: true,
				title: '车辆管理',
			},
			component(resolve){
				require(['@/app/league/league'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.NEW_CAR,
			name: 'newcar',
			meta: {
				requireAuth: true,
				title: '上架车辆',
			},
			component(resolve){
				require(['@/app/league/newCar'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: '/seeInfos/:status',
			name: 'seeInfos',
			meta: {
				requireAuth: true,
				title: '交易详情',
			},
			component: () => import('@/app/userdcar/seeInfos.vue'),

		},
		{
			path: URL.EMPLOYEE_MANAGEMENT,
			name: 'employeeManagement',
			meta: {
				requireAuth: true,
				title: '员工管理',
			},
			component(resolve){
				require(['@/app/employee/index'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.EMPLOYEE_DETAIL,
			name: 'employeeDetail',
			meta: {
				requireAuth: true,
				title: '员工详情',
			},
			component(resolve){
				require(['@/app/employee/detail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.ADD_EMPLOYEE,
			name: 'addEmployee',
			meta: {
				requireAuth: true,
				title: '新增员工',
			},
			component(resolve){
				require(['@/app/employee/add'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: URL.CAR_DETAIL + '/:id',
			name: 'carDetail',
			meta: {
				requireAuth: true,
				title: '车辆详情',
			},
			component(resolve){
				require(['@/app/car/detail'], comp => {
					resolve(comp);
				});
			},
		},
		{
			path: '/mp/sdnmwxm/car/detail/:id',
			name: 'carDetail',
			meta: {
				requireAuth: true,
				title: '车辆详情',
			},
			component(resolve) {
				require(['../app/car/CarDetail.vue'], resolve)
			}
		},
	]
});
