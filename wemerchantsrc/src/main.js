// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from "vue";
import store from "./vuex";
import App from "./App";
import router from "./router";
import VueResource from "vue-resource";
import * as URL from "./modules/URLs";
import APIs from "./modules/APIs";
import * as CONST from "./modules/CONSTs";
import VueHighcharts from "vue-highcharts";
import Highcharts from "highcharts";
import VueQrcode from "@chenfengyuan/vue-qrcode";
import VConsole from "vconsole";
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import Router from "vue-router";
import {isIOS} from "./modules/ua";
import {setToken} from "./vuex/actions";

Vue.config.productionTip = false;
Vue.config.devtools = true;
const vConsole = new VConsole();

Vue.use(VueHighcharts, {Highcharts});
Vue.use(VueResource);
Vue.use(require("vue-wechat-title"));
Vue.use(ElementUI);

Vue.component(VueQrcode.name, VueQrcode);

function isWechaTool() {
	let ua = window.navigator.userAgent.toLowerCase();
	if (ua.includes('MicroMessenger') && ua.includes('wechatdevtools')) {
		return true;
	} else if (ua.includes('MicroMessenger')) {
		return false;
	}

}

router.afterEach((to, from, next) => {
	const url = window.location.href.split('#')[0]
	// ios手机环境 并且是非开发者工具
	if (isIOS() && !isWechaTool()) {
		if (!store.state.initLink) store.commit('SET_INIT_LINK', url)
	} else {
		store.commit('SET_INIT_LINK', url)
	}
})

// 登录认证
router.beforeEach((to, from, next) => {
	debugger;

	if (!store.state.url) {
		store.dispatch("setUrl", document.URL);
	}
	var token = store.state.token;
	var token_time  = store.state.token_time
	if (to.meta.requireAuth) {
		debugger;
		if (
				store.state.userInfo.user_id !== undefined && store.state.userInfo.user_id !== '' && store.state.userInfo.user_id !== null && token!== undefined && token!== '' && token!== null && new Date().getTime() - token_time < 1800000
		) {
			store.commit("SET_TOKEN", {
				token: token,
				time: new Date().getTime()
			})
			next();
		} else {
			next({path: URL.LOGIN});
			store.commit("CLEAR_USER_INFO")
			store.commit("CLEAR_DATA")
			store.commit("CLEAR_ORGZ_INFO")
			store.commit("CLEAR_TOKEN_INFO")
		}
	} else {
		next();
	}
});

new Vue({
	el: "#app",
	router,
	store,
	template: "<App/>",
	components: {App},
});
