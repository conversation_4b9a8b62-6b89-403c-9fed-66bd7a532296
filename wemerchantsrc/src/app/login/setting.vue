<template>
	<div id="setting" class="content">
		<div class="bl-info">
			<div class="portrait" :style="portraitStyle"></div>
			<div class="content">
				<div>{{data.name}}</div>
				<div>{{data.mobile}}</div>
			</div>
		</div>
		<div class="bl-out" @click="logout">
			&nbsp;&nbsp;退出登录
		</div>
		<layer :show="confirm.show" :showTitle="false" :rightCallback="callback" :leftCallback="cancel">
			<div class="dlg-content">{{confirm.content}}</div>
		</layer>
	</div>
</template>
<script>
/**
 * @module app/login/setting
 * @desc 速店农贸版 个人设置页面
 * @version 0.0.1
 * <AUTHOR> <<EMAIL>>
 * @date 2017-12-22
 * @copyright 2017
 */
import { apiPost, testImage } from '_/utils';
import APIs from '_/APIs';
import layer from '@@/layer';
import router from '@/router';
import * as URL from '_/URLs';
import { mapActions } from 'vuex';
const actions = mapActions([ 'clearUserInfo', 'setAlert' ]);
export default {
	name: 'setting',
	data(){
		return {
			data: {},
			fetchFail: false, // 头像图片获取失败标记
			confirm: {
				content: '确定要退出登录吗？',
				show: false
			}
		};
	},
	components: { layer },
	computed: {
		portraitStyle(){
			if(!this.fetchFail && this.data && this.data.avatar){
				return 'background: url(\'' + this.data.avatar + '\') center center / 100% 100% no-repeat';
			}
			return 'background: url(\'/images/wemerchant/portrait.png\') center center / 100% 100% no-repeat';
		}
	},
	beforeRouteEnter(to, from, next){
		let getUserData = new Promise(function(resolve, reject){
			apiPost({
				url: APIs.AUTH,
				data: {
					action: 'account.current'
				},
				success(res){
					resolve(res);
				},
				error(res){
					reject(res);
				}
			});
		});
		Promise.all([getUserData]).then(values => {
			next(vm => {
				if(values[0].code + '' === '0'){
					vm.data = values[0].data;
					testImage(vm.data.avatar, status => {
						switch(status){
							case 'error':
							case 'timeout':
								vm.fetchFail = true;
								break;
							case 'success':
								vm.fetchFail = false;
								break;
						}
					});
				}
				else{
					vm.setAlert({
						msg: values[0].msg,
						type: false
					});
				}
			});
		})
		.catch(values => {
			next(vm => {
				vm.setAlert({
					msg: '获取用户信息失败',
					type: false
				});
			});
		});
	},
	methods: {
		...actions,
		callback(){ // 回调
			let $this = this;
			apiPost({
				url: APIs.AUTH,
				data: {
					action: 'account.logout'
				},
				success(res){
					$this.confirm.show = false;
					if(res.code + '' === '0'){
						$this.clearUserInfo();
						router.push(URL.LOGIN);
					}
					else{
						$this.setAlert({
							msg: res.msg,
							type: false
						});
					}
				},
				error(res){
					$this.confirm.show = false;
					$this.setAlert({
						msg: '登出失败',
						type: false
					});
				}
			});
		},
		logout(){
			this.confirm.show = true;
		},
		cancel(){ // 取消
			this.confirm.show = false;
		}
	}
};
</script>
<style lang="less">
@import '../../less/common';
#setting{
	.bgc(#fff);
	.bl-info{
		display: flex;
		align-items: center;
		.pd(25px * @px22rem, 15px * @px2rem, 25px * @px22rem, 15px * @px2rem);
		.brd-b(1px, solid, #E5E5E5);
	}
	.portrait{
		.wh(70px * @px22rem, 70px * @px22rem);
		.brdr(35px * @px22rem);
		.bgc(#eee);
		background: url('/images/wemerchant/portrait.png') center center / 100% 100% no-repeat;
	}
	.content{
		flex: 1;
		align-items: center;
		justify-content: left;
		.mgl(15px * @px22rem);
		div:nth-child(1){
			.bold();
			.fgc(#000);
			.mgb(5px * @px22rem);
		}
		div:nth-child(2){
			.fgc(#888);
		}
	}
	.bl-out{
		display: flex;
		align-items: center;
		.pd(12px * @px22rem, 15px * @px22rem, 12px * @px22rem, 15px * @px22rem);
		.fgc(#000);
		.pos-r();
		&:before{
			content: '';
			.wh(20px * @px22rem, 20px * @px22rem);
			background: url('/images/wemerchant/<EMAIL>') center center / 100% 100% no-repeat;
		}
		&:after{
			content: '';
			.wh(14px * @px22rem, 14px * @px22rem);
			.pos-a();
			left: auto;
			top: 0;
			right: 12px * @px22rem;
			bottom: 0;
			.mg(auto);
			background: url('/images/wemerchant/<EMAIL>') center center / 100% 100% no-repeat;
		}
	}
	.dlg-content{
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 30px * @px22rem;
		.fgc(#333);
		.mgb(50px * @px22rem);
		height: 4.3rem;
	}
}

.make-dpr-font(@dpr){
	[data-dpr="@{dpr}"]{
		#setting {
			.content {
				div:nth-child(1) {
					.fs(25px * @dpr);
				}
				div:nth-child(2) {
					.fs(18px * @dpr);
				}
			}
			.dlg-content, .bl-out {
				.fs(17px * @dpr);
			}
		}
	}
}
.make-dpr-font(1);
.make-dpr-font(2);
.make-dpr-font(3);
</style>
