<template>
	<div id="choosePro" class="content">
		<div class="search-head">
			<div class="search-inp" :class="{'onfocus': onfocus, 'unfocus': !onfocus}">
				<input type="text" placeholder="搜索商品" v-model="keyword" @focus="getFocus" @keyup.enter="getSearchResult('delete')">
				<div v-if="keyword !== ''" class="clean" @click="cleanKeyword"></div>
			</div>
			<div class="search-btn">
				<!-- <span :class="{'type': !typeFilter, 'type-close': typeFilter}" v-if="!onfocus" @click="openType">类别筛选</span> -->
				<span class="close" v-if="keyword !== ''" @click="getSearchResult('delete')">搜索</span>
				<span class="close" v-if="keyword === ''" @click="cancle">取消</span>
			</div>
		</div>
		<div class="pro-content" v-if="productList.length">
			<scroll ref="posProScroll"
				:scroll-x="false"
				:scroll-y="true"
				id="posProScroll"
				scroll-id="#posProScroll"
				:more="true"
				:wait="scrollObj.wait"
				:isover="scrollObj.isover"
				v-on:bindscrolltolower="scrollBottom"
				v-on:bindscrolltoupper="scrollUp">
				<div class="pro-list" v-for="list in productList" @click="choosePro(list)">
					<div class="name">{{list.name}}</div>
				</div>
			</scroll>
		</div>
		<div class="empty-pro" v-else>
			<div class="empty-img">
				<!-- <img src="/images/wemerchant/no-group.png"> -->
				<div>无商品信息</div>
			</div>
		</div>
	</div>
</template>

<script>
/**
 * @file posSet/choosePro.vue
 * @desc 速店农贸h5商户 pos设置商品
 * @version 0.0.1
 * <AUTHOR> Jiajun <<EMAIL>>
 * @date 2017-12-19
 * @copyright 2017
 */
/* jshint esversion: 6 */
/* eslint-disable no-new */
import APIs from '_/APIs';
import { apiPost } from '_/utils';
// import store from './vuex';
import router from '@/router';
// import * as URL from '_/URLs';
import scroll from '@@/scroll';
// import * as CONST from '_/CONSTs';
import { mapActions, mapGetters } from 'vuex';
const getters = mapGetters(['userInfo']);
const actions = mapActions(['setAlert']);
export default {
	name: 'choosePro',
	components: {
		scroll,
	},
	computed: {
		...getters,
	},
	created(){
		this.getSearchResult('delete');
	},
	data(){
		return {
			productList: [],
			keyword: '',
			onfocus: false,
			scrollObj: {
				wait: false,
				isover: false,
			},
			search: {
				start: 0,
				length: 20,
				total: 0,
			},
		};
	},
	watch: {
		// errorMsg(newValue, oldValue){
		// 	if(newValue !== ''){
		// 		let time = 3000;
		// 		setTimeout(() => {
		// 			this.errorMsg = '';
		// 		}, time);
		// 	}
		// },
	},
	methods: {
		...actions,
		/**
		 * 下拉滚动
		 * <AUTHOR> Jiajun <<EMAIL>>
		 * @date   2019-04-24
		 */
		scrollBottom(){
			if(this.productList.length >= this.search.total || !this.productList.length){ // 加载出来的数据已经比total多，或者上一页获取的数据就是空了
				this.scrollObj.isover = true;
				this.scrollObj.wait = true;
				setTimeout(() => {
					this.scrollObj.wait = false;
				}, 1000);
			}
			else{
				this.search.start = this.productList.length ? this.productList.length : 0;
				this.scrollObj.wait = true;
				this.getSearchResult('refresh');
			}
		},
		/**
		 * 上拉刷新
		 * <AUTHOR> Jiajun <<EMAIL>>
		 * @date   2019-04-24
		 */
		scrollUp(){
			this.scrollObj.isover = false;
			this.scrollObj.wait = true;
			this.search.start = 0;
			this.getSearchResult('delete');
		},
		/**
		 * 获取商品数据
		 * <AUTHOR> Jiajun <<EMAIL>>
		 * @date   2019-04-24
		 */
		getSearchResult(str){
			this.search.start = str === 'delete' ? 0 : this.search.start;
			let $this = this;
			apiPost({
				url: APIs.PRODUCT,
				data: {
					action: 'product.list',
					start: this.search.start,
					length: this.search.length,
					product_name: this.keyword,
				},
				success(res){
					if(res.code === '0'){
						if(str !== 'delete'){
							$this.productList = $this.productList.concat(res.data);
						}
						else{
							$this.productList = res.data;
						}
						// $this.productList.forEach(el => {
						// 	$this.chooseInfo.list.forEach(rel => {
						// 		if(rel.id === el.id){
						// 			el.choosed = true;
						// 		}
						// 	});
						// });
						$this.search.total = res.total;
					}
					else{
						$this.productList = [];
						$this.search.total = 0;
						$this.setAlert({msg: res.msg, type: false});
					}
					$this.scrollObj.wait = false;
				},
				error(res){
					$this.scrollObj.wait = false;
					$this.productList = [];
					$this.search.total = 0;
					$this.setAlert({msg: res.msg, type: false});
				},
			});
		},
		/**
		 * 输入框获取焦点
		 * <AUTHOR> Jiajun <<EMAIL>>
		 * @date   2017-12-18
		 */
		getFocus(){
			this.onfocus = true;
		},
		/**
		 * 清除关键字
		 * <AUTHOR> Jiajun <<EMAIL>>
		 * @date   2017-12-18
		 */
		cleanKeyword(){
			this.keyword = '';
		},
		/**
		 * 退出搜索
		 * <AUTHOR> Jiajun <<EMAIL>>
		 * @date   2017-12-18
		 */
		cancle(){
			this.onfocus = false;
			this.keyword = '';
		},
		/**
		 * 选择档口
		 * <AUTHOR> Jiajun <<EMAIL>>
		 * @date   2017-12-18
		 */
		choosePro(row){
			let obj = JSON.stringify({
				id: row.id,
				name: row.name,
			});
			localStorage.setItem('posPro', obj);
			router.go(-1);
		},
	},
};
</script>

<style lang="less">
@import '../../less/common';
@px2rem: 2rem / 75px;
#choosePro{
	.bgc(#fff);
	height: 100%;
	.search-head{
		width: 100%;
		.hlh(44px * @px2rem);
		// .brd-b(1px, solid, rgba(0, 0, 0, 0.1));
		.pd(8px * @px2rem);
		display: flex;
		align-items: center;
		.bgc(#EFEFF4);
		.search-inp{
			flex: 1;
			.dib();
			height: 28px * @px2rem;
			.pos-r();
			.bgc(#fff);
			.brdr(5px * @px2rem);
			> input{
				width: 100%;
				height: 28px * @px2rem;
				border-style: none;
				.brdr(5px);
				.ta-lt();
				// .pdl(26px * @px2rem);
			}
			&.unfocus{
				width: 310px * @px2rem;
				&:after{
					content: '';
					.wh(13px * @px2rem, 13px * @px2rem);
					position: absolute;
					top: 7.5px * @px2rem;
					left: 120px * @px2rem;
					background-image: image-set('/images/wemerchant/<EMAIL>' 1x);
					background-repeat: no-repeat;
					background-size: 100%;
				}
			}
			&.onfocus{
				width: 310px * @px2rem;
				.brdr(5px);
				.pdl(26px * @px2rem);
				&:after{
					content: '';
					.wh(13px * @px2rem, 13px * @px2rem);
					position: absolute;
					top: 7.5px * @px2rem;
					left: 8px * @px2rem;
					background-image: image-set('/images/wemerchant/<EMAIL>' 1x);
					background-repeat: no-repeat;
					background-size: 100%;
				}
			}
			.clean{
				// .dib();
				.wh(15px * @px2rem, 15px * @px2rem);
				position: absolute;
				top: 7.5px * @px2rem;
				right: 10px * @px2rem;
				background-image: image-set('/images/wemerchant/<EMAIL>' 1x);
				background-repeat: no-repeat;
				background-size: 100%;
				.cs();
			}
			input::-webkit-input-placeholder {
				.ta-c();
			}
			input:-moz-placeholder {
				.ta-c();
			}
			input:-ms-input-placeholder {
				.ta-c();
			}
			input::placeholder {
				.ta-c();
			}
		}
		.search-btn{
			.dib();
			.mgl(8px * @px2rem);
			.pos-r();
			// .type{
			// 	.fgc(#333333);
			// 	&:after{
			// 		content: '';
			// 		.wh(10px * @px2rem, 10px * @px2rem);
			// 		position: absolute;
			// 		top: 5.5px * @px2rem;
			// 		right: -10px * @px2rem;
			// 		background-image: image-set('/images/wemerchant/<EMAIL>' 1x);
			// 		background-repeat: no-repeat;
			// 		background-size: 100%;
			// 	}
			// }
			// .type-close{
			// 	.fgc(#F7B32D);
			// 	&:after{
			// 		content: '';
			// 		.wh(10px * @px2rem, 10px * @px2rem);
			// 		position: absolute;
			// 		top: 5.5px * @px2rem;
			// 		right: -10px * @px2rem;
			// 		background-image: image-set('/images/wemerchant/<EMAIL>' 1x);
			// 		background-repeat: no-repeat;
			// 		background-size: 100%;
			// 	}
			// }
			.close{
				.fgc(#F7B32D);
			}
		}
	}
	.pro-content{
		-webkit-overflow-scrolling: touch;
		overflow-y: auto;
		height: calc(~"100% - 1.173rem");
		// .pdb(20px * @px2rem);
	}
	#posProScroll{
		.pos-r();
		height: 100%;
	}
	.pro-list{
		.pos-r();
		.bgc(#fff);
		.brd-b(1px, solid, #EEEEEE);
		.mgl(15px * @px2rem);
		.hlh(44px * @px2rem, 44px * @px2rem);
		.name{
			.fgc(#353535);
		}
		// .address{
		// 	.fgc(#888888);
		// }
		// .choosed{
		// 	position: absolute;
		// 	background-image: image-set('/images/wemerchant/hook.png' 1x);
		// 	// background-image: image-set('/images/wemerchant/no-group.png' 1x);
		// 	background-repeat: no-repeat;
		// 	background-size: 100%;
		// 	width: 13px * @px2rem;
		// 	height: 13px * @px2rem;
		// 	top: 26px * @px2rem;
		// 	right: 15px * @px2rem;
		// }
	}
	.empty-pro{
		width: 100%;
		.ta-c();
		.pdt(100px * @px2rem);
		.empty-img{
			img{
				.wh(80px * @px2rem, 80px * @px2rem);
			}
			div{
				.fgc(#888888);
				.mgt(8px * @px2rem);
			}
		}
	}
}

.make-dpr-font(@dpr){
	[data-dpr="@{dpr}"]{
		#choosePro{
			.pro-list{
				.name{
					.fs(14px * @dpr);
				}
			}
			.empty-pro{
				.empty-img{
					div{
						.fs(14px * @dpr);
					}
				}
			}
			.search-inp{
				> input{
					.fs(13px * @dpr);
				}
			}
			.search-btn{
				> span{
					.fs(16px * @dpr);
				}
			}
			.search-inp{
				input::-webkit-input-placeholder {
					.fs(13px * @dpr);
				}
				input:-moz-placeholder {
					.fs(13px * @dpr);
				}
				input:-ms-input-placeholder {
					.fs(13px * @dpr);
				}
				input::placeholder {
					.fs(13px * @dpr);
				}
			}
		}
	}
}
.make-dpr-font(1);
.make-dpr-font(2);
.make-dpr-font(3);
</style>