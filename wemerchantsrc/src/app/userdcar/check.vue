<template>
	<div class="body-bgw">
		<header class="hui-header" style="position: static">
			<h1>照片资料检查</h1>
			<div class="hui-header-empty"></div>
		</header>
		<div class="hui-wrap">
			<div class="photo-check-container">
				<!-- 买方资料 -->
				<div class="photo-section">
					<div class="section-header">
						<h2>买方资料</h2>
						<span class="section-subtitle">(现车主)</span>
						<span class="type-badge" :class="buyerTypeClass">{{ buyerTypeText }}</span>
					</div>
					<div class="photo-grid">
						<!-- 买方个人类型：身份证正反面 -->
						<template v-if="!isBuyerCompany">
							<!-- 买方身份证正面 -->
							<div class="photo-item">
								<div class="photo-label">身份证正面</div>
								<div class="photo-content" :class="{ 'no-image': !formData.buyInfos.personUp }">
									<img v-if="formData.buyInfos.personUp"
										 :src="formData.buyInfos.personUp"
										 alt="身份证正面"
										 @click="previewImage(formData.buyInfos.personUp, '买方身份证正面')"/>
									<div v-else class="placeholder" @click="triggerFileInput('buyInfos.personUp')">
										<i class="iconfont icon-camera"></i>
										<span>点击上传</span>
									</div>
									<!-- 删除按钮 -->
									<button v-if="formData.buyInfos.personUp"
											class="delete-btn"
											@click.stop="deletePhoto('buyInfos.personUp')">
										×
									</button>
								</div>
							</div>
							<!-- 买方身份证反面 -->
							<div class="photo-item">
								<div class="photo-label">身份证反面</div>
								<div class="photo-content" :class="{ 'no-image': !formData.buyInfos.personDown }">
									<img v-if="formData.buyInfos.personDown"
										 :src="formData.buyInfos.personDown"
										 alt="身份证反面"
										 @click="previewImage(formData.buyInfos.personDown, '买方身份证反面')"/>
									<div v-else class="placeholder" @click="triggerFileInput('buyInfos.personDown')">
										<i class="iconfont icon-camera"></i>
										<span>点击上传</span>
									</div>
									<!-- 删除按钮 -->
									<button v-if="formData.buyInfos.personDown"
											class="delete-btn"
											@click.stop="deletePhoto('buyInfos.personDown')">
										×
									</button>
								</div>
							</div>
						</template>

						<!-- 买方单位类型：营业执照 -->
						<template v-else>
							<!-- 买方营业执照 -->
							<div class="photo-item">
								<div class="photo-label">营业执照</div>
								<div class="photo-content" :class="{ 'no-image': !formData.buyInfos.company }">
									<img v-if="formData.buyInfos.company"
										 :src="formData.buyInfos.company"
										 alt="营业执照"
										 @click="previewImage(formData.buyInfos.company, '买方营业执照')"/>
									<div v-else class="placeholder" @click="triggerFileInput('buyInfos.company')">
										<i class="iconfont icon-camera"></i>
										<span>点击上传</span>
									</div>
									<!-- 删除按钮 -->
									<button v-if="formData.buyInfos.company"
											class="delete-btn"
											@click.stop="deletePhoto('buyInfos.company')">
										×
									</button>
								</div>
							</div>
						</template>
					</div>
				</div>

				<!-- 卖方资料 -->
				<div class="photo-section">
					<div class="section-header">
						<h2>卖方资料</h2>
						<span class="section-subtitle">(原车主)</span>
						<span class="type-badge" :class="sellerTypeClass">{{ sellerTypeText }}</span>
					</div>
					<div class="photo-grid">
						<!-- 卖方个人类型：身份证正反面 + 手持身份证 -->
						<template v-if="!isSellerCompany">
							<!-- 卖方身份证正面 -->
							<div class="photo-item">
								<div class="photo-label">身份证正面</div>
								<div class="photo-content" :class="{ 'no-image': !formData.saleInfos.personUp }">
									<img v-if="formData.saleInfos.personUp"
										 :src="formData.saleInfos.personUp"
										 alt="身份证正面"
										 @click="previewImage(formData.saleInfos.personUp, '卖方身份证正面')"/>
									<div v-else class="placeholder" @click="triggerFileInput('saleInfos.personUp')">
										<i class="iconfont icon-camera"></i>
										<span>点击上传</span>
									</div>
									<!-- 删除按钮 -->
									<button v-if="formData.saleInfos.personUp"
											class="delete-btn"
											@click.stop="deletePhoto('saleInfos.personUp')">
										×
									</button>
								</div>
							</div>
							<!-- 卖方身份证反面 -->
							<div class="photo-item">
								<div class="photo-label">身份证反面</div>
								<div class="photo-content" :class="{ 'no-image': !formData.saleInfos.personDown }">
									<img v-if="formData.saleInfos.personDown"
										 :src="formData.saleInfos.personDown"
										 alt="身份证反面"
										 @click="previewImage(formData.saleInfos.personDown, '卖方身份证反面')"/>
									<div v-else class="placeholder" @click="triggerFileInput('saleInfos.personDown')">
										<i class="iconfont icon-camera"></i>
										<span>点击上传</span>
									</div>
									<!-- 删除按钮 -->
									<button v-if="formData.saleInfos.personDown"
											class="delete-btn"
											@click.stop="deletePhoto('saleInfos.personDown')">
										×
									</button>
								</div>
							</div>
							<!-- 卖方手持身份证照片 -->
							<div class="photo-item">
								<div class="photo-label">手持身份证照片</div>
								<div class="photo-content" :class="{ 'no-image': !formData.saleInfos.handImage }">
									<img v-if="formData.saleInfos.handImage"
										 :src="formData.saleInfos.handImage"
										 alt="手持身份证照片"
										 @click="previewImage(formData.saleInfos.handImage, '卖方手持身份证照片')"/>
									<div v-else class="placeholder" @click="triggerFileInput('saleInfos.handImage')">
										<i class="iconfont icon-camera"></i>
										<span>点击上传</span>
									</div>
									<!-- 删除按钮 -->
									<button v-if="formData.saleInfos.handImage"
											class="delete-btn"
											@click.stop="deletePhoto('saleInfos.handImage')">
										×
									</button>
								</div>
							</div>
						</template>

						<!-- 卖方单位类型：营业执照 -->
						<template v-else>
							<!-- 卖方营业执照 -->
							<div class="photo-item">
								<div class="photo-label">营业执照</div>
								<div class="photo-content" :class="{ 'no-image': !formData.saleInfos.company }">
									<img v-if="formData.saleInfos.company"
										 :src="formData.saleInfos.company"
										 alt="营业执照"
										 @click="previewImage(formData.saleInfos.company, '卖方营业执照')"/>
									<div v-else class="placeholder" @click="triggerFileInput('saleInfos.company')">
										<i class="iconfont icon-camera"></i>
										<span>点击上传</span>
									</div>
									<!-- 删除按钮 -->
									<button v-if="formData.saleInfos.company"
											class="delete-btn"
											@click.stop="deletePhoto('saleInfos.company')">
										×
									</button>
								</div>
							</div>
						</template>
					</div>
				</div>

				<!-- 车辆资料 -->
				<div class="photo-section">
					<div class="section-header">
						<h2>车辆资料</h2>
					</div>
					<div class="photo-grid">
						<!-- 行驶证照片 -->
						<div class="photo-item">
							<div class="photo-label">行驶证照片</div>
							<div class="photo-content" :class="{ 'no-image': !formData.carInfos.xsz }">
								<img v-if="formData.carInfos.xsz"
									 :src="formData.carInfos.xsz"
									 alt="行驶证照片"
									 @click="previewImage(formData.carInfos.xsz, '行驶证照片')"/>
								<div v-else class="placeholder" @click="triggerFileInput('carInfos.xsz')">
									<i class="iconfont icon-camera"></i>
									<span>点击上传</span>
								</div>
								<!-- 删除按钮 -->
								<button v-if="formData.carInfos.xsz"
										class="delete-btn"
										@click.stop="deletePhoto('carInfos.xsz')">
									×
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="bottom-bt-box">
			<button type="button" class="hui-button hui-default" @click="goBack">
				返回修改
			</button>
			<button type="button" class="hui-button hui-primary" @click="submitCheck">
				确认提交
			</button>
		</div>

		<!-- 图片预览弹窗 -->
		<div v-if="showPreview" class="image-preview-mask" @click="closePreview">
			<div class="image-preview-container" @click.stop>
				<div class="preview-header">
					<span class="preview-title">{{ previewTitle }}</span>
					<button class="close-btn" @click="closePreview">×</button>
				</div>
				<div class="preview-content">
					<img :src="previewImageUrl" :alt="previewTitle" class="preview-image"/>
				</div>
			</div>
		</div>

		<!-- 隐藏的文件输入框 -->
		<input
			ref="fileInput"
			type="file"
			accept="image/*"
			capture="camera"
			style="display: none"
			@change="handleFileUpload"
		/>
	</div>
</template>

<script>
import router from "@/router";
import APIs from "_/APIs";
import {apiPost} from "_/utils";

export default {
	name: 'PhotoCheck',
	data() {
		return {
			formData: JSON.parse(JSON.stringify(this.$store.state.fileData)),
			isBuyerCompany: this.$store.state.formData.typeUp === '单位',
			isSellerCompany: this.$store.state.formData.typeDown === '单位',
			showPreview: false,
			previewImageUrl: '',
			previewTitle: '',
			currentPhotoField: '' // 当前正在上传的照片字段
		}
	},
	computed: {
		buyerTypeText() {
			return this.isBuyerCompany ? '单位' : '个人';
		},
		sellerTypeText() {
			return this.isSellerCompany ? '单位' : '个人';
		},
		buyerTypeClass() {
			return this.isBuyerCompany ? 'company' : 'person';
		},
		sellerTypeClass() {
			return this.isSellerCompany ? 'company' : 'person';
		}
	},
	created() {

	},
	methods: {
		goBack() {
			router.go(-1);
		},
		setValueByPath(obj, path, value) {
			const segments = path.split('.');
			let current = obj;

			// 遍历到倒数第二个节点
			for (let i = 0; i < segments.length - 1; i++) {
				const key = segments[i];

				// 如果中间路径不存在，创建空对象
				if (!current[key]) {
					current[key] = {};
				}

				current = current[key];
			}

			// 设置最后一个属性的值
			const lastKey = segments[segments.length - 1];
			current[lastKey] = value;
		},
		previewImage(imageUrl, title) {
			this.previewImageUrl = imageUrl;
			this.previewTitle = title;
			this.showPreview = true;
			// 禁止背景滚动
			document.body.style.overflow = 'hidden';
		}
		,
		closePreview() {
			this.showPreview = false;
			// 恢复背景滚动
			document.body.style.overflow = '';
		}
		,
// 删除照片
		deletePhoto(fieldPath) {
			if (confirm('确定要删除这张照片吗？')) {
				const pathSegments = fieldPath.split('.');
				let current = this.formData;
				let parent = null;
				let lastKey = '';

				for (let i = 0; i < pathSegments.length; i++) {
					lastKey = pathSegments[i];
					parent = current;
					current = current[lastKey];

					if (!current && i < pathSegments.length - 1) {
						console.error('路径不存在');
						return;
					}
				}

				if (parent) {
					parent[lastKey] = '';
				}
				debugger;
				this.$store.commit('UPDATE_STEP3_DATA', {
					...this.formData
				})
				console.log(`已删除照片: ${fieldPath}`);
			}
		}
		,
		triggerFileInput(fieldPath) {
			debugger;
			this.currentPhotoField = fieldPath;
			this.$refs.fileInput.click();
		}
		,
// 处理文件上传
		async handleFileUpload(event) {
			debugger;
			const file = event.target.files[0];
			if (!file) return;

			try {
				// 验证文件类型
				if (!file.type.startsWith('image/')) {
					alert('请选择图片文件');
					return;
				}

				// 验证文件大小（限制为5MB）
				if (file.size > 5 * 1024 * 1024) {
					alert('图片大小不能超过5MB');
					return;
				}

				// 转换为base64
				const base64 = await this.fileToBase64(file);
				debugger;

				// 更新对应的照片字段
				const pathSegments = this.currentPhotoField.split('.');
				let current = this.formData;
				let parent = null;
				let lastKey = '';

				for (let i = 0; i < pathSegments.length; i++) {
					lastKey = pathSegments[i];
					parent = current;
					current = current[lastKey];

					if (!current && i < pathSegments.length - 1) {
						console.error('路径不存在');
						return;
					}
				}

				if (parent) {
					parent[lastKey] = base64;
				}
				debugger;
				this.$store.commit('UPDATE_STEP3_DATA', {
					...this.formData
				})
				// console.log(`已删除照片: ${fieldPath}`);
				// const pathArray = this.currentPhotoField.split('.');
				// let target = this.formData;
				// for (let i = 0; i < pathArray.length - 1; i++) {
				//   target = target[pathArray[i]];
				// }
				// target[pathArray[pathArray.length - 1]] = base64;

				console.log(`照片上传成功: ${this.currentPhotoField}`);

				// 清空文件输入框
				event.target.value = '';

			} catch (error) {
				console.error('文件处理失败:', error);
				alert('文件处理失败，请重试');
			}
		}
		,
// 文件转base64
		fileToBase64(file) {
			return new Promise((resolve, reject) => {
				const reader = new FileReader();
				reader.readAsDataURL(file);
				reader.onload = () => resolve(reader.result);
				reader.onerror = (error) => reject(error);
			});
		}
		,
		submitCheck: async function () {
			// 校验必填照片
			const missingPhotos = [];
			let map = new Map();

			// 买方
			if (this.isBuyerCompany) {
				if (!this.formData.buyInfos.company) missingPhotos.push('买方营业执照')
				else {
					map.set("买方营业执照", this.formData.buyInfos.company)
				}
			} else {
				if (!this.formData.buyInfos.personUp) missingPhotos.push('买方身份证正面')
				else {
					map.set("买方身份证正面", this.formData.buyInfos.personUp)
				}
				if (!this.formData.buyInfos.personDown) missingPhotos.push('买方身份证反面')
				else {
					map.set("买方身份证反面", this.formData.buyInfos.personDown)
				}
			}
			// 卖方
			if (this.isSellerCompany) {
				if (!this.formData.saleInfos.company) missingPhotos.push('卖方营业执照')
				else {
					map.set("卖方营业执照", this.formData.saleInfos.company)
				}
			} else {
				if (!this.formData.saleInfos.personUp) missingPhotos.push('卖方身份证正面')
				else {
					map.set("卖方身份证正面", this.formData.saleInfos.personUp)
				}
				if (!this.formData.saleInfos.personDown) missingPhotos.push('卖方身份证反面')
				else {
					map.set("卖方身份证反面", this.formData.saleInfos.personDown)
				}
				if (!this.formData.saleInfos.handImage) missingPhotos.push('卖方手持身份证照片')
				else {
					map.set("卖方手持身份证", this.formData.saleInfos.handImage)
				}
			}
			// 车辆
			if (!this.formData.carInfos.xsz) missingPhotos.push('行驶证照片')
			else {
				map.set("行驶证", this.formData.carInfos.xsz)
			}
			if (missingPhotos.length > 0) {
				this.$message.error('请上传以下必需照片：' + missingPhotos.join('、'));
				return;
			}

			map.set("ywid", this.$store.state.formData.ywid);
			map.set("vincode", this.$store.state.carData.m.code);
			map.set("registerType", this.$store.state.formData.registerType);
			map.set("symbol", this.$store.state.carData.symbol);
			map.set("Closedcar", this.$store.state.carData.ClosedCar);
			map.set("typeR", this.$store.state.carData.typeR);
			let obj = Object.fromEntries(map);
			let jsonString = JSON.stringify(obj);


			apiPost({
				url: APIs.DEAL,
				data: {
					action: 'update.img',
					query: jsonString,
				},
				success: (res) => {
					debugger;
					console.log(res);
					if (Object.keys(res.modifyImages).length > 0) {
						Object.keys(res.modifyImages).forEach(key => {
							this.setValueByPath(this.formData,key,res.modifyImages[key])
						});
					};

					debugger;
					this.formData.video = res.spvideo;
					this.$store.commit('UPDATE_STEP3_DATA', {
						...this.formData
					})

					this.$router.push({
						name: 'seeInfos',
						query: {
							tradeId: this.$route.query.tradeId || this.$store.state.formData.ywid
						},params: {
							status: 'check'
						}
					});

				},
				error: (error) => {
					console.error('提交失败:', error);
					this.$message.error('提交失败');
				}
			})


		}
	},
	beforeDestroy() {
		// 组件销毁时恢复背景滚动
		document.body.style.overflow = '';
	}
}
</script>

<style lang="less" scoped>
@import "../../less/common";
@import "../../less/base/hui.css";

:root {
	--primary-color: #1890ff;
	--primary-light: #e6f7ff;
	--border-color: #e8e8e8;
	--text-color: #333;
	--text-secondary: #666;
	--bg-color: #f0f5ff;
}

html, body {
	height: 100%;
	margin: 0;
	padding: 0;
	overflow: hidden;
	background: var(--bg-color);
}

.body-bgw {
	height: 100%;
	display: flex;
	flex-direction: column;
	background: var(--bg-color);
}

.hui-header {
	flex-shrink: 0;
	background: #fff;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

	h1 {
		color: #fff;
		background-color: #1e88e5;
		font-size: 18px;
		margin: 0;
		padding: 12px 15px;
		text-align: center;
	}
}

.hui-wrap {
	flex: 1;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch;
	padding: 15px;
	height: calc(100vh - 120px);
}

.photo-check-container {
	max-width: 800px;
	margin: 0 auto;
}

.photo-section {
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
	margin-bottom: 20px;
	overflow: hidden;

	.section-header {
		background: var(--primary-light);
		padding: 15px 20px;
		border-bottom: 1px solid var(--border-color);
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 8px;

		h2 {
			color: var(--primary-color);
			font-size: 16px;
			margin: 0;
		}

		.section-subtitle {
			color: var(--text-secondary);
			font-size: 14px;
		}

		.type-badge {
			padding: 4px 8px;
			border-radius: 12px;
			font-size: 12px;
			font-weight: 500;

			&.person {
				background: #e6f7ff;
				color: #1890ff;
				border: 1px solid #91d5ff;
			}

			&.company {
				background: #f6ffed;
				color: #52c41a;
				border: 1px solid #b7eb8f;
			}
		}
	}
}

.photo-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 20px;
	padding: 20px;
}

.photo-item {
	.photo-label {
		color: var(--text-color);
		font-size: 14px;
		font-weight: 500;
		margin-bottom: 8px;
	}

	.photo-content {
		position: relative;
		width: 100%;
		padding-bottom: 75%; // 4:3 比例
		background: #fff;
		border: 1px solid var(--border-color);
		border-radius: 4px;
		overflow: hidden;

		&.no-image {
			border-style: dashed;
		}

		img {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			cursor: pointer;
			transition: transform 0.3s ease;

			&:hover {
				transform: scale(1.05);
			}
		}

		.placeholder {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			color: var(--text-secondary);
			cursor: pointer;
			transition: all 0.3s ease;

			&:hover {
				background: var(--primary-light);
				color: var(--primary-color);
			}

			i {
				font-size: 32px;
				margin-bottom: 8px;
				color: #ccc;
				transition: color 0.3s ease;
			}

			span {
				font-size: 14px;
				transition: color 0.3s ease;
			}

			&:hover i,
			&:hover span {
				color: var(--primary-color);
			}
		}

		// 删除按钮
		.delete-btn {
			position: absolute;
			top: 8px;
			right: 8px;
			width: 24px;
			height: 24px;
			border: none;
			border-radius: 50%;
			background: rgba(255, 77, 79, 0.9);
			color: #fff;
			font-size: 16px;
			font-weight: bold;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;
			z-index: 10;

			&:hover {
				background: rgba(255, 77, 79, 1);
				transform: scale(1.1);
			}
		}
	}
}

// 图片预览弹窗样式
.image-preview-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
}

.image-preview-container {
	background: #fff;
	border-radius: 8px;
	max-width: 90vw;
	max-height: 90vh;
	display: flex;
	flex-direction: column;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.preview-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px 20px;
	border-bottom: 1px solid var(--border-color);
	background: #f8f9fa;

	.preview-title {
		font-size: 16px;
		font-weight: 500;
		color: var(--text-color);
	}

	.close-btn {
		background: none;
		border: none;
		font-size: 24px;
		color: #999;
		cursor: pointer;
		padding: 0;
		width: 30px;
		height: 30px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		transition: all 0.3s;

		&:hover {
			background: #e6e6e6;
			color: #666;
		}
	}
}

.preview-content {
	flex: 1;
	padding: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: auto;

	.preview-image {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
		border-radius: 4px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}
}

.bottom-bt-box {
	flex-shrink: 0;
	padding: 12px 15px;
	background: #fff;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
	text-align: center;
	display: flex;
	justify-content: center;
	gap: 15px;

	.hui-button {
		min-width: 120px;
		height: 40px;
		line-height: 40px;
		border-radius: 4px;
		font-size: 15px;
		transition: all 0.3s;

		&.hui-default {
			background: #fff;
			border: 1px solid var(--border-color);
			color: var(--text-color);

			&:hover {
				border-color: var(--primary-color);
				color: var(--primary-color);
			}
		}

		&.hui-primary {
			background: var(--primary-color);
			border-color: var(--primary-color);
			color: #fff;

			&:hover {
				background: lighten(#1890ff, 10%);
				border-color: lighten(#1890ff, 10%);
			}
		}
	}
}

@media screen and (max-width: 375px) {
	.hui-wrap {
		padding: 10px;
	}

	.photo-grid {
		grid-template-columns: 1fr;
		gap: 15px;
		padding: 15px;
	}

	.photo-section {
		.section-header {
			padding: 12px 15px;

			h2 {
				font-size: 15px;
			}

			.section-subtitle {
				font-size: 13px;
			}

			.type-badge {
				font-size: 11px;
				padding: 3px 6px;
			}
		}
	}

	.photo-item {
		.photo-content {
			.delete-btn {
				top: 6px;
				right: 6px;
				width: 22px;
				height: 22px;
				font-size: 14px;
			}
		}
	}

	.bottom-bt-box {
		padding: 10px;

		.hui-button {
			min-width: 100px;
			height: 36px;
			line-height: 36px;
			font-size: 14px;
		}
	}

	.image-preview-mask {
		padding: 10px;
	}

	.preview-header {
		padding: 12px 15px;

		.preview-title {
			font-size: 14px;
		}

		.close-btn {
			font-size: 20px;
			width: 28px;
			height: 28px;
		}
	}

	.preview-content {
		padding: 15px;
	}
}

@media screen and (max-width: 320px) {
	.photo-grid {
		padding: 12px;
		gap: 12px;
	}

	.photo-item {
		.photo-label {
			font-size: 13px;
		}

		.photo-content {
			.delete-btn {
				width: 20px;
				height: 20px;
				font-size: 13px;
			}
		}
	}

	.bottom-bt-box {
		.hui-button {
			min-width: 90px;
			font-size: 13px;
		}
	}

	.preview-header {
		padding: 10px 12px;

		.preview-title {
			font-size: 13px;
		}
	}

	.preview-content {
		padding: 12px;
	}
}
</style>
