<template>
	<div id="addrestock" class="content">
		<div class="top-content">
			<div class="field" v-if="id !== 'create'">
				<label>单据号</label>
				<span>{{data.code}}</span>
			</div>
			<!-- <div class="field">
				<label>盘点时间</label>
				<span>{{data.created_at}}</span>
			</div> -->
			<div class="field">
				<label>操作人</label>
				<span>{{data.created_by}}</span>
			</div>
			<div class="field">
				<label>配送要求</label>
				<input v-model="data.remark"></input>
			</div>
		</div>
		<div class="pro-btn" v-if="id === 'create'">
			<div class="item brd fs15" @click="choosePro">
				<i class="wemer-add"></i>
				<span>选择商品</span>
			</div>
			<!-- <div class="item fs15" @click="scanCode">
				<img src="/images/wemerchant/<EMAIL>">
				<span>扫码选择</span>
			</div> -->
		</div>
		<div v-else class="sub-title fs15">商品列表</div>
		<div class="pro-head fs14">
			<div>商品名称</div>
			<div>要货价</div>
			<div>要货数量</div>
			<div>商品单位</div>
			<div>小计金额</div>
		</div>
		<div class="pro-table" :class="[id === 'create' ? '' : 'amxinfo']">
			<div class="pro-body">
				<div class="pro-tr fs14" v-for="(row, idx) in config.goods" v-if="config.goods.length">
					<div class="pro-td pro-name" @click="deletePro(row, idx)"><i class="wemer-delete-all" v-if="id  === 'create'"></i>{{row.product_name}}</div>
					<div class="pro-td">{{row.price}}</div>
					<div class="pro-td fs15">
						<input v-if="id === 'create'" type="text" v-model="row.quantity" @input="checkNum(row, 'quantity')">
						<span v-else>{{row.quantity}}</span>
					</div>
					<div class="pro-td">{{row.unit_name}}</div>
					<div class="pro-td">{{row.diff_num}}</div>
				</div>
				<div class="no-datas fs14" v-else>还未添加要货商品</div>
			</div>
		</div>
		<div class="save-btn fs18" @click="save" v-if="id === 'create'">确定要货</div>
		<layer
			:show="layerShow"
			title="提示"
			:leftCallback="layerCancel"
			:rightCallback="layerSave">
			<div class="deleteText fs17">{{layerTitle}}</div>
		</layer>
	</div>
</template>

<script>
/**
 * @desc 速店农贸h5商户 盘点单详情
 * @version 0.0.1
 * <AUTHOR> <<EMAIL>>
 * @date 2019-05-14
 * @copyright 2019
 */
/* jshint esversion: 6 */
/* eslint-disable no-new */
import APIs from '_/APIs';
import { getDate, apiPost } from '_/utils';
import router from '@/router';
import * as URL from '_/URLs';
import layer from '@@/layer';
import store from '../../../vuex';
import { mapActions, mapGetters } from 'vuex';
const getters = mapGetters([ 'userInfo', 'url' ]);
const actions = mapActions([ 'setAlert' ]);
const wx = require('weixin-js-sdk');
export default {
	name: 'addrestock',
	beforeRouteEnter(to, from, next){
		let name = from.name;
		let type = to.params.id;
		next(vm => {
			document.title = type === 'create' ? '新增要货订单' : '要货订单详情';
			if(!name){
				vm.init();
			}
		});
	},
	components: {
		layer,
	},
	computed: {
		...getters
	},
	data(){
		return {
			id: '',
			isIos: false,
			showFilter: false,
			layerShow: false,
			layerTitle: '',
			disable: false, // 防抖
			data: {
				remark: '',
				goods: []
			},
			config: {
				index: '',
				goods: [],
				type: '',
				id: ''
			}
		};
	},
	methods: {
		...actions,
		/**
		 * 设置输入
		 * <AUTHOR> <<EMAIL>>
		 * @date   2019-04-22
		 */
		checkNum(row, name){
			let type = row.weight_type;
			let reg = '';
			if(type + '' === '1'){
				// 计件
				reg = /(?:(0|[1-9]\d{0,6})?)/;
			}
			else{
				reg = /(?:(0|[1-9]\d{0,6})(\.\d{0,3})?)/;
			}
			let result = reg.exec(row.quantity);
			row.quantity = result !== null ? result[0] : '';
			if(row.quantity || row.quantity === 0){
				let d = row.price * row.quantity * 100;
				row.diff_num = Math.round(d) / 100;
			}
		},
		/**
		 * 删除商品
		 * <AUTHOR> <<EMAIL>>
		 * @date   2019-04-22
		 */
		deletePro(row, idx){
			this.layerTitle = '确定删除么';
			this.layerShow = true;
			this.config.index = idx;
			this.config.type = 1;
		},
		/**
		 * 关闭弹框
		 * <AUTHOR> <<EMAIL>>
		 * @date   2019-04-22
		 */
		layerCancel(){
			this.layerShow = false;
			if(this.config.type === 2){
				router.go(-1);
			}
		},
		/**
		 * 保存操作
		 * <AUTHOR> <<EMAIL>>
		 * @date   2019-04-22
		 */
		layerSave(){
			if(this.config.type === 1){
				this.config.goods.splice(this.config.index, 1);
				this.layerShow = false;
			}
			else{
				router.replace(`${URL.PURCHASE_PAY}?id=${this.config.id}&type=1`);
			}
		},
		/**
		 * 跳转商品选择页面
		 * <AUTHOR> <<EMAIL>>
		 * @date   2019-04-22
		 */
		choosePro(){
			store.dispatch('setPros', {goods: this.config.goods, remark: this.data.remark});
			router.push(URL.RESTOCK_PRO);
		},
		/**
		 * 调用微信扫一扫
		 * <AUTHOR> <<EMAIL>>
		 * @date   2019-04-22
		 */
		scanCode(){
			let $this = this;
			wx.scanQRCode({
				needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
				scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
				success: res => {
					// let result = res.resultStr.split(',')[1];
					let result = '';
					if(res.resultStr.split(',')[1]){
						result = res.resultStr.split(',')[1];
					}
					else{
						result = res.resultStr.split(',')[0];
					}
					$this.getPro(result);
				}
			});
		},
		/**
		 * 获取条码对应商品
		 * <AUTHOR> <<EMAIL>>
		 * @date   2019-04-26
		 */
		getPro(code){
			let $this = this;
			apiPost({
				url: APIs.PRODUCT,
				data: {
					action: 'product.detail',
					barcode: code,
				},
				success(res){
					if(res.code + '' === '0'){
						let d = res.data;
						for(let i = 0; i < $this.config.goods.length; i++){
							let el = $this.config.goods[i];
							if(el.id + '' === d.id + ''){
								$this.setAlert({msg: '该商品已经存在列表中', type: false});
								return;
							}
						}
						let obj = {
							id: d.id,
							product_id: d.id,
							price: d.price,
							quantity: d.quantity || 0
						};
						$this.config.goods.push(obj);
					}
					else{ // 失败
						$this.setAlert({msg: res.msg, type: false});
					}
				},
				error(res){
					$this.setAlert({msg: res.msg, type: false});
				}
			});
		},
		/**
		 * 新建盘点单
		 * <AUTHOR> <<EMAIL>>
		 * @date   2019-04-28
		 */
		save(){
			let $this = this;
			if(!this.config.goods.length){
				this.setAlert({msg: '还未添加要货商品', type: false});
				return;
			}
			if(this.disable){
				return false;
			}
			for(let i = 0; i < this.config.goods.length; i++){
				let el = this.config.goods[i];
				if(el.quantity){
					continue;
				}
				this.setAlert({ msg: `请输入第${i + 1}条商品(${el.product_name})的要货数量`, type: false });
				return;
			}
			this.disable = true;
			let arr = [];
			this.config.goods.forEach(el => {
				arr.push({
					product_name: el.product_name,
					product_id: el.id,
					price: el.price,
					quantity: el.quantity
				});
			});
			apiPost({
				url: APIs.RESTOCK,
				data: Object.assign({action: 'add'}, {product_list: arr, remark: this.data.remark || ''}),
				success(res){
					if(res.code + '' === '0'){
						$this.setAlert({msg: res.msg, type: true});
						$this.layerTitle = '立即支付预付款?';
						$this.config.id = res.data.id;
						$this.config.type = 2;
						$this.layerShow = true;
					}
					else{
						$this.setAlert({msg: res.msg, type: false});
					}
					$this.disable = false;
				},
				error(res){
					$this.setAlert({msg: res.msg, type: false});
					$this.disable = false;
				}
			});
		},
		init(){
			this.id = this.$route.params.id;
			if(this.id !== 'create'){
				this.detail();
				this.getGoods();
			}
			else{
				this.data = {
					created_at: getDate(),
					created_by: this.userInfo.name
				};
				this.config.goods = [];
			}
		},
		detail(){
			let $this = this;
			apiPost({
				url: APIs.RESTOCK,
				data: {
					action: 'restock.content.info.get',
					id: this.id
				},
				success(res){
					if(res.code + '' === '0'){ // 成功
						$this.data = res.data || {};
					}
					else{ // 失败
						$this.setAlert({msg: res.msg, type: false});
					}
				},
				error(res){
					$this.setAlert({msg: res.msg, type: false});
				}
			});
		},
		getGoods(){
			let $this = this;
			apiPost({
				url: APIs.INVENTORY,
				data: {
					action: 'products',
					inventory_id: this.id
				},
				success(res){
					if(res.code + '' === '0'){ // 成功
						$this.config.goods = (res.data || []).map(el => {
							el.diff_num = (el.price * el.quantity).toFixed(3);
							return el;
						});
					}
					else{ // 失败
						$this.setAlert({msg: res.msg, type: false});
					}
				},
				error(res){
					$this.setAlert({msg: res.msg, type: false});
				}
			});
		}
	},
	created(){
		this.id = this.$route.params.id;
		let temp = store.getters.pros;
		if(temp.from === 1 || !temp.goods){
			this.init();
		}
		else{
			this.data = {
				created_at: getDate(),
				created_by: this.userInfo.name,
				remark: temp.remark
			};
			this.config.goods = temp.goods;
		}
	},
	// mounted(){
	// 	if(this.id !== 'create'){
	// 		return;
	// 	}
	// 	let $this = this;
	// 	let urls = '';
	// 	if(window.__wxjs_is_wkwebview === true){
	// 		urls = this.url.split('#')[0];
	// 		this.isIos = true;
	// 	}
	// 	else{
	// 		urls = window.location.href.split('#')[0];
	// 		this.isIos = false;
	// 	}
	// 	let scanRequest = new Promise((resolve, reject) => {
	// 		apiPost({
	// 			url: APIs.PRODUCT,
	// 			data: {
	// 				action: 'get.wechat.config',
	// 				url: urls,
	// 				js_api_list: ['scanQRCode'],
	// 			},
	// 			success(res){
	// 				if(res.code + '' === '0'){
	// 					resolve(res);
	// 				}
	// 				else{
	// 					reject(res);
	// 				}
	// 			},
	// 			error(res){
	// 				reject(res);
	// 			},
	// 		});
	// 	});
	// 	scanRequest.then(res => {
	// 		wx.config({
	// 			debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
	// 			appId: res.data.appId, // 必填，公众号的唯一标识
	// 			timestamp: res.data.timestamp, // 必填，生成签名的时间戳
	// 			nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
	// 			signature: res.data.signature, // 必填，签名
	// 			jsApiList: ['scanQRCode'] // 必填，需要使用的JS接口列表
	// 		});
	// 		wx.error(function(res){
	// 			$this.setAlert({msg: res, type: true});
	// 			// config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
	// 		});
	// 	}, res => {
	// 		alert(res);
	// 	});
	// },
};
</script>

<style lang="less">
@import '../../../less/common';
@px2rem: 2rem / 75px;
#addrestock{
	height: 100%;
	// .pos-r();
	.top-content{
		.fields{
			.wh(100%, 44px * @px2rem);
			.bgc(#fff);
			.brd-b(1px, solid, #E5E5E5);
			.pdl(15px * @px2rem);
			.pdr(15px * @px2rem);
			display: flex;
			label{
				width: 70px * @px2rem;
				// flex: 1;
				.dib();
			}
			select{
				width: calc(~"100% - 0rem");
				.ta-rt();
				direction: rtl;
				// flex: 1;
			}
		}
		.field{
			.hlh(44px * @px2rem, 44px * @px2rem);
			height: 44px * @px2rem;
			.bgc(#fff);
			.pos-r();
			.brd-b(1px, solid, #E5E5E5);
			.pdl(15px * @px2rem);
			.pdr(15px * @px2rem);
			.fs(0);
			display: flex;
			> label{
				color: #000000;
				.dib();
				width: 70px * @px2rem;
				&.empty-star:before{
					content: '*';
					.fgc(#fff);
				}
			}
			> input, select{
				// position: absolute;
				// right: 15px * @px2rem;
				// top: 12px * @px2rem;
				.ta-rt();
				flex: 1;
				border-style: none;
				border: none;
			}
			> select{
				// flex: 1;
				width: 100%;
				// height: 19px * @px2rem;
				background-color: rgba(0,0,0,0);
				right: 10px * @px2rem;
				direction: rtl;
				&.unchoosed{
					.fgc(#888888);
				}
				&.choosed{
					.fgc(#000000);
				}
			}
			>span{
				position: absolute;
				right: 15px * @px2rem;
				// top: 0;
				.dib();
			}
			.select-repeat{
				appearance:none;
				-moz-appearance:none;
				-webkit-appearance:none;
				background: url('/images/wemerchant/<EMAIL>') no-repeat scroll left center transparent;
				// padding-right: 14px;
			}
			.label-detail{
				// .dib();
				.fgc(#000);
				width: 100%;
				// height: 34px * @px2rem;;
				.pdr(24px * @px2rem);
				.ta-rt();
				// .mgr(15px * @px2rem);
				// display: flex;
				// flex: 1;
				// align-items: center;
				// .bgc(#242536);
				.pos-r();
			}
			.hide{
				position: absolute;
				left: 15px * @px2rem;
			}
			.filter-btn{
				width: 50%;
				.ta-c();
				line-height: 44px * @px2rem;
			}
			.blue{
				.fgc(#F7B32D);
			}
		}
	}
	.pro-btn{
		display: flex;
		.wh(100%, 44px * @px2rem);
		.mgt(8px * @px2rem);
		.mgb(8px * @px2rem);
		.item{
			flex: 1;
			line-height: 44px * @px2rem;
			.ta-c();
			.bgc(#fff);
			>i{
				.fgc(#888888);
			}
			>span{
				.dib();
				.va-t();
			}
			>img{
				.wh(20px * @px2rem, 22px * @px2rem);
				vertical-align: text-bottom;
			}
		}
		.brd{
			.brd-r(2px, solid, #E5E5E5);
		}
	}
	.sub-title{
		.pd(15px * @px22rem, 15px * @px22rem, 8px * @px22rem, 15px * @px22rem);
		.fgc(#333333);
	}
	.pro-head{
		display: flex;
		align-items: center;
		justify-content: center;
		height: 30px * @px2rem;
		width: 100%;
		.bgc(#fff);
		.brd-b(1px, solid, #E5E5E5);
		>div{
			flex: 1;
			.fgc(#888);
			.ta-c();
		}
	}
	.pro-table{
		.pos-r();
		.fs(0);
		.bgc(#fff);
		max-height: calc(~"100% - 6.18rem");
		overflow-y: auto;
		&.amxinfo{
			max-height: calc(~"100% - 5.5rem");
		}
		.pro-body{
			// .mgt(30px * @px2rem);
			.pro-tr{
				display: flex;
				align-items: center;
				justify-content: center;
				min-height: 44px * @px2rem;
				// .hlh(40px * @px2rem, 40px * @px2rem);
				.brd-b(1px, solid, #E5E5E5);
				.pro-td{
					.pos-r();
					flex: 1;
					.fgc(#525E6E);
					.ta-c();
					.pd(4px * @px2rem);
					.pdl(17px * @px2rem);
					// .pdr(4px * @px2rem);
					>input{
						width: 100%;
						.ta-c();
					}
					&.red{
						color: #F84445;
					}
					&.green{
						color: #12AAC7;
					}
				}
				.pro-name{
					word-wrap: break-word;
					word-break: break-all;
				}
			}
			.all{
				display: flex;
				.hlh(40px * @px2rem, 40px * @px2rem);
				.fgc(#525E6E);
				.pdl(20px * @px2rem);
				.pdr(20px * @px2rem);
				.num{}
				.price{
					flex: 1;
					.ta-rt();
				}
			}
			.wemer-delete-all{
				.fgc(#888);
				position: absolute;
				top: 10px * @px2rem;
				left: 6px * @px2rem;
				line-height: 0;
			}
			.no-datas{
				.hlh(40px * @px2rem, 40px * @px2rem);
				.brd-b(1px, solid, #E5E5E5);
				.ta-c();
			}
		}
	}
	.save-btn{
		position: fixed;
		bottom: 0;
		left: 0;
		.wh(100%, 47px * @px2rem);
		line-height: 47px * @px2rem;
		margin: 0 auto;
		.ta-c();
		.bgc(#FFC438);
		.fgc(#fff);
		.brdr(2px * @px2rem);
	}
	.deleteText{
		.pdt(40px * @px2rem);
		text-align: center;
	}
}

.make-dpr-font(@dpr){
	[data-dpr="@{dpr}"]{
		#addrestock{
			.field{
				> label{
					.fs(15px * @dpr);
				}
				> input{
					.fs(15px * @dpr);
				}
				> span{
					.fs(15px * @dpr);
				}
				> select{
					.fs(15px * @dpr);
				}
			}
		}
	}
}
.make-dpr-font(1);
.make-dpr-font(2);
.make-dpr-font(3);
</style>
