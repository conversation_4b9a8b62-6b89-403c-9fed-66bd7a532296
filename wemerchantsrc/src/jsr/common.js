$(document).ready(function () {
    hui('.add-icon').click(function () {
        hui('.mask').show();
    });
    hui('.mask-cn-close').click(function () {
        hui('.mask').hide();
    });
    hui('.mask-top').click(function () {
        hui('.mask').hide();
    });


    /*底部分页切换*/
    var $index, $pathname = window.location.pathname;
    if ($pathname == '' || $pathname == undefined || $pathname == null) {
        $pathname = '/front';
    }
    if ($pathname.indexOf("/app/car/trade") > -1) {
        $pathname = 'trade';
    }
    if ($pathname.indexOf("/app/car/carlist") > -1) {
        $pathname = 'carlist';
    }
    if ($pathname.indexOf("/app/mine") > -1) {
        $pathname = 'mine';
    }
    if ($pathname.indexOf("/app") > -1) {
        $pathname = 'app';
    }

    console.log($pathname);

    switch ($pathname) {
        case 'app':
            $index = 0;
            break;
        case 'trade':
            $index = 1;
            break;
        case 'carlist':
            $index = 2;
            break;
        case 'mine':
            $index = 3;
            break;
        default:
            $index = 0;
    }
    ;
    $('#hui-footer a').eq($index).addClass("active");
    $('#hui-footer a').eq($index).siblings('a').removeClass("active");

});