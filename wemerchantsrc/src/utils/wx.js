/**
 * 微信相关工具函数
 */
import {apiPost} from '_/utils'
import APIs from '_/APIs'
import store from '../vuex'

/**
 * 扫描VIN码
 * @returns {Promise} 识别结果
 */
export function scanVinCode() {
	return new Promise((resolve, reject) => {
		wx.chooseImage({
			count: 1,
			sizeType: ['original', 'compressed'],
			sourceType: ['camera', 'album'],
			success: async (res) => {
				try {
					const localId = res.localIds[0]
					// 获取图片数据
					wx.getLocalImgData({
						localId,
						success: async (res) => {
							try {
								// 上传图片并识别
								const response = await apiPost({
									url: APIs.GETALLCARD,
									data: {
										action: 'recognizevin',
										imageData: res.localData
									}
								})
								resolve(response)
							} catch (error) {
								reject(error)
							}
						},
						fail: (error) => {
							reject(error)
						}
					})
				} catch (error) {
					reject(error)
				}
			},
			fail: (error) => {
				reject(error)
			}
		})
	})
}

/**
 * 扫描行驶证
 * @returns {Promise} 识别结果
 */
export function scanLicenseImage() {
	return new Promise((resolve, reject) => {
		wx.chooseImage({
			count: 1,
			sizeType: ['original', 'compressed'],
			sourceType: ['camera', 'album'],
			success: async (res) => {
				try {
					const localId = res.localIds[0]
					// 获取图片数据
					wx.uploadImage({
						localId: localId,
						isShowProgressTips: 1, // 默认为1，显示进度提示
						success: function (res) {
							var serverId = res.serverId; // 返回图片的服务器端ID
							apiPost({
								url: APIs.DEAL,
								data: {
									action: 'get.car.ocrresult',
									serverId: serverId
								},success:function(res){
									debugger;
									resolve(res)
								},error:function(err){
									debugger;

									reject(err)
								}
							})
						}
					});
				} catch (error) {
					reject(error)
				}
			},
			fail: (error) => {
				reject(error)
			}
		})
	})
}

/**
 * 选择图片
 * @param {number} count 最多可以选择的图片张数，默认1
 * @returns {Promise<{localIds: string[]}>} 返回选中的图片的本地ID数组
 */
export function chooseImage(count = 1) {
    return new Promise((resolve, reject) => {
        wx.chooseImage({
            count: count, // 默认1
            sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
            sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
            success: function (res) {
                resolve({
                    localIds: res.localIds // 返回选定照片的本地ID列表
                });
            },
            fail: function (err) {
                console.error('选择图片失败:', err);
                reject(new Error('选择图片失败'));
            }
        });
    });
}

/**
 * 上传图片到微信服务器
 * @param {string} localId 需要上传的图片的本地ID
 * @returns {Promise<{serverId: string}>} 返回图片的服务器端ID
 */
export function uploadImage(localId) {
    return new Promise((resolve, reject) => {
        wx.uploadImage({
            localId: localId,
            isShowProgressTips: 1, // 默认为1，显示进度提示
            success: function (res) {
                resolve({
                    serverId: res.serverId // 返回图片的服务器端ID
                });
            },
            fail: function (err) {
                console.error('上传图片失败:', err);
                reject(new Error('上传图片失败'));
            }
        });
    });
}

// /**
//  * 初始化微信配置
//  * @returns {Promise}
//  */
// export function initWxConfig() {
//   return new Promise((resolve, reject) => {
//     apiPost({
//       url: APIs.GETALLCARD,
//       data: {
//         action: 'getwxconfig',
//         url: window.location.href.split('#')[0]
//       }
//     })
//       .then(res => {
//         wx.config({
//           debug: false,
//           appId: res.appId,
//           timestamp: res.timestamp,
//           nonceStr: res.nonceStr,
//           signature: res.signature,
//           jsApiList: [
//             'chooseImage',
//             'getLocalImgData',
//             'previewImage',
//             'uploadImage'
//           ]
//         })
//         wx.ready(() => {
//           resolve()
//         })
//         wx.error((error) => {
//           reject(error)
//         })
//       })
//       .catch(error => {
//         reject(error)
//       })
//   })
// }

export default {
	name: 'wx',
	// 判断是否是微信
	isWechat() {
		const ua = window.navigator.userAgent.toLowerCase()
		const matchStr = ua.match(/micromessenger/i)
		if (matchStr && matchStr.includes('micromessenger')) return true
		return false
	},
	/* 初始化wxjsdk各种接口 */
	init() {
		let apiList = [
			'chooseImage',
			'getLocalImgData',
			'previewImage',
			'uploadImage'
		]
		//需要使用的api列表
		return new Promise((resolve, reject) => {
			getSignature(store.state.initLink).then(res => {
				console.log("签名信息")
				console.log(res)
				debugger;
				if (res.appId) {
					wx.config({
						debug: true, // 开启调试模式,调用的所有 api 的返回值会在客户端 alert 出来，若要查看传入的参数，可以在 pc 端打开，参数信息会通过 log 打出，仅在 pc 端时才会打印。
						appId: res.appId,
						timestamp: res.timestamp,
						nonceStr: res.nonceStr,
						signature: res.signature,
						jsApiList: apiList
					})
					wx.ready(res => {
						// 微信SDK准备就绪后执行的回调。
						resolve(wx, res)
					})
				} else {
					console.log("失败了")
					reject(res)
				}
			}).catch(err => {
				console.log("失败了1")
				reject(err)
			})
		})
	}
}

function getSignature(url) {
	return new Promise((resolve, reject) => {
		apiPost({
			url: APIs.DEAL,
			data: {
				action: 'get.wx.config',
				url: url
			}, success(res) {
				resolve(res)
			}, error(err) {
				reject(err)
			}
		})
	})
}
