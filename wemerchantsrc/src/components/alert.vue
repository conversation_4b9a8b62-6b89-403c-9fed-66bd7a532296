<template>
	<div class="alert" v-if="alert.msg !== ''">
		<div class="left">
			<div
				class="icon"
				:class="{ error: !alert.type, success: alert.type }"
			></div>
		</div>
		<div class="right">
			<span>{{ alert.msg }}</span>
		</div>
	</div>
</template>

<script>
/**
 * @module alert
 * @desc 速店农贸 提示
 * @version 0.0.1
 * <AUTHOR> <<EMAIL>>
 * @date 2017-12-20
 * @copyright 2017
 */
import { mapGetters, mapActions } from "vuex";
const getters = mapGetters(["alert"]);
const actions = mapActions(["setAlert"]);
export default {
	name: "alert",
	computed: {
		...getters
	},
	methods: {
		...actions
	},
	watch: {
		"alert.msg": function(newValue, oldValue) {
			console.log(newValue);
			if (newValue !== "") {
				setTimeout(() => {
					this.setAlert({ msg: "", type: false });
				}, 3000);
			}
		}
	}
};
</script>

<style lang="less">
@import "~less/common.less";
@px2rem: 1rem / 75px;
.alert {
	position: fixed;
	top: 360px * @px2rem;
	width: 350px * @px2rem;
	left: 50%;
	.mgl(-175px * @px2rem);
	.bgc(#020403);
	.brdr(4px * @px2rem);
	opacity: 0.8;
	.pd(20px * @px2rem);
	display: inline-flex;
	.left {
		flex: 1;
	}
	.right {
		flex: 5;
		color: #ffffff;
	}
	.icon {
		.wh(40px * @px2rem, 40px * @px2rem);
		background-size: 100%;
		&.error {
			background-image: url("/images/wemerchant/<EMAIL>");
		}
		&.success {
			background-image: url("/images/wemerchant/<EMAIL>");
		}
	}
}
.make-dpr-font(@dpr) {
	[data-dpr="@{dpr}"] {
		.alert {
			.fs(14px * @dpr);
		}
	}
}
.make-dpr-font(1);
.make-dpr-font(2);
.make-dpr-font(3);
</style>
