<template>
	<transition name="mult">
		<div class="multi-select" v-if="show">
			<div class="line" :class="{'have-child': secondLineData.length !== 0 || showBgc}">
				<div class="item" v-for="(row, index) in firstLineData" :class="{'active': row.active}" @click="getSecondData(row, index)">{{row.name}}</div>
			</div>
			<div class="line" :class="{'have-child': thirdLineData.length !== 0}" v-if="!justOne">
				<div class="item" v-for="(row, index) in secondLineData" :class="{'active': row.active}" @click="getThirdData(row, index)">{{row.name}}</div>
			</div>
			<div class="line" v-if="!justOne">
				<div class="item" v-for="row in thirdLineData" :class="{'active': row.active}" @click="sureLastId(row)">{{row.name}}</div>
			</div>
		</div>
	</transition>
</template>

<script>
import { apiPost } from '_/utils';
// import APIs from '_/APIs';
export default {
	name: 'multiSelect',
	props: {
		/**
		 * 是否展示筛选面板
		 * @type {Boolean}
		 */
		show: {
			type: Boolean,
			default: false,
		},
		/**
		 * 筛选的地址
		 * @type {String}
		 */
		searchUrl: {
			type: String,
			default: '',
		},
		/**
		 * 筛选的参数
		 * @type {Object}
		 */
		searchParam: {
			type: Object,
			default: function(){
				return {};
			},
		},
		/**
		 * 筛选的父级id的名称
		 * @type {String}
		 */
		searchPidParamName: {
			type: String,
			default: 'pid',
		},
		filterIdParamName: {
			type: String,
			default: 'id',
		},
		filterNameParamName: {
			type: String,
			default: 'name',
		},
		callback: {},
		/**
		 * 所有数据
		 * @type Array
		 */
		dataList: {
			type: Array,
			default: function(){
				return [];
			},
		},
		returnObj: {
			type: Boolean,
			dafault: false,
		},
		justOne: {
			type: Boolean,
			dafault: false,
		}, // 只显示第一列
		showBgc: {
			type: Boolean,
			dafault: false,
		}, // 显示第一列背景色
	},
	data(){
		return {
			fullData: [], // 全部数据
			firstLineData: [], // 第一列
			secondLineData: [], // 第二列
			thirdLineData: [], // 第三列
		};
	},
	watch: {
		dataList(newValue, oldValue){
			this.fullData = this.dataList;
			this.firstLineData = this.dataList;
		},
	},
	mounted(){
		this.fullData = this.dataList;
		// console.log(this.fullData);
		this.firstLineData = this.dataList;
	},
	methods: {
		sureLastId(row){
			if(!this.returnObj){
				this.callback(row.id);
			}
			else{
				this.callback(row);
			}
			this.$emit('close');
			this.secondLineData = [];
			this.thirdLineData = [];
			this.firstLineData.forEach(el => {
				el.active = false;
			});
		},
		/**
		 * 点击第一列的数据
		 * <AUTHOR> <<EMAIL>>
		 * @date   2017-12-20
		 * @param  {Object}   row    选中对象
		 * @param  {Number}   findex 选中的索引
		 */
		getSecondData(row, findex){
			this.firstLineData.forEach((el, index) => {
				if(index === findex){
					el.active = true;
				}
				else{
					el.active = false;
				}
			});
			let cdata = this.fullData[findex].children;
			if(cdata.length === 0){
				this.sureLastId(row);
			}
			else{
				this.secondLineData = this.fullData[findex].children;
			}
		},
		/**
		 * 点击第二列的数据
		 * <AUTHOR> <<EMAIL>>
		 * @date   2017-12-20
		 * @param  {Object}   row    选中对象
		 * @param  {Number}   sindex 选中的索引
		 */
		getThirdData(row, sindex){
			this.secondLineData.forEach((el, index) => {
				if(index === sindex){
					el.active = true;
				}
				else{
					el.active = false;
				}
			});
			let findex = this.firstLineData.findIndex(el => {
				return el.active;
			});
			let cdata = this.secondLineData[sindex].children;
			if(cdata.length === 0){
				this.sureLastId(row);
			}
			else{
				this.thirdLineData = this.fullData[findex].children[sindex].children;
			}
		},
		/**
		 * 根据需要的字段重置数组
		 * <AUTHOR> <<EMAIL>>
		 * @date   2017-12-20
		 * @param  {Array}   list 需要重置的数字
		 */
		resetData(list){
			// if(Array.isArray(list)){
			// 	return [];
			// }
			for(let i = 0; i < list.length; ++i){
				list[i].active = false;
				if(this.filterIdParamName !== 'id' || this.filterNameParamName !== 'name'){
					list[i].id = list[i][this.filterIdParamName];
					list[i].name = list[i][this.filterNameParamName];
				}
			}
			return list;
		},
		/**
		 * 设置数据
		 * <AUTHOR> <<EMAIL>>
		 * @date   2017-12-20
		 * @param  {Number}   pid 父级id
		 * @param {Number} findex 第一列选中的数据的index
		 * @param {Number} sindex 第二列选中的数据的index
		 */
		setData(pid, findex, sindex){
			let $this = this;
			let searchParam = this.searchParam ? this.searchParam : {};
			searchParam[this.searchPidParamName] = pid;
			apiPost({
				url: this.searchUrl,
				data: searchParam,
				success(res){
					if(res.code === '0'){
						if(Array.isArray(res.data)){
							let tmpList = $this.resetData(res.data);
							if(typeof (findex) === 'undefined' && typeof (sindex) === 'undefined'){
								$this.fullData = tmpList;
								$this.firstLineData = tmpList;
							}
							else if(typeof (findex) !== 'undefined' && typeof (sindex) === 'undefined'){
								$this.fullData[findex].children = tmpList;
								$this.secondLineData = tmpList;
							}
							else{
								console.log('第三个');
								console.log(tmpList);
								if(tmpList.length === 0){
									$this.sureLastId(pid);
								}
								else{
									$this.fullData[findex].children[sindex].children = tmpList;
									$this.thirdLineData = tmpList;
								}
							}
						}
					}
				},
				error(res){},
			});
		},
	},
};
</script>

<style lang="less">
@import '~less/common.less';
.multi-select{
	width: 100%;
	height: calc(~"100% - 1.173rem");
	.bgc(#FFFFFF);
	position: absolute;
	bottom: 0;
	display: inline-flex;
	.line{
		overflow-y: auto;
		flex: 1;
		.dib();
		&.have-child{
			.bgc(#EFEFF4);
		}
		.item{
			.pdl(30px * @px2rem);
			.pdt(28px * @px2rem);
			.pdr(28px * @px2rem);
			.pdb(28px * @px2rem);
			color: #2A2A2A;
			&.active{
				.bgc(#FFFFFF);
			}
		}
	}
}
.mult-enter-active, .mult-leave-active{
	transition: all .3s;
}
.mult-enter{
	opacity: 0;
	transform: translate3d(0, -10%, 0);
}
.mult-leave-to{
	opacity: 0;
	transform: translate3d(0, -10%, 0);
}
.make-dpr-font(@dpr){
	[data-dpr="@{dpr}"]{
		.multi-select{
			.fs(15px * @dpr);
		}
	}
}
.make-dpr-font(1);
.make-dpr-font(2);
.make-dpr-font(3);
</style>