<template>
<div class="time-scroll" :class="[isHour ? 'hour' : '']">
	<div class="scroll" :ref="name" @scroll.stop="timeScroll">
		<ul class="select-list">
			<li class="item fs15"
				:class="{'active': item === val}"
				v-for="(item, index) in list"
				@click="setTime($event, index)"
				>{{item}}{{extra}}
			</li>
		</ul>
	</div>
</div>
</template>

<script>
/**
 * @module components/datescroll
 * @desc 单列选择scroll
 * @version 0.0.1
 * <AUTHOR> <<EMAIL>>
 * @date   2018-08-13
 */
const cWidth = document.documentElement.clientWidth;
const cPercent = parseInt(375 / cWidth * 30);
export default {
	name: 'timescroll',
	data(){
		return {
			val: '',
			debounceTimer: null,
			isInit: false,
			height: cPercent,
		};
	},
	props: {
		isHour: {
			type: [ Boolean ],
			defalut: true,
		},
		name: {
			type: [ String ],
			defalut: 'startHour',
		},
		list: {
			type: [ Array ],
			defalut: () => [],
		},
		value: {
			type: [ String, Number ],
			defalut: '00:00',
		},
		isShow: {
			type: [ Boolean ],
			defalut: false,
		},
		extra: {
			type: [ String, Number ],
			defalut: '年',
		}
	},
	watch: {
		isShow(val){
			if(val){
				this.initScroll();
			}
		},
	},
	methods: {
		/**
		 * 当现实选择浮层时初始化滚动条位置
		 * <AUTHOR> <<EMAIL>>
		 * @date   2018-08-13
		 */
		initScroll(){
			let index = this.list.findIndex(el => el === this.value);
			this.$nextTick(() => {
				this.$refs[this.name].scrollTop = index * cPercent;
			});
			this.val = this.value;
		},

		setTime(event, index){
			this.val = this.list[index];
			event.target.parentElement.parentElement.scrollTop = index * cPercent;
			this.emit();
		},
		/**
		 * [列表滚动事件处理]
		 * <AUTHOR> <<EMAIL>>
		 * @date   2018-08-13
		 * @param  {Object}   event [事件对象]
		 */
		timeScroll(event){
			let st = event.target.scrollTop;
			let step = (st - st % cPercent) / cPercent;
			if(this.debounceTimer !== null){
				clearTimeout(this.debounceTimer);
			}

			this.val = this.list[step];
			this.debounceTimer = setTimeout(() => {
				event.target.scrollTop = cPercent * step;
				this.debounceTimer = null;
			}, 200);

			this.emit();
		},
		/**
		 * [发送数据给父组件]
		 * <AUTHOR> <<EMAIL>>
		 * @date   2018-08-13
		 */
		emit(){
			this.$emit('time-change', this.val, this.name);
		},
	},
	mounted(){
		this.$nextTick(() => {
			this.initScroll();
		});
	},
};
</script>

<style lang="less" scoped>
@import (reference) '~less/common.less';
.time-scroll{
	width: 50%;
	.pos-r();
	.dib();
	overflow: hidden;
	max-height: 210px * @px22rem;
	&:before{
		content: '';
		.pos-a(100; 90px * @px22rem);
		.db();
		.wh(100%, 30px * @px22rem);
		.brd-t(@borderColor:#E5E5E5);
		.brd-b(@borderColor:#E5E5E5);
	}
	.scroll{
		max-height: inherit;
		overflow-y: scroll;
	}
	.select-list{
		.dib();
		height: auto;
		.mg(0);
		.pd(0);
		.va-t();
		.fs(0);
		list-style: none;
		line-height: 30px * @px22rem;
		&:before, &:after{
			content: '';
			.db();
			.wh(100%, 90px * @px22rem);
		}
		>.item{
			list-style: none;
			.hlh(30px * @px22rem, 30px * @px22rem);
			.ta-c();
			&.active{
				.fgc(#000);
			}
		}
	}
}
</style>
