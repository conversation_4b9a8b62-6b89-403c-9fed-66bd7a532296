<template>
	<div class="scroll-view" v-bind:class="{
		scrollbottom: lowerY<0,
		scrolltop: scroll_top>upperThreshold,
		nodata:isover && waitBottom,
		waittop:waitTop,
		waitbottom:waitBottom && needScroll }">
		<div v-bind:class="{ scrollerh: scrollX }">
			<slot></slot>
		</div>
	</div>
</template>

<script>
import IScroll from 'iscroll';
import {mapActions} from 'vuex';
// TODO 手势抖动 需要优化 http://pnc.co.il/dev/iscroll-5-pull-to-refresh-and-infinite-demo.html
/*
 * 使用注意事项：在6p设备 不是6，使用big wrapper为fixed，flex， 列表为flex：1的div里 会失效，无法滚动。
 */
export default {
	name: 'scrollView',
	data(){
		return {
			lowerY: 1,
			scroll_top: 0,
			vScroll: null,
			waitBottom: false,
			waitTop: false,
			threshold: false,
			needScroll: false,
		};
	},
	props: {
		/**
		 * 是否是横向滚动
		 * @type {Boolean}
		 */
		scrollX: {
			type: Boolean,
			default: false
		},
		/**
		 * 是否是竖向滚动
		 * @type {Boolean}
		 */
		scrollY: {
			type: Boolean,
			default: true
		},
		/**
		 * 滚动到顶部距离多少
		 * @type {Number}
		 */
		scrollTop: {
			type: Number,
			default: 0
		},
		/**
		 * 自定义滚动到顶部距离多少才开始刷新
		 * @type {Number}
		 */
		upperThreshold: {
			type: Number,
			default: 100
		},
		/**
		 * 自定义滚动到底部距离多少才开始加载更多
		 * @type {Number}
		 */
		lowerThreshold: {
			type: Number,
			default: 100 // TODO 此prop待结合flexible调整
		},
		/**
		 * 数据是否加载完毕
		 * @type {Boolean}
		 */
		isover: {
			type: Boolean,
			default: false
		},
		/**
		 * 正在等待数据
		 * @type {Boolean}
		 */
		wait: {
			type: Boolean,
			default: false
		},
		/**
		 * 是否要滚动到顶部
		 * @type {Boolean}
		 */
		toTop: {
			type: Boolean,
			default: false
		},
		/**
		 * 是否要开启下拉刷新
		 * @type {Boolean}
		 */
		canUpload: {
			type: Boolean,
			default: false
		},
		/**
		 * 是否有加载更多
		 * @type {Boolean}
		 */
		more: {
			type: Boolean,
			default: false
		},
		/**
		 * 规定当前滚动的特定id
		 * @type {String}
		 */
		scrollId: {
			default: '.scroll-view'
		}
	},
	watch: {
		wait(val, oldVal){
			console.log(val);
			if(!val){
				this.waitBottom = this.waitTop = false;
			}
		},
		toTop(val, oldVal){
			if(val){
				this.vScroll.scrollTo(0, 0);
			}
		},
	},
	computed: {
		dpr(){
			let dpr = document.getElementsByTagName('html')[0].getAttribute('data-dpr');
			return isNaN(dpr) ? 1 : dpr;
		}
	},
	mounted(){
		let that = this;
		this.scroll_top = this.scrollTop;
		// 创建滚动
		this.vScroll = new IScroll(this.scrollId, {
			scrollX: that.scrollX,
			scrollY: that.scrollY,
			probeType: 3,
			mouseWheel: true,
			disableTouch: false,
			disableMouse: true,
			disablePointer: true,
			eventPassthrough: that.scrollX,
			click: true,
			tap: true
		});
		// 滚动监听
		this.vScroll.on('scrollStart', function(){
			that.vbindscrollstart(this);
		});
		// 滚动监听
		this.vScroll.on('scrollEnd', function(){
			that.vbindscrollend(this);
			that.vbindscroll(this);
		});
		if(this.more){
			// 滚动监听
			this.vScroll.on('scroll', function(){
				that.vbindscroll(this);
			});
		}
		if(window.document){
			document.querySelector(this.scrollId).addEventListener('DOMNodeInserted', that.refresh, false);
			document.querySelector(this.scrollId).addEventListener('DOMNodeRemoved', that.refresh, false);
		}
	},
	methods: {
		...mapActions(['setAlert']),
		/**
		 * 抛出开始滚动方法
		 * <AUTHOR> <<EMAIL>>
		 * @date   2017-12-20
		 * @param  {Object}   evt
		 */
		vbindscrollstart: function(evt){
			this.$emit('bindscrollstart', evt);
		},
		/**
		 * 抛出结束滚动方法
		 * <AUTHOR> <<EMAIL>>
		 * @date   2017-12-20
		 * @param  {Object}   evt
		 */
		vbindscrollend: function(evt){
			this.threshold = false;
			this.$emit('bindscrollend', evt);
			this.refresh();
		},
		/**
		 * 抛出滚动到顶部的方法
		 * <AUTHOR> <<EMAIL>>
		 * @date   2017-12-20
		 * @param  {Object}   evt
		 */
		vbindscrolltoupper: function(evt){
			this.$emit('bindscrolltoupper', evt);
		},
		/**
		 * 抛出滚动到底部的方法
		 * <AUTHOR> <<EMAIL>>
		 * @date   2017-12-20
		 * @param  {Object}   evt
		 */
		vbindscrolltolower: function(evt){
			this.$emit('bindscrolltolower', evt);
		},
		/**
		 * 监听滚动的方法
		 * <AUTHOR> <<EMAIL>>
		 * @date   2017-12-20
		 * @param  {Object}   evt
		 */
		vbindscroll: function(evt){
			console.log(evt);
			this.scroll_top = parseInt(evt.y);
			this.lowerY = parseInt(evt.y - evt.maxScrollY);
			// console.log('scroll_top:' + this.scroll_top + ',lowerY:' + this.lowerY + ',lowerThreshold:' + this.lowerThreshold + ',threshold:' + this.threshold + ',waitBottom:' + this.waitBottom + ',waitTop:' + this.waitTop);

			if(this.scroll_top === 0){
				console.log('top');
				this.waitTop = false;
				this.needScroll = false;
			}
			else{
				if(evt.maxScrollY !== 0){
					if(this.lowerY === 0){
						console.log('bottom');
						this.waitBottom = false;
						this.needScroll = true;
					}
					else if(this.lowerY > 0){
						console.log('scrolling');
						this.waitBottom = true;
						this.needScroll = false;
					}
				}
				else{
					console.log('不需要滚');
					this.needScroll = false;
				}
			}
			// if(this.lowerY === 0 && evt.maxScrollY !== 0){
			// 	console.log('bottom');
			// 	this.waitBottom = false;
			// 	this.needScroll = true;
			// }
			// else if(evt.maxScrollY !== 0){
			// 	this.needScroll = true;
			// 	this.waitBottom = true;
			// }
			// else{
			// 	this.waitBottom = true;
			// 	this.needScroll = false;
			// }
			// let isUpper = this.lowerY + this.lowerThreshold; // 必定大于0
			if(this.needScroll && !this.threshold && !this.waitBottom){
				console.log('to-bottom');
				this.waitBottom = true;
				this.threshold = true;
				this.vbindscrolltolower(this);
			}
			else if(!this.threshold && !this.waitTop && this.canUpload){
				console.log('to-top');
				this.waitTop = true;
				this.threshold = true;
				this.vbindscrolltoupper(this);
			}
			this.$emit('bindscroll', evt);
		},
		/**
		 * 刷新
		 * <AUTHOR> <<EMAIL>>
		 * @date   2017-12-20
		 * @param  {Object}   evt
		 */
		refresh: function(evt){
			setTimeout(() => {
				this.vScroll.refresh();
			}, 1);
		}
	}
};
</script>

<style lang="less">
@import '~less/common.less';
/*scroll修改样式*/
.scroll-view:after,.scroll-view:before{
	content: " ";
	width: 8px * @px22rem;
	height: 8px * @px22rem;
	border-radius: 50%;
	// animation: typing 1s linear infinite alternate;
	// -webkit-animation: typing 1s linear infinite alternate;
	position: absolute;
	left: 50%;
	opacity:0;
	transition: all .6s;
	text-align: center;
	font-size: 12px * @px22rem;
	margin-left: -16px * @px22rem;
}
.scroll-view:after{
	bottom: 5px * @px22rem;
}
.scroll-view:before{
	top: 15px * @px22rem;
}
@-webkit-keyframes typing{
	0%{
		background-color: rgba(0,0,0, 1);
		box-shadow: 12px 0px 0px 0px rgba(0,0,0,0.4),24px 0px 0px 0px rgba(0,0,0,0.6);
	}
	25%{
		background-color: rgba(0,0,0, 0.6);
		box-shadow: 12px 0px 0px 0px rgba(0,0,0,0.6),24px 0px 0px 0px rgba(0,0,0,0.4);
	}
	75%{
		background-color: rgba(0,0,0, 0.2);
		box-shadow: 12px 0px 0px 0px rgba(0,0,0,0.8),24px 0px 0px 0px rgba(0,0,0,0.1);
	}
	100%{
		background-color: rgba(0,0,0, 1);
		box-shadow: 12px 0px 0px 0px rgba(0,0,0,0.4),24px 0px 0px 0px rgba(0,0,0,0.6);
	}
}
@keyframes typing{
	0%{
		background-color: rgba(0,0,0, 1);
		box-shadow: 12px 0px 0px 0px rgba(0,0,0,0.4),24px 0px 0px 0px rgba(0,0,0,0.6);
	}
	25%{
		background-color: rgba(0,0,0, 0.6);
		box-shadow: 12px 0px 0px 0px rgba(0,0,0,0.6),24px 0px 0px 0px rgba(0,0,0,0.4);
	}
	75%{
		background-color: rgba(0,0,0, 0.2);
		box-shadow: 12px 0px 0px 0px rgba(0,0,0,0.8),24px 0px 0px 0px rgba(0,0,0,0.1);
	}
	100%{
		background-color: rgba(0,0,0, 1);
		box-shadow: 12px 0px 0px 0px rgba(0,0,0,0.4),24px 0px 0px 0px rgba(0,0,0,0.6);
	}
}
.scroll-view.scrollbottom:after,.scroll-view.scrolltop:before{
	opacity:0.5;
}
.scroll-view.nodata:after{
	content: "没有内容了";
	background-image: none;
	animation:none;
	-webkit-animation:none;
	line-height: 20px * @px22rem;
	bottom: 20px * @px22rem;
	width: 80%;
	margin: auto;
	left: 0;
	right: 0;
	font-size: 15px * @px22rem;
}

.scroll-view.waitbottom>div{
}
.scroll-view.waittop>div{
	padding-top: 30px * @px22rem;
}
.scroll-view.waitbottom:after,.scroll-view.waittop:before{
	opacity:0.5;
}
.scrollerh{
	display: inline-flex;
}
.hoverAct:active {
	background-color: #ECECEC;
}
</style>
