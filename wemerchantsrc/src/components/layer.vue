<template>
	<div class="layer" v-if="show">
		<!-- <div class="layer-mask"></div> -->
		<div class="layer-body l-body" :style="{'margin-top': (-height / 75) + 'rem', 'height': (height / 37.5) + 'rem'}">
			<div class="l-title" v-if="showTitle"><span>{{title}}</span> <i class="wemer-close fs18" @click="closeLayer" v-if="showClose"></i></div>
			<div class="l-content">
				<slot></slot>
			</div>
			<div class="l-opes">
				<a @click="leftCallback">{{leftBtnName}}</a>
				<a @click="rightCallback">{{rightBtnName}}</a>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'layer',
	props: {
		height: {
			type: Number,
			default: 210,
		},
		show: {
			type: Boolean,
			default: false,
		},
		showTitle: {
			type: Boolean,
			default: true,
		},
		title: {
			type: String,
			default: '标题',
		},
		leftBtnName: {
			type: String,
			default: '取消',
		},
		rightBtnName: {
			type: String,
			default: '确认',
		},
		leftCallback: {
			type: Function,
			default(){},
		},
		rightCallback: {
			type: Function,
			default(){},
		},
		closeLayer: {
			type: Function,
			default(){},
		},
		showClose: {
			type: Boolean,
			default: false,
		}
	},
};
</script>

<style lang="less">
@import '~less/common.less';
.layer{
	position: fixed;
	top: 0;
	left: 0;
	.wh(100%, 100%);
	.l-title{
		height: 48px * @px22rem;
		line-height: 48px * @px22rem;
		width: 100%;
		.pdl(15px * @px22rem);
		.pdr(15px * @px22rem);
		color: #000000;
		border-bottom: 1px * @px22rem solid #E5E5E5;
		.wemer-close{
			float: right;
		}
		span{
			.dib();
			max-width: calc(~"100% - 0.75rem");
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
	.l-body{
		.bgc(#FFFFFF);
		position: absolute;
		left: 50%;
		top: 50%;
		width: 280px * @px22rem;
		.mgl(-140px * @px22rem);
		.brdr(4px * @px22rem);
	}
	.l-opes{
		.wh(100%, 100px * @px2rem);
		position: absolute;
		bottom: 0;
		line-height: 100px * @px2rem;
		display: inline-flex;
		border-top: 1px * @px2rem solid #D2D3D5;
		a{
			.ta-c();
			flex: 1;
			text-decoration: none;
			&:first-child{
				color: #000000;
				border-right: 1px * @px2rem solid #D2D3D5;
			}
			&:last-child{
				color: #F7B32D;
			}
		}
	}
}
.make-dpr-font(@dpr){
	[data-dpr="@{dpr}"]{
		.layer{
			.l-title{
				.fs(17px * @dpr);
			}
			.l-opes{
				.fs(18px * @dpr);
			}
		}
	}
}
.make-dpr-font(1);
.make-dpr-font(2);
.make-dpr-font(3);
</style>
