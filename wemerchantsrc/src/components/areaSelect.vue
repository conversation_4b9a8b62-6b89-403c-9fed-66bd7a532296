<template>
	<div id="areaSelect" class="is-comp">
		<input type="text" class="inp-select field-input fs14" :name="name" :placeholder="placeholder" readonly @click.stop="showOptions" :value="showData">
		<i class="area-icon wemer-next fs18" @click.stop="showOptions" v-if="showData === ''"></i>
		<i class="area-icon wemer-delete-all fs18" @click.stop="reset" v-if="showData !== ''"></i>
		<div class="area-select-layer" v-if="provinceShow" @click="blur">
			<div class="area-select-options" @click.stop>
				<div class="fs14" v-if="provinceShow">
					<ul>
						<li v-for="(province, index) in provinceData"
							@click.stop="showCity(province.id, index)"
							:class="{'active': checkedData.provinceId === province.id}">
							<span>{{province.name}}</span>
						</li>
					</ul>
				</div>
				<div class="fs14" v-if="cityShow">
					<ul>
						<li v-for="(city, index) in cityData"
							@click.stop="showArea(city.id, index)"
							:class="{'active': checkedData.cityId === city.id}">
							<span>{{city.name}}</span>
						</li>
					</ul>
				</div>
				<div class="fs14" v-if="areaShow">
					<ul>
						<li v-for="(area, index) in areaData"
							@click.stop="clickArea(area.id, index)"
							:class="{'active': checkedData.areaId === area.id}">
							<span>{{area.name}}</span>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
/**
 * @module components/areaSelect
 * @desc 农贸 省市区下拉选择
 * @version 0.0.1
 * <AUTHOR> <<EMAIL>>
 * @date   2018-06-26
 * @copyright 2018
 */
import { apiPost } from '_/utils';
import APIs from '_/APIs';
export default {
	name: 'areaSelect',
	props: {
		name: {
			type: [String, Number],
			default: 'district',
		},
		initData: {
			type: [ Object, Function, null ],
			default: () => {},
		},
		level: {
			type: Number,
			default: 3,
		},
		position: {
			type: String,
			default: 'bottom'
		},
		placeholder: {
			type: String,
			default: '请选择'
		}
	},
	data(){
		return {
			provinceShow: false,
			cityShow: false,
			areaShow: false,
			checkedData: {
				provinceId: '',
				provinceName: '',
				cityId: '',
				cityName: '',
				areaId: '',
				areaName: '',
			},
			provinceData: [],
			cityData: [],
			areaData: [],
			showData: '',
		};
	},
	watch: {
		initData(nv, ov){
			this.checkedData = nv;
			this.sum();
		}
	},
	methods: {
		sum(){
			let province = '';
			if(this.checkedData.provinceName){
				province = this.checkedData.provinceName;
			}
			let city = '';
			if(this.checkedData.cityId !== 0 && this.checkedData.cityName){
				city = ' / ' + this.checkedData.cityName;
			}
			let area = '';
			if(this.checkedData.areaName){
				area = ' / ' + this.checkedData.areaName;
			}
			this.showData = province + city + area;
		},
		windowClick(event){
			this.provinceShow = false;
			this.cityShow = false;
			this.areaShow = false;
		},
		reset(){
			this.checkedData = {
				provinceId: '',
				provinceName: '',
				cityId: '',
				cityName: '',
				areaId: '',
				areaName: '',
			};
			this.sum();
			this.emitData();
			this.provinceShow = false;
			this.cityShow = false;
			this.areaShow = false;
		},
		/**
		 * 关闭选择地区面板
		 * <AUTHOR> <<EMAIL>>
		 * @date   2018-06-26
		 */
		closeOptions(){
			this.provinceShow = false;
			this.cityShow = false;
			this.areaShow = false;
			this.emitData();
		},
		emitData(){
			this.$emit('get-city', this.checkedData, this.name);
		},
		blur(){
			this.provinceShow = false;
			this.cityShow = false;
			this.areaShow = false;
		},
		/**
		 * 开启选择地区面板
		 * <AUTHOR> <<EMAIL>>
		 * @date   2018-06-26
		 */
		showOptions(){
			let $this = this;
			if(this.provinceShow || this.cityShow || this.areaShow){
				this.closeOptions();
			}
			else{
				let getProvinceData = new Promise(function(resolve, reject){
					apiPost({
						url: APIs.TRACE,
						data: {
							action: 'regions.list',
						},
						success(res){
							resolve(res);
						},
						error(res){
							resolve([]);
						}
					});
				});
				let getCityData = new Promise(function(resolve, reject){
					if($this.checkedData.provinceId){
						apiPost({
							url: APIs.TRACE,
							data: {
								action: 'regions.list',
								id: $this.checkedData.provinceId
							},
							success(res){
								resolve(res);
							},
							error(res){
								resolve([]);
							}
						});
					}
					else{
						resolve([]);
					}
				});
				let getAreaData = new Promise(function(resolve, reject){
					if($this.checkedData.cityId){
						apiPost({
							url: APIs.TRACE,
							data: {
								action: 'regions.list',
								id: $this.checkedData.cityId
							},
							success(res){
								resolve(res);
							},
							error(res){
								resolve([]);
							}
						});
					}
					else{
						resolve([]);
					}
				});
				Promise.all([ getProvinceData, getCityData, getAreaData ]).then(values => {
					$this.provinceData = values[0].data;
					$this.cityData = values[1].data;
					$this.areaData = values[2].data;
					$this.provinceShow = true;
					if($this.initData.cityId){
						$this.cityShow = true;
					}
					if($this.initData.areaId){
						$this.areaShow = true;
					}
				})
				.catch(values => {});
			}
		},
		/**
		 * 展开市
		 * <AUTHOR> <<EMAIL>>
		 * @date   2018-06-26
		 * @param  {int}   provinceId    省份id
		 * @param  {int}   provinceIndex 省份的索引
		 */
		showCity(provinceId, provinceIndex){
			let checkProvince = this.provinceData[provinceIndex];
			this.checkedData.provinceId = checkProvince.id;
			this.checkedData.provinceName = checkProvince.name;
			this.areaShow = false;
			this.checkedData.cityId = '';
			this.checkedData.cityName = '';
			this.checkedData.areaId = '';
			this.checkedData.areaName = '';
			this.sum();
			this.emitData();
			let $this = this;
			apiPost({
				url: APIs.TRACE,
				data: {
					action: 'regions.list',
					id: provinceId
				},
				success(res){
					if(res.code + '' === '0'){
						$this.cityData = res.data;
						if($this.cityData && $this.cityData.length){
							$this.cityShow = true;
						}
						else{
							$this.closeOptions();
						}
					}
					else{
						$this.cityData = [];
					}
				},
				error(res){
					$this.cityData = [];
				},
			});
		},
		/**
		 * 展开区域
		 * <AUTHOR> <<EMAIL>>
		 * @date   2018-06-26
		 * @param  {int}   cityId    市id
		 * @param  {int}   cityIndex 市的索引
		 */
		showArea(cityId, cityIndex){
			let checkCity = this.cityData[cityIndex];
			this.checkedData.cityId = checkCity.id;
			this.checkedData.cityName = checkCity.name;
			this.checkedData.areaId = '';
			this.checkedData.areaName = '';
			this.sum();
			this.emitData();
			let $this = this;
			apiPost({
				url: APIs.TRACE,
				data: {
					action: 'regions.list',
					id: cityId
				},
				success(res){
					if(res.code + '' === '0'){
						$this.areaData = res.data;
						if($this.cityData && $this.cityData.length){
							$this.areaShow = true;
						}
						else{
							$this.closeOptions();
						}
					}
					else{
						$this.areaData = [];
					}
				},
				error(res){
					$this.areaData = [];
				},
			});
		},
		/**
		 * 点击选择区域
		 * <AUTHOR> <<EMAIL>>
		 * @date   2018-06-26
		 * @param  {int}   areaId    区域id
		 * @param  {int}   areaIndex 区域的索引
		 */
		clickArea(areaId, areaIndex){
			let checkArea = this.areaData[areaIndex];
			this.checkedData.areaId = checkArea.id;
			this.checkedData.areaName = checkArea.name;
			this.sum();
			this.closeOptions();
		},
		/**
		 * [添加全局点击事件监听]
		 * <AUTHOR> <<EMAIL>>
		 * @date   2018-06-26
		 */
		addEventListener(){
			window.addEventListener('click', this.blur);
		},
		/**
		 * [移除全局点击事件监听]
		 * <AUTHOR> <<EMAIL>>
		 * @date   2018-06-26
		 */
		removeEventListener(){
			window.removeEventListener('click', this.blur);
		},
	},
	mounted(){
		this.addEventListener();
		this.checkedData = this.initData;
		this.sum();
	},
	destroyed(){
		this.removeEventListener();
	},
};
</script>

<style lang="less">
@import (reference) '../less/common';
#areaSelect{
	.pos-r();
	input{
		.pdr(18px * @px22rem);
	}
	.area-icon{
		position: absolute;
		right: 0;
		top: 0;
		z-index: 1;
		color: #A0B0DC;
	}
	.area-select-layer{
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 3;
		background: rgba(0, 0, 0, 0.6);
	}
	.area-select-options{
		position: absolute;
		right: 0;
		bottom: 0;
		left: 0;
		display: flex;
		.bgc(#FFFFFF);
		> div{
			flex: 1;
			color: #525E6E;
			overflow-y: auto;
			height: 240px * @px22rem;
			border-right: 1px * @px22rem solid #ECEFF3;
			&:last-child{
				border-right: none;
			}
			> ul{
				list-style: none;
				.pd(0);
				.mg(0);
				> li{
					position: relative;
					height: 32px * @px22rem;
					line-height: 32px * @px22rem;
					.pdl(8px * @px22rem);
					.pdr(3px * @px22rem);
					.cs();
					&.active{
						.bgc(#F8FAFE);
					}
					> i{
						position: absolute;
						top: 0;
						right: 0;
					}
				}
			}
		}
		span{
			padding-right: 15px * @px22rem;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			display: block;
		}
	}
}
</style>
