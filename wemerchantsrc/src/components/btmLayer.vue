<template>
		<div class="btm-layer" v-if="show" @click="closeCallback">
			<!-- <div class="layer-mask"></div> -->
			<transition name="btml">
				<div class="layer-body btm-body" v-if="showBody">
					<div class="btn" v-for="row in btns" :style="{'color': row.color}" @click="row.callback" v-if="!row.noShow">{{row.name}}</div>
					<div class="cancel" @click="closeCallback">取消</div>
				</div>
			</transition>
		</div>
</template>

<script>
export default {
	name: 'btmLayer',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		/**
		 * 按钮群
		 * @type {Array}
		 */
		btns: {
			type: Array,
			default: function(){
				return [
					{
						name: '事例',
						color: '#FF4F17',
						callback: function(){
							console.log('事例');
						},
					}
				];
			},
		},
		closeCallback: {},
	},
	data(){
		return {
			showBody: false,
			cleanId: '',
		};
	},
	watch: {
		show(newValue, oldValue){
			if(newValue){
				this.cleanId = setInterval(
					() => {
						this.showBody = true;
						clearInterval(this.cleanId);
					}, 1);
			}
			else{
				this.showBody = false;
			}
		},
	},
};
</script>

<style lang="less">
@import '~less/common.less';
.btm-layer{
	position: fixed;
	top: 0;
	left: 0;
	.wh(100%, 100%);
	.btm-body{
		.bgc(#EFEFF4);
		width: 100%;
		position: absolute;
		bottom: 0;
		left: 0;
		color: #000000;
		.cancel, .btn{
			.bgc(#FFFFFF);
			border-bottom: 1px * @px22rem solid #E5E5E5;
			.wh(100%, 50px * @px22rem);
			line-height: 50px * @px22rem;
			.ta-c();
		}
		.cancel{
			.mgt(10px * @px22rem);
		}
	}
}
.btml-enter-active, .btml-leave-active{
	transition: all .3s;
}
.btml-enter{
	opacity: 0;
	transform: translate3d(0, 100%, 0);
}
.btml-leave-to{
	opacity: 0;
	transform: translate3d(0, 100%, 0);
}
.make-dpr-font(@dpr){
	[data-dpr="@{dpr}"]{
		.btm-layer .btm-body{
			.fs(18px * @dpr);
		}
	}
}
.make-dpr-font(1);
.make-dpr-font(2);
.make-dpr-font(3);
</style>