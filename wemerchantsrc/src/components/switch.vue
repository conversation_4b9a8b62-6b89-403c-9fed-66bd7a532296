<template>
<div id="switch"
	:class="{'checked': checked, 'disabled': disabled}"
	@click="triggerCheck"
	ref="switchBtn"
	>
	<input
		type="checkbox"
		ref="input"
		@change="handleChange"
		:value="value"
		:true-value="activeValue"
		:false-value="inactiveValue"
		:disabled="disabled"
		>
	<label class="label-switch"></label>
	<span class="active-text switch-text" v-if="checked">{{activeText}}</span>
	<span class="inactive-text switch-text" v-if="!checked">{{inactiveText}}</span>
</div>
</template>
<script>
/**
 * @file app/switch.vue
 * @desc this is switch.vue
 * @version 0.1.0
 * <AUTHOR> <<EMAIL>>
 * @date 2019-07-12
 * @copyright 2019
 */
/* jshint esversion: 6 */
export default {
	data(){
		return {
			left: 0,
			largeLeft: 0,
		};
	},
	model: {
		prop: 'checked',
		event: 'change',
	},
	props: {
		id: {
			type: [ String, Function ],
			required: true,
		},
		disabled: {
			type: [ Boolean ],
			default: false,
		},
		activeText: {
			type: [ String ],
			default: '',
		},
		inactiveText: {
			type: [ String ],
			default: '',
		},
		activeValue: {
			type: [ Number, String, Boolean ],
			default: true,
		},
		inactiveValue: {
			type: [ Number, String, Boolean ],
			default: false,
		},
		checked: {
			type: [ String, Number, Boolean ],
			default: false,
		},
		value: {
			type: [ String, Number, Boolean ],
			default: false,
		},
		name: {
			type: [ String ],
			default: '',
		},
		byInterface: { // 通过接口返回值来控制是否滑动开关
			type: Boolean,
			default: false,
		}
	},
	computed: {
		checkedVal(){
			return this.value === this.activeValue;
		},
	},
	methods: {
		triggerCheck(){
			if(this.disabled){
				return;
			}
			if(this.byInterface){
				this.$emit('change', this.checked !== true, this.name);
			}
			else{
				this.$refs.input.click();
			}
		},
		handleChange(event){
			this.$emit('change', this.checked !== true, this.name);
			this.$emit('input', this.checked !== true, this.name);
			this.$nextTick(() => {
				this.$refs.input.checked = this.checked;
			});
		},
	},
	watch: {
		checked(val, oldVal){
			this.$refs.input.checked = this.checked;
			if(this.checked){
				this.left = this.largeLeft;
			}
			else{
				this.left = 0;
			}
		},
	},
	created(){
	},
	mounted(){
		this.$refs.input.checked = this.checked;
	},
};
</script>

<style lang="less" scoped>
@import (reference) '~less/base/utils';
@px22rem: 2rem / 75px;
#switch{
	.pos-r();
	.dib();
	.wh(50px * @px22rem, 28px * @px22rem);
	.bgc(#AEB5BF);
	.brdr(25px * @px22rem);
	transition: all 300ms ease;
	user-select: none;
	&.checked{
		.bgc(#F7B32D);
	}
	&.disabled{
		.bgc(#ECEFF3);
		cursor: not-allowed;
		>label.label-switch{
			cursor: not-allowed;
		}
	}
	&.disabled.checked{
		.bgc(#B2D4FF);
	}
	.switch-text{
		position: absolute;
		left: 0;
		right: 0;
		.dib();
		.fc(12px * @px22rem, #fff);
		.ta-c();
		line-height: 18px * @px22rem;
	}
	> .active-text{
		padding-right: 15px * @px22rem;
	}
	> .inactive-text{
		padding-left: 12px * @px22rem;
	}
	>input[type=checkbox]{
		opacity: 0;
		.pos-a(-1; 0; 0);
	}
	>input[type=checkbox]:checked{
		+label.label-switch{
			left: 25px * @px22rem;
		}
	}
	>label.label-switch{
		position: absolute;
		top: 2px * @px22rem;
		left: 1px * @px22rem;
		.wh(24px * @px22rem, 24px * @px22rem);
		.brdr(25px * @px22rem);
		.bgc(#fff);
		box-shadow: 1px 0px 6px -2px #949494;
		transition: all 300ms ease;
	}
	// .checked{
	// 	left: 24px * @px22rem;
	// }
	// .checked{
	// 	left: 0;
	// }
}
</style>
