<template>
	<div class="wrapperDate" id="wrapper" :class="{'cal-icon': showCalIcon, 'cal-white-icon': showCalIconWhite}">
		<div class="cls-def" :class="{clsdef, 'transparent': transparent}">
			<flat-pickr
				ref="dp"
				v-model="date"
				:config="_dateConfig"
				:placeholder="placeholder"
				/>
		</div>
		<div @click="clearDate" v-show="date && clear" class="clear"></div>
	</div>
</template>
<script>
/**
 * @module components/searchSelectPaging
 * @desc 速商 日期选择框
 * @version 0.0.1
 * <AUTHOR> <<EMAIL>>
 * @date 2017-12-20
 * @copyright 2017
 */
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import {getDate} from '_/utils';
import {mapActions} from 'vuex';
const zh = require('flatpickr/dist/l10n/zh.js').default.zh;
export default{
	name: 'datepicker',
	components: {
		flatPickr
	},
	props: {
		showIcon: { // 是否显示右边的日历小图标
			type: Boolean,
			default: false
		},
		iconWhite: {
			type: Boolean,
			default: false
		},
		transparent: {
			type: Boolean,
			default: true
		},
		clear: {
			type: Boolean,
			default: true
		},
		clearData: {
			type: Boolean,
			default: false
		},
		placeholder: {
			type: String,
			default: ''
		},
		classes: { // input的class
			type: String,
			default: 'datepicker'
		},
		dateConfig: {
			type: Object,
			default: function(){
				return {};
			}
		},
		clsdef: {
			type: String,
			default: ''
		},
		dpWrapper: {
			type: String,
			default: ''
		},
		initDate: {
			type: [Array, Object],
			default: () => {
				return [];
			}
		},
		name: { // input的class
			type: String,
			default: 'date'
		},
		mode: {
			type: String,
			default: 'single'
		},
		callback: {
		},
	},
	data(){
		return {
			date: '',
		};
	},
	watch: {
		clearData(val, oval){
			if(val){
				this.data = '';
				let date = '';
				if(this.mode === 'range'){
					date = {
						start: '',
						end: '',
					};
				}
				else if(this.mode === 'single'){
					date = '';
				}
				if(typeof this.callback === 'function'){
					this.callback(date, this.name);
				}
			}
		},
	},
	computed: {
		showCalIcon(){
			return this.showIcon && !(this.date && this.clear);
		},
		showCalIconWhite(){
			return this.showIcon && this.iconWhite && !(this.date && this.clear);
		},
		_dateConfig(){
			let ua = navigator.userAgent.toLowerCase();
			let isAndroid = ua.indexOf('android') > -1; // && ua.indexOf("mobile");
			let disableMobile = !(!!navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform)) && !(isAndroid); // 鉴于flatpickr在移动设备上不稳定 统一启用系统默认picker 不是ios&android 就判定为电脑 为了调试方便
			// let disableMobile = false;
			let config = {
				maxDate: 'today',
				dateFormat: 'Y-m-d',
				disableMobile: disableMobile,
				locale: zh,
				mode: this.mode,
				defaultDate: this.initDate,
				onClose: (el) => {
					if(!el || !el.length){
						return;
					}
					let date = '';
					if(this.mode === 'range'){
						date = {
							start: getDate(el[0]),
							end: getDate(el[1]),
						};
					}
					else if(this.mode === 'single'){
						date = getDate(el[0]);
						// this.date = getDate(el[0]);
					}
					if(typeof this.callback === 'function'){
						this.callback(date, this.name);
					}
				}
			};
			if(this.dateConfig){
				for(let key in this.dateConfig){
					config[key] = this.dateConfig[key];
				}
			}
			return config;
		}
	},
	created(){
		let ua = navigator.userAgent.toLowerCase();
		let isAndroid = ua.indexOf('android') > -1;
		if(!isAndroid){
			let dpr = document.getElementsByTagName('html')[0].getAttribute('data-dpr');
			document.getElementsByTagName('html')[0].className += (' zoom' + dpr);
		}
	},
	methods: {
		...mapActions(['setAlert']),
		clearDate(){
			if(this.date){
				this.date = '';
			}
			if(typeof this.callback === 'function'){
				this.callback('', this.name);
			}
		},
	}
};
</script>
<style lang="less">
@import '../less/common';
.flatpickr-calendar {
	left: 0 !important;
}

.cal-icon{
	background: url('/images/wemerchant/icon_calendar.png') right center / 18px * @px22rem 18px * @px22rem no-repeat;
}
.cal-white-icon{
	background-image: image-set('/images/wemerchant/calendar.png' 1x);
	background-image: image-set('/images/wemerchant/<EMAIL>' 2x);
	background-image: image-set('/images/wemerchant/<EMAIL>' 3x);
	background-repeat: no-repeat;
	background-size: 18px * @px22rem 18px * @px22rem;
	background-position: right center;
}
.no-bg {
	background: none;
}
.wrapperDate{
	.of(hidden);
	.pos-a();
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 5;
}
.cls-def{
	.pos-atr(10, 0, 0);
	bottom: 0;
	left: 0;
	.input{
		.wh(100%, 100%);
		min-width: 100%;
		.pos-a();
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		.bgc(transparent);
		border: none;
		z-index: 9;
	}
	input[type="date"]::-webkit-inner-spin-button,
	input[type="date"]::-webkit-calendar-picker-indicator {
		display: none !important;
		-webkit-appearance: none !important;
	}
}
.clear{
	.wh(40px * @px2rem, 40px * @px2rem);
	position: absolute;
	top: 0;
	bottom: 0;
	.mg(auto);
	right: 0;
	background-image: image-set('/images/wemerchant/search_delete@3x_trans.png' 1x);
	background-repeat: no-repeat;
	background-size: 100%;
	.cs();
	z-index: 100;
}
.datepicker{
	.fc(14px * @px22rem, #888);
}
.transparent{
	opacity: 0;
}
// 解决type为range时，弹出的窗口在dpr大于1的设备上会缩小的问题。
.zoom1{
	.flatpickr-calendar{
		// zoom: 1;
		transform-origin:0 0;
		transform: scale(2,2);
	}
}
.zoom2{
	.flatpickr-calendar{
		transform-origin:0 0;
		transform: scale(2,2);
		// zoom: 2;
	}
}
.zoom3{
	.flatpickr-calendar{
		transform-origin:0 0;
		transform: scale(3,3);
		// zoom: 3;
	}
}
</style>
