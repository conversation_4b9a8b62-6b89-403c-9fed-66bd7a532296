<template>
	<div id="app">
		<div v-wechat-title="$route.meta.title"></div>
		<alert></alert>
		<keep-alive><router-view class="router-view" v-if="$route.meta.keepAlive"></router-view></keep-alive>
		<router-view class="router-view" v-if="!$route.meta.keepAlive"></router-view>
	</div>
</template>

<script>
import alert from '@@/alert';
export default {
	name: 'app',
	components: { alert },
	created(){
		document.body.addEventListener('focusout', () => {
			let ua = window.navigator.userAgent;
			if(ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)){
				let currentPosition;
				let timer;
				let speed = 1;
				timer = setInterval(function(){
					currentPosition = document.documentElement.scrollTop || document.body.scrollTop;
					currentPosition -= speed;
					window.scrollTo(0, currentPosition); // 页面向上滚动
					currentPosition += speed;
					window.scrollTo(0, currentPosition); // 页面向下滚动
					clearInterval(timer);
				}, 100);
			}
		});
	}
};
</script>

<style lang="less" scoped>
@import 'less/base/utils';
@px2rem: 1rem / 75px;
*{
	box-sizing: border-box;
}

html, body{
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	height: 100%;
	min-height: 100%;
	.bgc(#EFEFF4);
}
#app{
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	height: 100%;
	min-height: 100%;
	.pos-r();
}
.fade-enter-active, .fade-leave-active{
	transition: opacity .3s;
}
.fade-enter, .fade-leave-active{
	opacity: 0;
}
</style>
