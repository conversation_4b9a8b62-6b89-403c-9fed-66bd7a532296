/**
 * @file utils.js
 * @desc 工具函数库
 * @version 0.0.1
 * <AUTHOR> <ya<PERSON><PERSON><PERSON>@comteck.cn>
 * @date 2017-09-27
 * @copyright 2017
 */
/* jshint esversion: 6 */
import Vue from 'vue';
import store from '../vuex';
// import router from '@/router';
// import store from '../vuex/index';
// import * as CONST from '../modules/CONSTs';
// import * as URL from '../modules/URLs';
export const apiPost = function(obj){
	let params = {
		url: '',
		data: {},
		success(data){},
		error(data){},
	};
	for(let key in obj){
		if(obj[key]){
			params[key] = obj[key];
		}
	}
	if(store.state.token){
		params.data.token = store.state.token;
	}
	// params.data.token = 'b32dcefafb9754f241c0e2263f7d556d';
	setTimeout(() => {
		Vue.http.post(params.url, params.data)
		.then(res => {
			// todo 有部分接口无论授权是否成功 都会返回未授权，暂时先把跳转login的操作代码隐藏
			/*
			if(res.data.code === CONST.CODE_NOLOGIN){
				// 没有登录
				// todo 清空用户所有的信息
				store.dispatch('clearUserInfo');
				router.push(URL.LOGIN);
			}
			else{
				params.success(res.data);
			}
			*/
			params.success(res.data);
		}, res => {
			debugger;
			params.error(res.data);
		});
	}, 1);
};
/**
 * 表格的高度自适应
 * <AUTHOR> <<EMAIL>>
 * @date   2017-09-27
 * @param  {Number}   clHeight  盒子高度
 * @param  {Number}   rmHeight  盒子除表格的剩余高度
 * @param  {Number}   showTrNum 表格的行数
 */
export const tableResize = function(clHeight, rmHeight, showTrNum){
	if(showTrNum === 0){
		showTrNum = 1;
	}
	let ctHeight = clHeight - rmHeight;
	let allTrHeight = showTrNum * 41;
	let tbHeight = ctHeight;
	if(allTrHeight < ctHeight){
		tbHeight = allTrHeight;
	}
	return tbHeight;
};
/**
 * 数据重构
 * <AUTHOR> <<EMAIL>>
 * @date   2017-09-30
 * @param  {Array}   vueData 原数据
 */
export const parse = function(vueData){
	return JSON.parse(JSON.stringify(vueData));
};
/**
 * 个位数前加0
 * <AUTHOR> <<EMAIL>>
 * @date   2017-09-30
 * @param  {String | Number}   num 数据
 * @param  {Number}   bit 几位
 */
export const fillZero = function(num, bit = 0){
	let str = num.toString();
	if(str.length < bit){
		let n = bit - str.length;
		while(n--){
			str = '0' + str;
		}
	}
	return str;
};

/**
 * 获取格式化的时间
 * <AUTHOR> <<EMAIL>>
 * @date   2017-09-30
 * @param  {Date}     time 时间
 */
export const getDate = function(time = new Date().getTime()){
	let date = new Date(time);
	let formatDate = `${date.getFullYear()}-${fillZero(date.getMonth() + 1, 2)}-${fillZero(date.getDate(), 2)}`;
	return formatDate;
};
// 当前日期前多少天
export const dayToDate = function(n){
	let now = (new Date()).getTime();
	let date = now - 1000 * 60 * 60 * 24 * Number(n);
	return getDate(new Date(date));
};

export const compareTime = (date1, date2) => {
	date1 = date1.replace(/-/gi, '/');
	date2 = date2.replace(/-/gi, '/');
	let time1 = new Date(date1).getTime();
	let time2 = new Date(date2).getTime();
	if(time1 > time2){
		return 1;
	}
	else if(time1 === time2){
		return 2;
	}
	else{
		return 3;
	}
};

/**
 * 浏览器本地暂存加载失败时显示的数据
 * <AUTHOR> <<EMAIL>>
 * @date   2017-09-30
 * @param  {Object}   pageInfo 页面的数据
 */
export const localSetItemFail = function(pageInfo){
	localStorage.setItem('loadFailTitles', JSON.stringify(pageInfo.titles));
	localStorage.setItem('loadFailBackBtn', pageInfo.backBtn);
	localStorage.setItem('loadFailBackPath', pageInfo.backPath);
	localStorage.setItem('loadFailToPath', pageInfo.toPath);
};

/**
 * 手机号格式、号段（运营商）校验
 * <AUTHOR> <<EMAIL>>
 * @date   2017-10-07
 * @param {string|int} telphone 待校验手机号
 * @return {returnObject}
 */
export const checkMobile = function(telphone){
	let isChinaMobile	=	/^134[0-8]\d{7}$|^(?:13[5-9]|14[78]|15[0-27-9]|178|18[2-478]|198)\d{8}$/; // update 198/148 @date 2017-08-08
	let isChinaUnion	=	/^(?:13[0-2]|14[56]|15[56]|166|176|18[56])\d{8}$/; // update 166/146 @date 2017-08-08
	// ^1349\d{7}$ 1349号段 电信方面没给出答复，视作不存在
	// 2017-04-24 追加 173号段 from 测试反馈
	let isChinaTelcom	=	/^(?:133|153|17[37]|18[019]|199)\d{8}$/; // update 199/1740/1741 @date 2017-08-08
	let isOtherTelphone	=	/^(?:170([059])\d{7}|(?:174[01])\d{8}|144\d{10}|141\d{10})$/; // 1740、1741电信和工信部的卫星通信号段 144 十三位移动物联网 / 141 十三位电信物联网
	// telphone = this.trim(telphone);
	if(telphone.length !== 11){
		return false;
	}
	else{
		if(isChinaMobile.test(telphone)){
			return true;
		}
		else if(isChinaUnion.test(telphone)){
			return true;
		}
		else if(isChinaTelcom.test(telphone)){
			return true;
		}
		else if(isOtherTelphone.test(telphone)){
			return true;
		}
		else{
			return false;
		}
	}
};

/**
 * 截取时间戳 只取日期串
 * <AUTHOR> Jie <<EMAIL>>
 * @date    2017-12-20
 * @param	{string}	str 2017-12-11 12:12:12
 * @return	{string}	str	2017-12-11
 */
export const handleDate = function(str){
	if(!str){
		return '';
	}
	if(str.length <= 10){
		return str;
	}
	return str.substr(0, 10);
};

/**
 * 转换数字位2位小数
 * <AUTHOR> Jie <<EMAIL>>
 * @date    2017-12-20
 */
export const returnFixed2 = function(str){
	str = parseFloat(str);
	if(isNaN(str)){
		return '0.00';
	}
	return str.toFixed(2);
};

/**
 * 账单状态转换
 * <AUTHOR> Jie <<EMAIL>>
 * @date    2017-12-22
 * @param	{Integer}	status
 * @return	账单状态：1未完成收款 2完成收款 3欠费状态 4坏账 5未完成填写（抄表类型只有先完成填写才能到 2,4状态，其他类型没有完成填写状态）
 */
export const returnBillType = function(status){
	switch(status){
		case 1:
			return '未缴费';
		case 2:
			return '已缴费';
		case 3:
			return '欠费状态';
		case 4:
			return '坏账';
		case 5:
			return '未完成填写';
		default:
			return '';
	}
};

/**
 * 检测图片url，是否有效
 * <AUTHOR> Jie <<EMAIL>>
 * @date    2018-01-09
 * @param	{String}	url			图片地址
 * @param	{Function}	callback	图片获取回调
 * @param	{Number}	timeout		图片获取最长时间毫秒数
 */
export const testImage = function(url, callback, timeout){
	timeout = timeout || 2000;
	let timedOut = false;
	let timer;
	let img = new Image();
	img.onerror = img.onabort = function(){
		if(!timedOut){
			clearTimeout(timer);
			callback('error');
		}
	};
	img.onload = function(){
		if(!timedOut){
			clearTimeout(timer);
			callback('success');
		}
	};
	img.src = url;
	timer = setTimeout(function(){
		timedOut = true;
		// reset .src to invalid URL so it stops previous
		// loading, but doesn't trigger new load
		callback('timeout');
	}, timeout);
};
/**
 * 微信支付的方法校验
 * <AUTHOR> <<EMAIL>>
 * @date   2017-11-30
 * @param  {Function}   onBridgeReady 支付方法
 */
export const wxPay = function(onBridgeReady){
	if(typeof WeixinJSBridge === 'undefined'){
		if(document.addEventListener){
			document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);
		}
		else if(document.attachEvent){
			document.attachEvent('WeixinJSBridgeReady', onBridgeReady);
			document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);
		}
	}
	else{
		onBridgeReady();
	}
};

/**
 * 增加千分位
 * <AUTHOR> <<EMAIL>>
 * @date   2018-05-21
 * @param  {String}   str
 * @return {String}
 */
const addThousandth = str => {
	let len = 0;
	if(str.length % 3 !== 0){
		len = 3 - str.length % 3;
		str = new Array(len + 1).join('0') + str;
	}
	let ret = [];
	let index = 0;
	while(index < str.length){
		ret.push(str.substr(index, 3));
		index += 3;
	}
	ret = ret.join(',').split('');
	while(len--){
		ret.shift();
	}
	return ret.join('');
};

/**
 * 格式化数字/数字格式的字符串为带千分位和两位小数的字符串
 * <AUTHOR> <<EMAIL>>
 * @date   2018-05-21
 * @param  {Number|String} number 待格式化数值
 * @param  {Number}        bit    小数位数，默认两位小数
 * @return {String}
 */
export const anf = (number, bit = 2) => {
	let flag = '';
	if(number === null || number === undefined){
		number = 0.0;
	}
	if(typeof number === 'string'){
		number = parseFloat(number);
	}
	if(isNaN(number)){
		number = 0.0;
	}
	if(number < 0){
		number = Math.abs(number);
		flag = '-';
	}
	number = number.toFixed(bit);
	number = number.split('.').map((el, index) => {
		if(index === 0){
			return addThousandth(el);
		}
		else{
			return el;
		}
	}).join('.');
	return `${flag}${number}`;
};
