/**
 * 车辆管理相关API
 */
import { apiPost } from '_/utils'
import APIs from '_/APIs'

/**
 * 添加车辆
 * @param {Object} data - 车辆信息
 */
export function addCar(data) {
  return apiPost({
    url: APIs.LEAGUE,
    data: {
      action: 'add.car',
      ...data
    },
    success: function(res){
    	console.log("添加车辆成功")
    	console.log(res)
    },
    error: function(res){
    	console.log("添加车辆失败")
    	console.log(res)
    }
  })
}

/**
 * 上传车辆照片
 * @param {File} file - 照片文件
 */
export function uploadCarPhoto(file) {
  const formData = new FormData()
  formData.append('file', file)
  return apiPost({
    url: APIs.UPLOAD,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 搜索品牌型号
 * @param {string} keyword - 搜索关键字
 */
export function searchBrandModel(keyword,func) {
	console.log("keyword为："+keyword)
	var result;
	apiPost({
		url: APIs.DEAL,
		data: {
			action: 'search.brand',
			brand: encodeURIComponent(keyword)
		},success: function(res){
			func(res);
		},error: function(res){
			func("error");
		}

	})
	return result;

}

/**
 * 获取车辆选项数据
 * @param {string} type - 选项类型：level/color/fuelType/usage/maintenance
 */
export function getCarOptions(type) {
  return apiPost({
    url: APIs.GETALLCARD,
    data: {
      action: 'getoptions',
      type
    }
  })
}
