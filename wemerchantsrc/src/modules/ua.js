/**
 * 获取userAgent 平台以及微信版本号
 * <AUTHOR> <<EMAIL>>
 * @date   2018-12-20
 */
const OS_NAME_PATTERN = /(Android|iPhone|iPad|iPod)/i;
const OS_VERSION_PATTERN = /Android[\s/]([\d.]+)|OS ([\d_.]+) like Mac OS X/i;

export let UA = typeof window !== 'undefined' ? window.navigator.userAgent : '';

export let getOSName = () => {
	let matched = UA.match(OS_NAME_PATTERN);
	return matched ? matched[1] : 'unknow';
};

export let isIOS = () => {
	let osName = getOSName();
	return osName === 'iPhone' || osName === 'iPod' || osName === 'iPad';
};

export let getOSVersion = () => {
	let matched = UA.match(OS_VERSION_PATTERN);
	let version = matched ? (matched[1] || matched[2]) : '0.0.0';
	return version.replace(/_/g, '.');
};
