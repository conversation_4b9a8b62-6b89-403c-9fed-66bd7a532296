/**
 * 自定义指令的 mixin
 * <AUTHOR> <<EMAIL>>
 * @date 2017-07-29
 * @copyright 2017
 */

/**
 * vFocus 获得焦点
 * @type {Object}
 */
export const vFocus = {
	directives: {
		/**
		 * 焦点指令
		 * <AUTHOR> <<EMAIL>>
		 * @type {Object}
		 * @date 2017-06-14
		 */
		focus: {
			/**
			 * 值更新时候触发获得焦点
			 * <AUTHOR> <<EMAIL>>
			 * @date   2017-07-29
			 * @param  {HTMLElement} el            指令所绑定的元素
			 * @param  {Boolean}     options.value 绑定值(新)
			 */
			update(el, { value, oldValue }){
				if(value){
					el.focus();
				}
			},
		},
	},
};

/**
 * vClick 触发点击
 * @type {Object}
 */
export const vClick = {
	directives: {
		click: {
			/**
			 * 值更新时候触发点击
			 * <AUTHOR> <<EMAIL>>
			 * @date   2017-07-29
			 * @param  {HTMLElement} el                指令所绑定的元素
			 * @param  {Boolean}     options.value     绑定值(新)
			 * @param  {Boolean}     options.oldValue  绑定值(旧)
			 */
			update(el, { value, oldValue }){
				if(value && !oldValue){
					el.click();
				}
			},
		},
	},
};
