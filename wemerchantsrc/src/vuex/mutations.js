/**
 * @file vuex/mutations.js
 * @desc 速店农贸版商户助手 vuex mutations
 * @version 0.0.1
 * <AUTHOR> <<EMAIL>>
 * @date 2017-12-20
 * @copyright 2017
 */
import * as mutationTypes from './mutation-types';

let {
	SET_USER_INFO,
	CLEAR_USER_INFO,
	SET_ALERT,
	SET_ORGZ,
	SET_TOKEN,
	SET_URL,
	SET_SUPPLY,
	SET_INSPECT,
	SET_PRO_INFO,
	SET_GOODS,
	SET_COUPON,
	SET_PROS,
	SET_UUID,
	SET_POS_PRO,
	UPDATE_STEP1_DATA,
	UPDATE_STEP2_DATA,
	UPDATE_STEP3_DATA,
	CLEAR_DATA,
	CLEAR_ORGZ_INFO,
	SET_INIT_LINK,
	CLEAR_TOKEN_INFO
} = mutationTypes;

export default {
	[CLEAR_TOKEN_INFO](state) {
		state.token = '';
		state.token_time = '';
	},
	[SET_INIT_LINK](state, data) {
		state.initLink = data;
	},
	//清空数据
	[CLEAR_DATA](state) {
		const defaultData = {
				fileData: {
					buyInfos: {
						personUp: '',
						personDown: '',
						company: ''
					},
					saleInfos: {
						personUp: '',
						personDown: '',
						company: '',
						handImage: ''
					},
					carInfos: {
						xsz: '',
					},
					video: ''
				},
				carData: {
					s: {
						ywid: '',
					},
					registerType: '正式办理',
					typeR: '',
					rType: '',
					mCarId: '',
					tradeCarId: '',
					symbol: '',
					ClosedCar: '',
					carFlag: '',
					saleName: '',
					owner: '',
					address: '',
					issueDate: '',
					issuingAuthority: '',
					m: {
						plate: '',
						type: '',
						usertype: '非营运',
						brandnum: '',
						code: '',
						origin: '国产',
						enum: '',
						stime: '',
						rnum: '',
						disp: '',
						energy: '否',
						brand: '',
						color: '',
						price: '',
						mile: '',
						lpath: ''
					}
				},
				formData: {
					bsidPhoto1: '',
					bsidPhoto: '',
					ssidPhoto2: '',
					ssidPhoto: '',
					type: 'get',
					typeR: '',
					typeUp: '个人',
					typeDown: '个人',
					buytype: '',
					saletype: '',
					carid: '',
					ywid: '',
					bid: '',
					sid: '',
					mpath: '',
					spath: '',
					// buyIdImage: '',
					upIdImg: '',
					// saleIdImage: '',
					downIdImg: '',
					salehandimage: '',
					photoFiles: {},
					registerType: '正式办理',
					m: {
						name: '',
						address: '',
						phone: '',
						identtype: '身份证',
						identno: '',
						nation: '0',
						sfzzp: ''
					},
					e1: {
						name: '',
						address: '',
						identtype: '营业执照',
						identno: '',
						lpath: ''
					},
					u: {
						name: '',
						address: '',
						phone: '',
						identtype: '身份证',
						identno: '',
						nation: '0',
						sfzzp: '',
						reason: ''
					},
					e: {
						name: '',
						address: '',
						identtype: '营业执照',
						identno: '',
						lpath: ''
					},
					be1Photo: '',
					sePhoto: '',
				}
			}
		Object.assign(state, defaultData)
	},

	// 预登记第三步表单数据
	[UPDATE_STEP3_DATA](state, data) {
		Object.assign(state.fileData, data)
	},
	//预登记第二步表单数据
	[UPDATE_STEP2_DATA](state, data) {
		Object.assign(state.carData, data)

	},
	// 设置预登记第一步表单数据
	[UPDATE_STEP1_DATA](state, data) {
		Object.assign(state.formData, data)
	},
	/**
	 * 设置用户信息
	 * <AUTHOR> Jiajun <<EMAIL>>
	 * @date   2017-12-20
	 * @param  {Object} state
	 * @return {object} data 用户信息
	 */
	[SET_USER_INFO](state, data) {
		state.userInfo = data;
	},
	/**
	 * 清空用户信息
	 * <AUTHOR> Jiajun <<EMAIL>>
	 * @date   2017-12-20
	 * @param  {Object} state
	 */
	[CLEAR_USER_INFO](state) {
		state.userInfo = {
			user_id: '', // 用户id
			last_orgz_id: '', // 最后一次选中的档口id
		};
	},
	[CLEAR_ORGZ_INFO](state) {
		state.orgz = {
			name: '', // 用户id
			id: '', // 最后一次选中的档口id
		};
	},
	/**
	 * 设置错误信息
	 * <AUTHOR> Jiajun <<EMAIL>>
	 * @date   2017-12-20
	 * @param  {Object} state
	 * @param  {String} msg 错误信息
	 */
	[SET_ALERT](state, {msg, type}) {
		state.alert = {
			msg: msg,
			type: type,
		};
	},
	/**
	 * 设置错误信息
	 * <AUTHOR> Jiajun <<EMAIL>>
	 * @date   2017-12-20
	 * @param  {Object} state
	 * @param  {String} msg 错误信息
	 */
	[SET_ORGZ](state, data) {
		state.orgz = data;
	},
	/**
	 * 设置获取信息标识
	 * <AUTHOR> Jiajun <<EMAIL>>
	 * @date   2017-12-20
	 * @param  {Object} state
	 * @param  {String} msg 获取信息标识
	 */
	[SET_TOKEN](state, data) {
		state.token = data.token;
		state.token_time = data.time;

	},
	/**
	 * 设置第一次登录页面url(ios)
	 * <AUTHOR> Jiajun <<EMAIL>>
	 * @date   2018-10-19
	 * @param  {Object} state
	 * @param  {String} msg 页面url
	 */
	[SET_URL](state, data) {
		state.url = data;
	},
	/**
	 * 设置供应商数据集
	 * <AUTHOR> <<EMAIL>>
	 * @date   2019-04-22
	 */
	[SET_SUPPLY](state, data) {
		state.supply = data;
	},
	/**
	 * 设置检疫检证数据集
	 * <AUTHOR> <<EMAIL>>
	 * @date   2019-04-22
	 */
	[SET_INSPECT](state, data) {
		state.inspect = data;
	},
	/**
	 * 设置检疫检证数据集
	 * <AUTHOR> <<EMAIL>>
	 * @date   2019-04-22
	 */
	[SET_PRO_INFO](state, data) {
		state.proInfo = data;
	},
	/**
	 * 设置盘点商品
	 * <AUTHOR> <<EMAIL>>
	 * @date   2019-04-22
	 */
	[SET_GOODS](state, data) {
		state.goods = data;
	},
	/**
	 * 设置优惠券
	 * <AUTHOR> Jiajun <<EMAIL>>
	 * @date   2019-07-15
	 */
	[SET_COUPON](state, data) {
		state.coupon = data;
	},
	/**
	 * 设置要货商品
	 * <AUTHOR> <<EMAIL>>
	 * @date   2019-04-22
	 */
	[SET_PROS](state, data) {
		state.pros = data;
	},
	/**
	 * 设置uuid
	 */
	[SET_UUID](state, data) {
		state.uuid = data;
	},
	/**
	 * 设置pos热键商品
	 */
	[SET_POS_PRO](state, data) {
		state.posPro = data;
	},
};
