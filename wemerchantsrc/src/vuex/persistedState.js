export default function persistedState(keys = []) {
	return store => {
		// 初始化时从本地存储加载数据
		keys.forEach(key => {
			const savedState = localStorage.getItem(key);
			if (savedState) {
				try {
					store.replaceState({
						...store.state,
						[key]: JSON.parse(savedState)
					});
				} catch (e) {
					console.error(`解析本地存储数据 ${key} 失败`, e);
				}
			}
		});

		// 监听状态变化，实时保存到本地存储
		store.subscribe((mutation, state) => {
			keys.forEach(key => {
				if (state[key] !== undefined) {
					localStorage.setItem(key, JSON.stringify(state[key]));
				}
			});
		});
	};
}
