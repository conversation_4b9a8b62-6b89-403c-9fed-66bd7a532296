<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
<head>
	<title>通汇天辰</title>
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<script src="/lib/lib-flexible/flexible.js"></script>
	<script src="/lib/lib-flexible/flexible_css.js"></script>
	<script src="/lib/lrz/dist/lrz.all.bundle.js"></script>
	<script src="/lib/lib-flexible/jquery-1.9.1.js"></script>
<!--	<script src="/lib/lib-flexible/select2.js"></script>-->
	<script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>

	<script src="/lib/lib-flexible/hui.js"></script>
	<script src="/lib/lib-flexible/hui-tab.js"></script>
	<script src="/lib/lib-flexible/hui-refresh-load-more.js"></script>
	<script src="/lib/lib-flexible/hui-select-beautify.js"></script>
	<script src="/lib/lib-flexible/common.js"></script>
<!--	<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.8/css/select2.min.css"-->
<!--		  rel="stylesheet"/>-->
	<link rel="stylesheet" href="/lib/lib-flexible/index.css">
<!--	<link rel="stylesheet" href="/lib/lib-flexible/select2.css">-->

	<link rel="stylesheet" href="/lib/lib-flexible/hui.css">
	<link rel="stylesheet" href="/lib/lib-flexible/footer.css">
	<% for (var css in htmlWebpackPlugin.files.css) { %>
	<link rel="stylesheet" href="<%= htmlWebpackPlugin.files.css[css] %>">
	<% } %>

	<% for (var bundle in htmlWebpackPlugin.options.bundleConfig) { %>
	<script src="/<%= htmlWebpackPlugin.options.bundleConfig[bundle].js %>"></script>
	<% } %>
<!--	<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.8/js/select2.min.js"></script>-->

	<script>
		var BASE_URL = '/';
		// var IDENTIFICATION = '194e42e14a6606e33f6a2c780cc11345';
		var IDENTIFICATION = '<?php echo isset($identification) ? $identification : ""?>';
		var STALLSINFO = '<?php echo isset($stall_info) ? $stall_info : ""?>';
	</script>
</head>
<body>
<div id="app"></div>
<!-- built files will be auto injected -->
<% for (var chunk in htmlWebpackPlugin.files.chunks) { %>
<script src="<%= htmlWebpackPlugin.files.chunks[chunk].entry %>"></script>
<% } %>

</body>
</html>
